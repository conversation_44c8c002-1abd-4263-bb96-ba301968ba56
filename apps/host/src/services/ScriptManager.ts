import { MMKV } from 'react-native-mmkv';
import { ScriptManager } from '@callstack/repack/client';

const storage = new MMKV({ id: 'script-storage' });

export class ScriptManagerWithMMKV {
  static init() {
    if (__DEV__) {
      ScriptManager.shared.on('loading', (script) => {});

      ScriptManager.shared.on('loaded', (script) => {});

      ScriptManager.shared.on('error', (error) => {
        console.error('Script loading failed:', error);
      });
    }

    ScriptManager.shared.setStorage({
      async getItem(key) {
        const value = storage.getString(key);
        return value ?? null;
      },
      async setItem(key, value) {
        storage.set(key, value);
      },
      async removeItem(key) {
        storage.delete(key);
      },
    });
  }

  // Clear cache
  static clearCache(scriptId?: string) {
    if (scriptId) {
      storage.delete(scriptId);
    } else {
      storage.clearAll();
    }
  }
}
