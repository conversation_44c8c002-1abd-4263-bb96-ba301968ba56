import { useEffect, useState } from 'react';
import { AppConfig, useAppStore, useQuery } from '@btaskee/design-system';
import axios from 'axios';

export const useMaintain = () => {
  const [isMaintainApp, setIsMaintainApp] = useState(false);
  const { isoCode } = useAppStore();

  const { API_MAINTAIN_URL } = AppConfig.get();

  const { data, refetch, isSuccess } = useQuery({
    queryKey: [API_MAINTAIN_URL],
    queryFn: () => {
      return axios.post(API_MAINTAIN_URL!, { isoCode });
    },
    enabled: !!API_MAINTAIN_URL,
    refetchInterval: (query) => {
      return query.state.data?.data?.isAllSystem ? 30000 : false;
    },
    retry: true,
  });

  useEffect(() => {
    if (isSuccess) {
      setIsMaintainApp(!!data?.data?.isAllSystem);
    }
  }, [data?.data?.isAllSystem, isSuccess]);

  return { setIsMaintainApp, refetch, isMaintainApp };
};
