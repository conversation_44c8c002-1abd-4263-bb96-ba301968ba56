import React, { useEffect } from 'react';
import { Platform } from 'react-native';
import {
  <PERSON><PERSON>,
  BlockView,
  CText,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  EndpointKeys,
  getVersionAppName,
  IconImage,
  Spacing,
  useApiQuery,
  useI18n,
} from '@btaskee/design-system';

import { icUpdateAppVersion } from '@images';

export const useCheckVersionApp = () => {
  const { t } = useI18n('host');
  const { data, refetch } = useApiQuery({
    key: EndpointKeys.checkCompareVersionApp,
    params: {
      version: getVersionAppName(),
      platform: Platform.OS,
    },
    options: {
      retry: true,
      retryOnMount: true,
    },
  });

  useEffect(() => {
    if (data?.isShow && !data.isForce) {
      Alert.alert.open({
        title: 'UPDATE_APP_VERSION.TITLE',
        message: (
          <BlockView
            center
            margin={{ top: Spacing.SPACE_28 }}
          >
            <IconImage
              source={icUpdateAppVersion}
              size={DeviceHelper.WINDOW.WIDTH / 2}
            />
            <CText
              testID="screenNewApp"
              margin={{
                horizontal: Spacing.SPACE_16,
                top: Spacing.SPACE_24,
              }}
              center
            >
              {t('UPDATE_APP_VERSION.UPDATE_REQUIRE')}
            </CText>
          </BlockView>
        ),
      });
    }
  }, [data?.isForce, data?.isShow, t]);

  return { ...data, refetch };
};
