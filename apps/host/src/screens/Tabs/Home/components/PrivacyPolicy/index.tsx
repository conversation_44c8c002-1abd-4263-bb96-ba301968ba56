import React from 'react';
import {
  BlockView,
  CText,
  FastImage,
  getIsoCodeGlobal,
  getTextWithLocale,
  ISO_CODE,
  LOCALES,
  openUrl,
  TouchableOpacity,
  useI18n,
} from '@btaskee/design-system';

import { imgPrivacyPolicy } from '@images';

import { styles } from './styles';

export const getLinksBtaskee = () => {
  const createLocaleLinks = (
    defaultValue: string,
    data?: Partial<Record<LOCALES, string>>,
  ) =>
    Object.values(LOCALES).reduce((acc, locale) => {
      acc[locale] = data?.[locale] || defaultValue || '';
      return acc;
    }, {} as Record<LOCALES, string>);

  const linkOfBtaskeeByCountry = {
    [ISO_CODE.VN]: {
      term: createLocaleLinks('https://www.btaskee.com/en/terms/', {
        [LOCALES.vi]: 'https://www.btaskee.com/dieu-khoan-su-dung/',
      }),
      policy: createLocaleLinks('https://www.btaskee.com/en/privacy/', {
        [LOCALES.vi]: 'https://www.btaskee.com/chinh-sach-bao-mat/',
      }),
    },
    [ISO_CODE.TH]: {
      term: createLocaleLinks('https://www.btaskee.com/th/terms-and-condition'),
      policy: createLocaleLinks(
        'https://www.btaskee.com/th/privacy-policy-th/',
      ),
    },
    [ISO_CODE.ID]: {
      term: createLocaleLinks('https://www.btaskee.com/id/terms-indonesia/'),
      policy: createLocaleLinks(
        'https://www.btaskee.com/id/privacy-indonesia/',
      ),
    },
    [ISO_CODE.MY]: {
      term: createLocaleLinks(
        'https://www.btaskee.com/my/terms-and-condition-my/',
      ),
      policy: createLocaleLinks(
        'https://www.btaskee.com/my/privacy-policy-my/',
      ),
    },
  };

  const isoCode = getIsoCodeGlobal();

  return linkOfBtaskeeByCountry[isoCode as ISO_CODE];
};

export const PrivacyPolicy = () => {
  const { t } = useI18n('host');

  const link = getLinksBtaskee();

  const onSeeMore = () => {
    openUrl(getTextWithLocale(link.policy));
  };

  return (
    <BlockView style={styles.container}>
      <BlockView center>
        <FastImage
          resizeMode={'cover'}
          source={imgPrivacyPolicy}
          style={styles.imgPrivacyPolicy}
        />
        <CText style={styles.txtPrivacyPolicy}>
          {t('PRIVACY_POLICY.CONTENT_POLICY')}
        </CText>
      </BlockView>
      <BlockView style={styles.boxSeeMore}>
        <TouchableOpacity onPress={onSeeMore}>
          <CText style={styles.txtSeeMore}>{t('VIEW_MORE')}</CText>
        </TouchableOpacity>
      </BlockView>
    </BlockView>
  );
};
