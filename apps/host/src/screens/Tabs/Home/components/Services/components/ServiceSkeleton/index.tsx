import React from 'react';
import { BlockView, SkeletonBox } from '@btaskee/design-system';

import { styles } from './styles';

interface ServiceSkeletonProps {
  widthItem: number;
}

export const ServiceSkeleton: React.FC<ServiceSkeletonProps> = ({
  widthItem,
}) => {
  const SIZE_ICON = widthItem * 0.6;

  return (
    <BlockView style={[styles.container, { width: widthItem }]}>
      {/* Service Icon Skeleton */}
      <SkeletonBox
        width={SIZE_ICON}
        height={SIZE_ICON}
        style={[
          styles.iconSkeleton,
          { borderRadius: Math.round(SIZE_ICON / 4) },
        ]}
      />

      {/* Service Name Container */}
      <BlockView style={styles.serviceNameContainer}>
        {/* Service Name Skeleton */}
        <SkeletonBox
          width={widthItem * 0.6}
          height={12}
          style={styles.serviceNameSkeleton}
        />

        {/* Optional subtitle skeleton (for subscription services) */}
        <SkeletonBox
          width={widthItem * 0.7}
          height={12}
          style={styles.subtitleSkeleton}
        />
      </BlockView>
    </BlockView>
  );
};
