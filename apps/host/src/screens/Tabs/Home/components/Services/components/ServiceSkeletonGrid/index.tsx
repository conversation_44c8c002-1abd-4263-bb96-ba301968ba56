import React from 'react';
import { BlockView, SkeletonBox } from '@btaskee/design-system';

import { ServiceSkeleton } from '../ServiceSkeleton';
import { styles } from './styles';

interface ServiceSkeletonGridProps {
  widthItem: number;
  count?: number;
}

export const ServiceSkeletonGrid: React.FC<ServiceSkeletonGridProps> = ({
  widthItem,
  count = 8, // Default to 8 skeletons (2 rows of 4)
}) => {
  // Generate array of skeleton items
  const skeletonItems = Array.from({ length: count }, (_, index) => (
    <ServiceSkeleton
      key={`skeleton-${index}`}
      widthItem={widthItem}
    />
  ));

  return (
    <BlockView>
      <SkeletonBox
        width={100}
        height={20}
        style={styles.titleSkeleton}
      />
      <BlockView style={styles.container}>{skeletonItems}</BlockView>
    </BlockView>
  );
};
