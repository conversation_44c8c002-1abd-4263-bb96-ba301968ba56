import { StyleSheet } from 'react-native';
import { BorderRadius, Colors, Spacing } from '@btaskee/design-system';

export const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    marginVertical: Spacing.SPACE_12,
  },
  serviceNameContainer: {
    flex: 1,
    marginTop: Spacing.SPACE_08, // Using hardcoded value as SPACE_06 doesn't exist
    paddingHorizontal: 4, // Using hardcoded value as SPACE_04 doesn't exist
    width: '100%',
    alignItems: 'center',
  },
  serviceNameSkeleton: {
    marginBottom: Spacing.SPACE_04,
    borderRadius: BorderRadius.RADIUS_04,
  },
  subtitleSkeleton: {
    borderRadius: BorderRadius.RADIUS_04,
  },
  iconSkeleton: {
    backgroundColor: Colors.neutral100,
  },
});
