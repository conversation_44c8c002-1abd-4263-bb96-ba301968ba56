import { StyleSheet } from 'react-native';
import { DeviceHelper, Spacing } from '@btaskee/design-system';

export const styles = StyleSheet.create({
  container: {
    paddingHorizontal: Spacing.SPACE_16,
    paddingVertical: Spacing.SPACE_24,
  },

  errorImage: {
    width: 200,
    height: 200,
  },

  contentContainer: {
    marginBottom: Spacing.SPACE_32,
    paddingHorizontal: Spacing.SPACE_16,
  },

  errorTitle: {
    marginBottom: Spacing.SPACE_08,
    textAlign: 'center',
  },

  errorMessage: {
    textAlign: 'center',
    lineHeight: 20,
  },

  retryButton: {
    minWidth: DeviceHelper.WINDOW.WIDTH * 0.5,
    alignSelf: 'center',
  },
});
