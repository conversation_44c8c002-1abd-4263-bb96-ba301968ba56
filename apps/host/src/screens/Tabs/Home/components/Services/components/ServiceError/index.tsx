/**
 * ServiceError Component
 *
 * Displays error state for Services component with retry functionality
 * Uses design system components and ColorsV2 tokens following super-app patterns
 */
import React from 'react';
import {
  BlockView,
  Colors,
  CText,
  FastImage,
  FontSizes,
  img404error,
  PrimaryButton,
  useI18n,
} from '@btaskee/design-system';

import { styles } from './styles';

/**
 * Props interface for ServiceError component
 */
interface ServiceErrorProps {
  /**
   * Function to call when retry button is pressed
   */
  onRetry: () => void;

  /**
   * Whether the retry operation is currently loading
   */
  isRetrying?: boolean;

  /**
   * Optional custom error message to display
   */
  errorMessage?: string;

  /**
   * Test ID for accessibility and testing
   */
  testID?: string;
}

/**
 * ServiceError component for displaying API error states with retry functionality
 *
 * @param props - ServiceError component props
 * @returns JSX.Element
 */
export const ServiceError: React.FC<ServiceErrorProps> = ({
  onRetry,
  isRetrying = false,
  errorMessage,
  testID = 'service-error',
}) => {
  const { t } = useI18n('common');

  /**
   * Handle retry button press
   */
  const handleRetry = () => {
    if (!isRetrying) {
      onRetry();
    }
  };

  return (
    <BlockView
      flex
      center
      backgroundColor={Colors.neutralWhite}
      style={styles.container}
      testID={testID}
    >
      <FastImage
        source={img404error}
        style={styles.errorImage}
        testID={`${testID}-image`}
      />

      <BlockView
        center
        style={styles.contentContainer}
      >
        <CText
          bold
          size={FontSizes.SIZE_16}
          color={Colors.neutral800}
          style={styles.errorTitle}
          testID={`${testID}-title`}
        >
          {t('ERROR_SETTING')}
        </CText>

        <CText
          size={FontSizes.SIZE_14}
          color={Colors.neutral500}
          center
          style={styles.errorMessage}
          testID={`${testID}-message`}
        >
          {errorMessage || t('ERROR_SETTING_RELOAD')}
        </CText>
      </BlockView>

      <PrimaryButton
        title={isRetrying ? t('LOADING') : t('ERROR_SETTING_BUTTON_RELOAD')}
        onPress={handleRetry}
        disabled={isRetrying}
        style={styles.retryButton}
        testID={`${testID}-retry-button`}
        color={Colors.orange500}
        titleColor={Colors.neutralWhite}
      />
    </BlockView>
  );
};
