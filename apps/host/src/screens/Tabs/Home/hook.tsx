import React, { useCallback, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  ChatRouteName,
  EndpointKeys,
  FastImage,
  handleError,
  ISO_CODE,
  RouteName,
  useApiMutation,
  useAppLoading,
  useAppStore,
  useCheckSignIn,
  useI18n,
  useUserStore,
} from '@btaskee/design-system';

import { useAppNavigation, useGetDataInitApp } from '@hooks';
import { imgUseApp } from '@images';

import { PrivacyPolicy } from './components';
import styles from './styles';

const useHomeScreen = () => {
  const { t } = useI18n('host');
  const { isoCode } = useAppStore();
  const { user, getUser } = useUserStore();
  const navigation = useAppNavigation();

  const { onHandleCheckSignIn } = useCheckSignIn();
  const { getDataInitApp } = useGetDataInitApp();
  const { showAppLoading, hideAppLoading } = useAppLoading();

  const { mutate: updatePrivacyPolicy } = useApiMutation({
    key: EndpointKeys.updatePrivacyPolicy,
    options: {
      onMutate: showAppLoading,
      onSettled: hideAppLoading,
      onSuccess: getUser,
      onError: (error) => {
        handleError(error);
      },
    },
  });

  // Run app initialization once (or when getDataInitApp changes), independent of user updates
  useEffect(() => {
    getDataInitApp();
  }, [getDataInitApp]);

  // Show privacy policy alert when needed, without triggering init again
  useEffect(() => {
    if (user?._id && !user?.updatePrivacyPolicyAt && isoCode === ISO_CODE.VN) {
      Alert.alert.open({
        title: t('PRIVACY_POLICY.TITLE_POLICY'),
        message: <PrivacyPolicy />,
        actions: [
          {
            text: t('SKIP'),
            style: 'cancel',
            onPress: onSkipUpdatePrivacyPolicy,
          },
          {
            text: t('PRIVACY_POLICY.ACCEPTED_POLICY'),
            onPress: onUpdatePrivacyPolicy,
          },
        ],
        isDisabledBackdropPress: true,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user?._id, user?.updatePrivacyPolicyAt, isoCode, t]);

  const handleNavigateToChat = () => {
    onHandleCheckSignIn(() =>
      navigation.navigate(RouteName.ChatManagement, {
        screen: ChatRouteName.Chat,
      }),
    );
  };

  const onUpdatePrivacyPolicy = useCallback(() => {
    updatePrivacyPolicy({});
  }, [updatePrivacyPolicy]);

  const onSkipUpdatePrivacyPolicy = useCallback(() => {
    Alert.alert?.open({
      title: t('PRIVACY_POLICY.TITLE_SKIP_POLICY'),
      message: (
        <FastImage
          resizeMode={'cover'}
          source={imgUseApp}
          style={styles.imgUseApp}
        />
      ),
      actions: [
        {
          text: t('OK'),
          onPress: onUpdatePrivacyPolicy,
        },
      ],
      isDisabledBackdropPress: true,
    });
  }, [t, onUpdatePrivacyPolicy]);

  return { navigation, t, handleNavigateToChat };
};

export default useHomeScreen;
