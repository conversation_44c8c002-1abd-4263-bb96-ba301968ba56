import React, { memo } from 'react';
import {
  BlockView,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  FontSizes,
  IconImage,
  openUrl,
  PrimaryButton,
  Spacing,
  useI18n,
} from '@btaskee/design-system';

import { icUpdateAppVersion } from '@images';

interface Props {
  link: string;
}

const RequireUpdateVersionApp = ({ link }: Props) => {
  const { t } = useI18n('host');

  return (
    <BlockView
      flex
      center
      padding={{ horizontal: Spacing.SPACE_16 }}
    >
      <IconImage
        source={icUpdateAppVersion}
        size={DeviceHelper.WINDOW.WIDTH / 2}
      />
      <CText
        size={FontSizes.SIZE_20}
        bold
        center
        margin={{
          bottom: Spacing.SPACE_16,
          top: DeviceHelper.WINDOW.HEIGHT / 10,
        }}
      >
        {t('UPDATE_APP_VERSION.TITLE')}
      </CText>
      <CText
        testID="txtUpdateVersionRequire"
        center
        margin={{ bottom: Spacing.SPACE_32 }}
      >
        {t('UPDATE_APP_VERSION.UPDATE_REQUIRE')}
      </CText>
      <PrimaryButton
        testID="btnUpdateNow"
        title={t('UPDATE_APP_VERSION.UPDATE_NOW')}
        onPress={() => openUrl(link)}
      />
    </BlockView>
  );
};

export default memo(RequireUpdateVersionApp);
