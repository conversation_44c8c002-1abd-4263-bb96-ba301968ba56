import { useState } from 'react';
import { NativeModules } from 'react-native';
import {
  AccountRouteName,
  Alert,
  EndpointKeys,
  getColorStatusOfTask,
  IOutstandingPayment,
  IPaymentMethodInfo,
  openUrl,
  PAYMENT_METHOD,
  PaymentService,
  RouteName,
  showPriceAndCurrency,
  TYPE_OF_PAYMENT,
  useApiMutation,
  useAppLoading,
  useAppStore,
  useI18n,
  useUserStore,
} from '@btaskee/design-system';

import { useAppNavigation } from '@hooks';

export interface OutstandingItemProps extends IOutstandingPayment {
  onReloadOutStanding: () => void;
}

const useOutstandingItem = ({ _id }: OutstandingItemProps) => {
  const { t } = useI18n('host');
  const { isoCode } = useAppStore();
  const { getFinancialAccount } = useUserStore();
  const navigation = useAppNavigation();
  const { hideAppLoading, showAppLoading } = useAppLoading();

  const [paymentMethod, setPaymentMethod] = useState<IPaymentMethodInfo>();
  const [isDisableSubmit, setIsDisableSubmit] = useState(false);
  const colorTaskStatus = getColorStatusOfTask('CANCELED');

  const { mutate: rechargePaymentMutate } = useApiMutation({
    key: EndpointKeys.rechargePayment,
    options: {
      onMutate: showAppLoading,
      onSettled: hideAppLoading,
      onSuccess: (data) => {
        // TODO: need to implement payment

        switch (paymentMethod?.name) {
          // Payment card (visa/master)
          case PAYMENT_METHOD.card:
            Alert.alert.open({
              title: t('DIALOG_TITLE_INFORMATION'),
              message: t('PAYMENT_CARD_REVIEW'),
              actions: [{ text: t('CLOSE') }],
            });
            navigation.popToTop();
            break;
          // ATM/InternetBanking
          case PAYMENT_METHOD.bankTransfer:
            if (data?.url) {
              // navigation.navigate(RouteName.VTCPayWebview, {
              //   url: data?.url,
              //   title: 'ATM & Internet Banking',
              //   type: 'RECHARGE',
              //   response: () => {
              //     onReloadOutStanding();
              //     navigation.goBack();
              //   },
              //   // onStateChange: (state) => {
              //   //   stopURL = state.url;
              //   // },
              //   onBack: () => {
              //     onReloadOutStanding();
              //     navigation.goBack();
              //   },
              // });
            }
            break;

          // MOMO
          case PAYMENT_METHOD.momo:
            if (data?.payUrl) {
              navigation.popToTop();

              openUrl(data.payUrl);
            }
            break;

          // ZALO
          case PAYMENT_METHOD.zaloPay:
            const { PayZaloBridge } = NativeModules;
            if (data?.token) {
              // open app zaloPay
              PayZaloBridge.payOrder(data?.token);
            }
            break;
          // promptPay
          case PAYMENT_METHOD.promptPay:
            // TODO: need to implement payment
            // navigation.navigate(RouteName.Payment, {
            //   screen: PaymentRouteName.PayWithQRCode,
            //   params: {
            //     data: data,
            //     amount: cost,
            //     title: t('PAYMENT_METHOD_PROMT_PAY'),
            //   },
            // });
            break;
          // Credit
          case PAYMENT_METHOD.credit:
            Alert.alert.open({
              title: t('DIALOG_TITLE_INFORMATION'),
              message: t('BOOK_SUBSCRIPTION_SUCCESS_HEADER'),
              actions: [
                {
                  text: t('CLOSE'),
                  onPress: () => {
                    getFinancialAccount();
                  },
                },
              ],
            });
            navigation.popToTop();
            break;
          default:
            break;
        }
      },
      onError: (error: any) => {
        // Show error
        const errorCode = error?.code;
        if (errorCode) {
          const errorObj: any = {
            title: t('DIALOG_TITLE_INFORMATION'),
            message: t('ERROR_TRY_AGAIN'),
            actions: [{ text: t('CLOSE') }],
          };
          switch (errorCode) {
            case 'TASK_HAS_BEEN_PAYMENT':
              errorObj.message = t('TASK_HAS_BEEN_PAYMENT');
              break;
            case 'PAYMENT_PROCESSING':
              errorObj.message = t('TOP_UP_PAYMENT_CARD_SUCCESS');
              break;
            case 'NOT_ENOUGH_MONEY':
              errorObj.message = t('BPAY_LACK_OF_MONEY', {
                t: showPriceAndCurrency(error?.data?.amount),
              });
              errorObj.actions = [
                { text: t('CLOSE'), style: 'cancel' },
                {
                  text: t('ADD_MONEY'),
                  onPress: () =>
                    navigation?.navigate(RouteName.TabAccountNavigator, {
                      screen: AccountRouteName.BPay,
                    }),
                },
              ];
              break;
            default:
              break;
          }

          // show error
          return Alert.alert.open(errorObj);
        }
      },
    },
  });

  const handleChangePaymentMethod = (payment: IPaymentMethodInfo) => {
    setPaymentMethod(payment);

    // Select other payment, show button payment
    if (paymentMethod?.name !== payment?.name) {
      setIsDisableSubmit(false);
    }
  };

  const onChoosePaymentMethod = async () => {
    const payment = await PaymentService.choosePaymentMethod({
      type: TYPE_OF_PAYMENT.recharge,
      currentPaymentMethod: paymentMethod,
    });
    handleChangePaymentMethod(payment);
  };

  const repayOutstanding = async () => {
    const selectedPayment: any = {
      method: paymentMethod?.name,
    };
    if (paymentMethod?.cardInfo) {
      selectedPayment.cardId = paymentMethod?.cardInfo?._id;
    }

    if (paymentMethod?.bankInfo) {
      selectedPayment.bank = paymentMethod?.bankInfo?.name;
    }

    const option = {
      outstandingPaymentId: _id,
      payment: selectedPayment,
      isoCode,
    };
    setIsDisableSubmit(true);
    rechargePaymentMutate(option);
  };

  return {
    t,
    navigation,
    paymentMethod,
    isDisableSubmit,
    colorTaskStatus,
    repayOutstanding,
    onChoosePaymentMethod,
  };
};

export default useOutstandingItem;
