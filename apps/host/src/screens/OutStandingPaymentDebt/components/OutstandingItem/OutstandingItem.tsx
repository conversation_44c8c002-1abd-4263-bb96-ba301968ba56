import React, { memo } from 'react';
import {
  BlockView,
  BorderRadius,
  Card,
  CText,
  DateTimeHelpers,
  FastImage,
  getTextWithLocale,
  PrimaryButton,
  showPriceAndCurrency,
  Spacing,
  TypeFormatDate,
} from '@btaskee/design-system';

import { cardIcon, icClock2, icDollar, icLocation } from '@images';

import useOutstandingItem, { OutstandingItemProps } from './hook';
import styles from './styles';

const firstNumber = '**** **** **** ';

const OutstandingItem: React.FC<OutstandingItemProps> = (props) => {
  const {
    t,
    paymentMethod,
    colorTaskStatus,
    isDisableSubmit,
    onChoosePaymentMethod,
    repayOutstanding,
  } = useOutstandingItem(props);
  const { createdAt, cost, cardInfo, serviceText, taskInfo } = props;

  return (
    <Card margin={{ bottom: Spacing.SPACE_20 }}>
      <BlockView
        flexGrow
        padding={{ left: Spacing.SPACE_04, bottom: Spacing.SPACE_16 }}
      >
        <BlockView
          flex
          radius={BorderRadius.RADIUS_16}
        >
          <CText
            bold
            style={{ fontSize: Spacing.SPACE_20 }}
          >
            {getTextWithLocale(serviceText)}
          </CText>
        </BlockView>

        <BlockView margin={{ top: Spacing.SPACE_12 }}>
          <BlockView
            flex
            backgroundColor={colorTaskStatus?.backgroundColor}
            radius={BorderRadius.RADIUS_16}
            padding={{ horizontal: Spacing.SPACE_08 }}
          >
            <CText color={colorTaskStatus?.color}>
              {t('OUTSTANDING_PAYMENT_STATUS')}
            </CText>
          </BlockView>
        </BlockView>
      </BlockView>

      <BlockView style={styles.outStandingItem}>
        <FastImage
          source={icLocation}
          style={styles.icon}
        />
        <CText
          testID={'address'}
          style={styles.outStandingItemTxtRight}
        >
          {taskInfo?.address}
        </CText>
      </BlockView>

      <BlockView style={styles.outStandingItem}>
        <FastImage
          source={icClock2}
          style={styles.icon}
        />
        <CText style={styles.outStandingItemTxtRight}>
          {DateTimeHelpers.formatToString({
            date: createdAt,
            typeFormat: TypeFormatDate.DateTimeFullWithDayAndTimeFirst,
          })}
        </CText>
      </BlockView>

      {cardInfo?.cardNumber ? (
        <BlockView style={styles.outStandingItem}>
          <FastImage
            source={cardIcon}
            style={styles.icon}
          />
          <CText margin={{ left: Spacing.SPACE_20 }}>
            {firstNumber}
            {cardInfo.cardNumber}
          </CText>
        </BlockView>
      ) : null}

      <BlockView style={styles.outStandingItem}>
        <FastImage
          source={icDollar}
          style={styles.icon}
        />
        <CText style={styles.outStandingItemTxtRight}>
          {showPriceAndCurrency(cost)}
        </CText>
      </BlockView>

      <BlockView margin={{ top: Spacing.SPACE_16 }}>
        <CText>
          {t('OUTSTANDING_PAYMENT_CHOOSE_PAYMENT_METHOD').toUpperCase()}
        </CText>

        {/*
            // TODO: need to implement payment
        */}
        <PrimaryButton
          title="SELECT_PAYMENT_METHOD"
          onPress={onChoosePaymentMethod}
        />
      </BlockView>

      <PrimaryButton
        style={styles.button}
        disabled={Boolean(!paymentMethod || isDisableSubmit)}
        onPress={repayOutstanding}
        title={t('OUTSTANDING_PAYMENT_REPAY_LABEL')}
      />
    </Card>
  );
};

export default memo(OutstandingItem);
