import React, { memo } from 'react';
import { BlockView, CText, ScrollView, Spacing } from '@btaskee/design-system';

import OutstandingItem from './components/OutstandingItem';
import useOutStandingPaymentDebt, { OutStandingPaymentDebtProps } from './hook';
import styles from './styles';

export const OutStandingPaymentDebt: React.FC<OutStandingPaymentDebtProps> =
  memo((props) => {
    const { t, onReloadOutStanding, listOutstanding } =
      useOutStandingPaymentDebt(props);

    return (
      <BlockView
        flex
        style={{ padding: Spacing.SPACE_20 }}
      >
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={styles.scrollView}
        >
          {listOutstanding.length > 0 ? (
            listOutstanding.map((outstanding) => {
              return (
                <BlockView key={outstanding._id}>
                  <OutstandingItem
                    {...outstanding}
                    reloadOutstanding={onReloadOutStanding}
                  />
                </BlockView>
              );
            })
          ) : (
            <BlockView style={{ marginTop: Spacing.SPACE_32 }}>
              <CText>{t('OUTSTANDING_EMPTY')}</CText>
            </BlockView>
          )}
        </ScrollView>
      </BlockView>
    );
  });
