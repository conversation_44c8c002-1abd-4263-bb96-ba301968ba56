import { useCallback, useState } from 'react';
import {
  EndpointKeys,
  HostStackScreenProps,
  RouteName,
  useApiMutation,
  useI18n,
} from '@btaskee/design-system';

export type OutStandingPaymentDebtProps =
  HostStackScreenProps<RouteName.OutStandingPaymentDebt>;

const useOutStandingPaymentDebt = ({
  navigation,
  route,
}: OutStandingPaymentDebtProps) => {
  const { t } = useI18n('host');
  const [listOutstanding, setListOutstanding] = useState(
    route.params?.outstanding || [],
  );

  const { mutate } = useApiMutation({
    key: EndpointKeys.getOutstandingPayment,
    options: {
      onSuccess: (data) => {
        setListOutstanding(data);
      },
    },
  });
  const onReloadOutStanding = useCallback(() => {
    mutate({});
  }, [mutate]);

  return { navigation, t, listOutstanding, onReloadOutStanding };
};

export default useOutStandingPaymentDebt;
