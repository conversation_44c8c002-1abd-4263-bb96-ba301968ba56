import { useMemo } from 'react';
import {
  getTextWithLocale,
  IService,
  useI18n,
  useSettingsStore,
} from '@btaskee/design-system';
import { isEmpty } from 'lodash-es';

interface IServiceGroup {
  _id: string;
  text: any;
  title?: string;
  services: IService[];
}

interface UseServiceGroupsProps {
  favoriteList: IService[];
}

export const useServiceGroups = ({ favoriteList }: UseServiceGroupsProps) => {
  const { t: tHost } = useI18n('host');
  const settingsStore = useSettingsStore();
  const serviceGroup = settingsStore?.settings?.serviceGroup;
  const services = settingsStore?.settings?.services;

  const serviceGroupList: IServiceGroup[] = useMemo(() => {
    let customServiceGroup: any = [];
    if (favoriteList?.length) {
      customServiceGroup = serviceGroup?.map((item) => {
        const newServices = item?.services?.filter((el) => {
          const isFavorite = favoriteList?.some(
            (favorite) => favorite?._id === el?._id,
          );
          return !isFavorite;
        });
        return {
          ...item,
          services: newServices,
        };
      });
    } else {
      customServiceGroup = serviceGroup;
    }

    // const listNewServices = services?.filter(
    //   (item) =>
    //     item?.isNewService &&
    //     !favoriteList?.some((favorite) => favorite?._id === item?._id),
    // );
    // if (!isEmpty(listNewServices)) {
    //   customServiceGroup.splice(0, 0, {
    //     _id: 'newServices',
    //     services: listNewServices,
    //     title: tHost('EXPLORE.NEW_SERVICES'),
    //   });
    // }

    return customServiceGroup;
  }, [favoriteList, serviceGroup, services, tHost]);

  const getServiceGroupTitle = (item: IServiceGroup) => {
    return item.title ? item.title : getTextWithLocale(item.text);
  };

  const isEmptyServiceGroup = useMemo(() => {
    return isEmpty(
      serviceGroupList?.filter((item) => !isEmpty(item?.services)),
    );
  }, [serviceGroupList]);

  return {
    serviceGroup,
    serviceGroupList,
    getServiceGroupTitle,
    isEmptyServiceGroup,
  };
};
