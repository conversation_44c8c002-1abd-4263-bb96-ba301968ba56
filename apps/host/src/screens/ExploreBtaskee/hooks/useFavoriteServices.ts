import { useCallback, useEffect, useMemo, useState } from 'react';
import {
  AnimationHelpers,
  EndpointKeys,
  getIsoCodeGlobal,
  handleError,
  IService,
  ToastHelpers,
  useApiMutation,
  useAppLoading,
  useI18n,
  useSettingsStore,
  useUserStore,
} from '@btaskee/design-system';
import { isEmpty } from 'lodash-es';

const MAX_FAVORITE_ITEM = 8;
const MIN_FAVORITE_ITEM = 4;

interface UseFavoriteServicesProps {
  isHadOpenExploreBtaskee?: boolean;
  setIsHadOpenExploreBtaskee: (value: boolean) => void;
  isOpenDefault?: boolean;
}

export const useFavoriteServices = ({
  isHadOpenExploreBtaskee,
  setIsHadOpenExploreBtaskee,
  isOpenDefault,
}: UseFavoriteServicesProps) => {
  const { t: tHost } = useI18n('host');
  const { t } = useI18n('common');
  const { showAppLoading, hideAppLoading } = useAppLoading();
  const { user } = useUserStore();
  const { updateFavoriteService } = useSettingsStore();
  const settingsStore = useSettingsStore();
  const services = settingsStore?.settings?.services;

  // Convert IFavouriteService[] to IService[] by finding matching services
  const initialFavoriteList = useMemo(() => {
    const favouriteServices = settingsStore?.settings?.favouriteServices || [];
    if (!favouriteServices.length || !services?.length) return [];
    return favouriteServices
      .map((favService) =>
        services.find((service) => service._id === favService._id),
      )
      .filter(Boolean) as IService[];
  }, [settingsStore?.settings?.favouriteServices, services]);

  const [favoriteList, setFavoriteList] =
    useState<IService[]>(initialFavoriteList);

  const isEditDefault = isOpenDefault
    ? false
    : !isEmpty(user) && !isHadOpenExploreBtaskee;

  const [isEdit, setIsEdit] = useState(isEditDefault);

  // Sync favoriteList when initialFavoriteList changes
  useEffect(() => {
    setFavoriteList(initialFavoriteList);
  }, [initialFavoriteList]);

  useEffect(() => {
    if (isEditDefault) {
      setIsHadOpenExploreBtaskee(true);
    }
  }, [isEditDefault, setIsHadOpenExploreBtaskee]);

  // Check if user has changed favorite list
  const isChangeFavoriteList = useMemo(() => {
    let isChange = false;
    if (!isEmpty(user) && favoriteList.length) {
      isChange =
        JSON.stringify(favoriteList) !== JSON.stringify(initialFavoriteList);
    }
    return isChange;
  }, [favoriteList, initialFavoriteList, user]);

  const { mutate: addFavoriteService } = useApiMutation({
    key: EndpointKeys.addFavoriteService,
    options: {
      onMutate: () => {
        showAppLoading();
      },
      onSettled: () => {
        hideAppLoading();
      },
    },
  });

  const toggleEdit = useCallback(() => {
    setIsEdit(!isEdit);
  }, [isEdit]);

  const setInitialFavoriteList = useCallback(() => {
    toggleEdit();
    setFavoriteList(initialFavoriteList);
  }, [toggleEdit, initialFavoriteList]);

  const onAdd = useCallback(
    (item: IService) => () => {
      AnimationHelpers.runLayoutAnimation();
      if (favoriteList.length >= MAX_FAVORITE_ITEM) {
        ToastHelpers.showWarning({
          message: tHost('EXPLORE.MAX_FAVORITE_MESSAGE', {
            max: MAX_FAVORITE_ITEM,
          }),
        });
      } else {
        const newList = [...favoriteList, item];
        setFavoriteList(newList);
      }
    },
    [favoriteList, tHost],
  );

  const onMinus = useCallback(
    (item: IService) => () => {
      AnimationHelpers.runLayoutAnimation();
      if (favoriteList.length <= MIN_FAVORITE_ITEM) {
        ToastHelpers.showWarning({
          message: tHost('EXPLORE.MIN_FAVORITE_MESSAGE', {
            min: MIN_FAVORITE_ITEM,
          }),
        });
      } else {
        const newList = favoriteList.filter((el) => item?._id !== el._id);
        setFavoriteList(newList);
      }
    },
    [favoriteList, tHost],
  );

  const onSave = useCallback(async () => {
    try {
      showAppLoading();
      const favouriteServiceIds = favoriteList.map((el) => el._id as string);
      addFavoriteService(
        { favouriteServiceIds, isoCode: getIsoCodeGlobal() },
        {
          onSuccess: () => {
            ToastHelpers.showSuccess({
              message: t('UPDATE_SUCCESS'),
              position: 'top',
            });
            toggleEdit();
            updateFavoriteService(favoriteList);
          },
          onError: (error) => {
            handleError(error);
          },
        },
      );
    } catch (error) {
    } finally {
      hideAppLoading();
    }
  }, [
    t,
    favoriteList,
    addFavoriteService,
    toggleEdit,
    updateFavoriteService,
    showAppLoading,
    hideAppLoading,
  ]);

  const favoriteListRender = useMemo(() => {
    const length = favoriteList.length;
    const arr = Array(MAX_FAVORITE_ITEM).fill(null).slice(length);
    if (isEdit && length < MAX_FAVORITE_ITEM) {
      return [...favoriteList, ...arr];
    }
    return favoriteList;
  }, [favoriteList, isEdit]);

  const updateFavoriteList = useCallback((newList: IService[]) => {
    setFavoriteList(newList);
  }, []);

  return {
    favoriteList,
    favoriteListRender,
    isEdit,
    isChangeFavoriteList,
    toggleEdit,
    setInitialFavoriteList,
    onAdd,
    onMinus,
    onSave,
    updateFavoriteList,
    MAX_FAVORITE_ITEM,
    MIN_FAVORITE_ITEM,
  };
};
