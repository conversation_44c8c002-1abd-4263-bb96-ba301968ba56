import React from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ont<PERSON>izes,
  IService,
  Spacing,
  useI18n,
} from '@btaskee/design-system';

import { ServiceItem } from '@components';

import { styles } from '../styles';

const WIDTH_CONTENT_CONTAINER =
  DeviceHelper.WINDOW.WIDTH - Spacing.SPACE_12 * 4 - 2;
const WIDTH_ITEM = WIDTH_CONTENT_CONTAINER / 4;

interface IServiceGroup {
  _id: string;
  text: any;
  title?: string;
  services: IService[];
}

interface ServiceGroupsListProps {
  serviceGroupList: IServiceGroup[];
  isEdit: boolean;
  isEmptyServiceGroup: boolean;
  getServiceGroupTitle: (item: IServiceGroup) => string;
  onAdd: (item: IService) => () => void;
  promotionCode?: string;
  defaultPaymentMethod?: any;
}

const ServiceGrid: React.FC<{
  services: IService[];
  groupId: string;
  isEdit: boolean;
  onAdd: (item: IService) => () => void;
  promotionCode?: string;
  defaultPaymentMethod?: any;
}> = ({
  services,
  groupId,
  isEdit,
  onAdd,
  promotionCode,
  defaultPaymentMethod,
}) => {
  return (
    <BlockView style={styles.itemServiceContainer}>
      {services.map((service) => {
        return (
          <ServiceItem
            testID={`postTaskService${service?.name}`}
            key={`${groupId}-${service._id}`}
            service={service}
            isShowPlus={isEdit}
            widthItem={WIDTH_ITEM}
            promotionCode={promotionCode}
            onPressPlus={onAdd(service)}
            defaultPaymentMethod={defaultPaymentMethod}
          />
        );
      })}
    </BlockView>
  );
};

export const ServiceGroupsList: React.FC<ServiceGroupsListProps> = ({
  serviceGroupList,
  isEdit,
  isEmptyServiceGroup,
  getServiceGroupTitle,
  onAdd,
  promotionCode,
  defaultPaymentMethod,
}) => {
  const { t: tHost } = useI18n('host');

  if (isEdit) {
    // Check if service groups are empty in edit mode
    if (isEmptyServiceGroup) return null;

    return (
      <BlockView style={styles.blockContainer}>
        <BlockView style={styles.headerBlock}>
          <CText
            size={FontSizes.SIZE_16}
            bold
          >
            {tHost('EXPLORE.OTHER_SERVICES')}
          </CText>
        </BlockView>
        <BlockView>
          {serviceGroupList?.map((item) => {
            if (!item?.services?.length) return null;
            return (
              <BlockView
                key={item?._id}
                margin={{ top: Spacing.SPACE_16 }}
              >
                <CText bold>{getServiceGroupTitle(item)}</CText>
                <ServiceGrid
                  services={item.services}
                  groupId={item._id}
                  isEdit={isEdit}
                  onAdd={onAdd}
                  promotionCode={promotionCode}
                  defaultPaymentMethod={defaultPaymentMethod}
                />
              </BlockView>
            );
          })}
        </BlockView>
      </BlockView>
    );
  }

  return (
    <>
      {serviceGroupList?.map((item) => {
        if (!item?.services?.length) return null;
        return (
          <BlockView
            key={item?._id}
            style={styles.blockContainer}
          >
            <BlockView style={styles.headerBlock}>
              <CText
                size={FontSizes.SIZE_16}
                bold
              >
                {getServiceGroupTitle(item)}
              </CText>
            </BlockView>
            <ServiceGrid
              services={item.services}
              groupId={item._id}
              isEdit={isEdit}
              onAdd={onAdd}
              promotionCode={promotionCode}
              defaultPaymentMethod={defaultPaymentMethod}
            />
          </BlockView>
        );
      })}
    </>
  );
};
