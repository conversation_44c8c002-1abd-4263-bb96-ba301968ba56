import React from 'react';
import { DragSortableView } from 'react-native-drag-sort';
import {
  BlockView,
  Colors,
  ConditionView,
  CText,
  DeviceHelper,
  FontSizes,
  IService,
  Spacing,
  useI18n,
} from '@btaskee/design-system';
import { isEmpty } from 'lodash-es';

import { ServiceItem } from '@components';

import { styles } from '../styles';

const WIDTH_CONTENT_CONTAINER =
  DeviceHelper.WINDOW.WIDTH - Spacing.SPACE_12 * 4 - 2;
const WIDTH_ITEM = WIDTH_CONTENT_CONTAINER / 4;

interface FavoriteServicesListProps {
  favoriteList: IService[];
  favoriteListRender: (IService | null)[];
  isEdit: boolean;
  onMinus: (item: IService) => () => void;
  onDragStart: () => void;
  onDragEnd: () => void;
  onDataChange: (data: IService[]) => void;
  promotionCode?: string;
  defaultPaymentMethod?: any;
}

export const FavoriteServicesList: React.FC<FavoriteServicesListProps> = ({
  favoriteList,
  favoriteListRender,
  isEdit,
  onMinus,
  onDragStart,
  onDragEnd,
  onDataChange,
  promotionCode,
  defaultPaymentMethod,
}) => {
  const { t: tHost } = useI18n('host');

  if (isEmpty(favoriteList)) {
    return null;
  }

  return (
    <BlockView>
      <ConditionView
        condition={isEdit}
        viewTrue={
          <BlockView style={styles.desFavoriteContainer}>
            <CText
              color={Colors.green500}
              size={FontSizes.SIZE_12}
            >
              {tHost('EXPLORE.DES_FAVORITE_SERVICE')}
            </CText>
          </BlockView>
        }
      />
      <BlockView style={styles.itemServiceContainer}>
        <DragSortableView
          childrenWidth={WIDTH_ITEM}
          childrenHeight={WIDTH_ITEM * 0.6 + 80}
          parentWidth={WIDTH_CONTENT_CONTAINER}
          dataSource={favoriteListRender}
          keyExtractor={(item, index) => item?._id || index.toString()}
          renderItem={(item) => (
            <ServiceItem
              testID={`postTaskService${item?.name}`}
              service={item}
              isShowMinus={isEdit}
              widthItem={WIDTH_ITEM}
              onPressMinus={onMinus(item)}
              promotionCode={promotionCode}
              defaultPaymentMethod={defaultPaymentMethod}
            />
          )}
          onDragStart={onDragStart}
          onClickItem={(_, item) => {
            onMinus(item)();
          }}
          onDragEnd={onDragEnd}
          onDataChange={(data) => {
            const newList = data.filter((e) => e);
            onDataChange(newList);
          }}
        />
      </BlockView>
    </BlockView>
  );
};
