import React from 'react';
import { BlockView, IService } from '@btaskee/design-system';
import { isEmpty } from 'lodash-es';

import { styles } from '../styles';
import { FavoriteServicesHeader } from './FavoriteServicesHeader';
import { FavoriteServicesList } from './FavoriteServicesList';

interface FavoriteServicesProps {
  favoriteList: IService[];
  favoriteListRender: (IService | null)[];
  isEdit: boolean;
  isChangeFavoriteList: boolean;
  onToggleEdit: () => void;
  onCancelEdit: () => void;
  onSave: () => void;
  onMinus: (item: IService) => () => void;
  onDragStart: () => void;
  onDragEnd: () => void;
  onDataChange: (data: IService[]) => void;
  promotionCode?: string;
  defaultPaymentMethod?: any;
}

export const FavoriteServices: React.FC<FavoriteServicesProps> = ({
  favoriteList,
  favoriteListRender,
  isEdit,
  isChangeFavoriteList,
  onToggleEdit,
  onCancelEdit,
  onSave,
  onMinus,
  onDragStart,
  onDragEnd,
  onDataChange,
  promotionCode,
  defaultPaymentMethod,
}) => {
  if (isEmpty(favoriteList)) {
    return null;
  }

  return (
    <BlockView style={styles.blockContainer}>
      <FavoriteServicesHeader
        isEdit={isEdit}
        isChangeFavoriteList={isChangeFavoriteList}
        onToggleEdit={onToggleEdit}
        onCancelEdit={onCancelEdit}
        onSave={onSave}
      />
      <FavoriteServicesList
        favoriteList={favoriteList}
        favoriteListRender={favoriteListRender}
        isEdit={isEdit}
        onMinus={onMinus}
        onDragStart={onDragStart}
        onDragEnd={onDragEnd}
        onDataChange={onDataChange}
        promotionCode={promotionCode}
        defaultPaymentMethod={defaultPaymentMethod}
      />
    </BlockView>
  );
};
