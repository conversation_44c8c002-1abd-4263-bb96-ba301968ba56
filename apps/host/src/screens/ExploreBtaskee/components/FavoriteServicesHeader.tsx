import React from 'react';
import {
  BlockView,
  Colors,
  ConditionView,
  CText,
  FontSizes,
  HitSlop,
  PrimaryButton,
  SizedBox,
  Spacing,
  TouchableOpacity,
  useI18n,
} from '@btaskee/design-system';

import { styles } from '../styles';

interface FavoriteServicesHeaderProps {
  isEdit: boolean;
  isChangeFavoriteList: boolean;
  onToggleEdit: () => void;
  onCancelEdit: () => void;
  onSave: () => void;
}

export const FavoriteServicesHeader: React.FC<FavoriteServicesHeaderProps> = ({
  isEdit,
  isChangeFavoriteList,
  onToggleEdit,
  onCancelEdit,
  onSave,
}) => {
  const { t } = useI18n('common');
  const { t: tHost } = useI18n('host');

  return (
    <BlockView style={styles.headerBlock}>
      <CText
        size={FontSizes.SIZE_16}
        bold
        flex
      >
        {tHost('EXPLORE.FAVORITE_SERVICES')}
      </CText>
      <ConditionView
        condition={isEdit}
        viewTrue={
          <BlockView row>
            <PrimaryButton
              testID="cancelFavouriteServiceBtn"
              title={t('CANCEL')}
              titleColor={Colors.green500}
              color={Colors.neutral100}
              titleProps={{ style: { fontSize: FontSizes.SIZE_12 } }}
              style={styles.button}
              onPress={onCancelEdit}
            />
            <ConditionView
              condition={isChangeFavoriteList}
              viewTrue={
                <>
                  <SizedBox width={Spacing.SPACE_12} />
                  <PrimaryButton
                    testID="saveFavouriteServiceBtn"
                    title={t('SAVE')}
                    titleProps={{ style: { fontSize: FontSizes.SIZE_12 } }}
                    style={styles.button}
                    onPress={onSave}
                  />
                </>
              }
            />
          </BlockView>
        }
        viewFalse={
          <TouchableOpacity
            testID="editFavouriteServiceBtn"
            hitSlop={HitSlop.SMALL}
            activeOpacity={0.7}
            onPress={onToggleEdit}
          >
            <CText color={Colors.green500}>{tHost('EXPLORE.EDIT')}</CText>
          </TouchableOpacity>
        }
      />
    </BlockView>
  );
};
