import React from 'react';
import { StatusBar } from 'react-native';
import { BlockView, CText, useI18n } from '@btaskee/design-system';

import { styles } from '../styles';

export const EmptyState: React.FC = () => {
  const { t } = useI18n('common');

  return (
    <BlockView style={styles.container}>
      <StatusBar backgroundColor="transparent" />
      <BlockView
        flex
        center
      >
        <CText bold>{t('COMING_SOON')}</CText>
      </BlockView>
    </BlockView>
  );
};
