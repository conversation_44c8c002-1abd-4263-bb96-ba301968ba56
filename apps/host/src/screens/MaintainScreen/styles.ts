import { StyleSheet } from 'react-native';
import { Colors, DeviceHelper, <PERSON>ont<PERSON><PERSON> } from '@btaskee/design-system';

const HEIGHT_IMAGE = Math.round(DeviceHelper.WINDOW.HEIGHT / 1.3);

export default StyleSheet.create({
  txtTitleButton: {
    paddingHorizontal: 20,
  },
  content: {
    top: -HEIGHT_IMAGE / 8,
  },
  txtHeader: {
    fontSize: 24,
    marginBottom: 15,
  },
  container: {
    // paddingHorizontal: constant.MARGIN.medium2,
    flex: 1,
  },
  imageStyle: {
    width: DeviceHelper.WINDOW.WIDTH,
    height: HEIGHT_IMAGE,
  },
  txtContent: {
    textAlign: 'center',
    lineHeight: 23,
    fontSize: FontSizes.SIZE_14,
    marginBottom: 15,
  },
  wrapButtonOffMaintain: {
    width: '25%',
    height: '15%',
    position: 'absolute',
    zIndex: 1,
    right: 0,
  },
  wrapInput: {
    position: 'absolute',
    top: 250,
    left: 0,
    right: 0,
    zIndex: 2,
  },
  containerInput: {
    backgroundColor: Colors.neutralBackground,
  },
});
