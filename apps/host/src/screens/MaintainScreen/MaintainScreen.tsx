import React, { memo, useState } from 'react';
import {
  BlockView,
  ConditionView,
  CText,
  CTextInput,
  FastImage,
  PrimaryButton,
  QueryObserverResult,
  RefetchOptions,
  ToastHelpers,
  TouchableOpacity,
  useI18n,
} from '@btaskee/design-system';
import { AxiosResponse } from 'axios';

import { imgMaintenanceApp } from '@images';

import styles from './styles';

let tOuts: any[] = [];

interface MaintainScreenProps {
  setIsMaintainApp: (value: boolean) => void;
  refetch: (
    options?: RefetchOptions | undefined,
  ) => Promise<QueryObserverResult<AxiosResponse<any, any>, Error>>;
}

const MaintainScreen: React.FC<MaintainScreenProps> = ({
  setIsMaintainApp,
  refetch,
}) => {
  const [isShowInput, setIsShowInput] = useState(false);
  const { t } = useI18n('host');

  const handleOffMaintain = () => {
    tOuts.push(
      setTimeout(function () {
        tOuts.shift();
      }, 5000),
    );

    if (tOuts.length === 10) {
      // clear all the timeouts
      for (const x in tOuts) {
        clearTimeout(tOuts[x]);
      }
      tOuts = [];
      setIsShowInput(true);
    }
  };
  // Cho phép tắt chế độ bảo trì
  const handleInput = (text: string) => {
    if (text === 'bTaskee347%@') {
      setIsMaintainApp(false);
      ToastHelpers.showWarning({
        message: 'bTaskee dev mode',
      });
    }
  };
  return (
    <BlockView
      horizontal
      style={styles.container}
    >
      <TouchableOpacity
        onPress={handleOffMaintain}
        activeOpacity={1}
        style={styles.wrapButtonOffMaintain}
      />
      <ConditionView
        condition={isShowInput}
        viewTrue={
          <BlockView style={styles.wrapInput}>
            <CTextInput
              onChangeText={handleInput}
              containerStyle={styles.containerInput}
              label={null}
            />
          </BlockView>
        }
      />
      <FastImage
        source={imgMaintenanceApp}
        style={styles.imageStyle}
        resizeMode="cover"
      />
      <BlockView
        style={styles.content}
        center
      >
        <CText
          style={styles.txtHeader}
          bold
        >
          {t('MAINTENANCE.OPPS')}
        </CText>
        <CText
          style={styles.txtContent}
          bold
        >
          {t('MAINTENANCE.TITLE')}
        </CText>
        <PrimaryButton
          onPress={() => refetch()}
          title={t('MAINTENANCE.RELOAD')}
        />
      </BlockView>
    </BlockView>
  );
};

export default memo(MaintainScreen);
