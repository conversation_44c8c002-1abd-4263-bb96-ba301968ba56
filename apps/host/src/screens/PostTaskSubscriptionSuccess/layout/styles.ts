import { StyleSheet } from 'react-native';
import {
  Colors,
  DeviceHelper,
  FontSizes,
  Spacing,
} from '@btaskee/design-system';

export const styles = StyleSheet.create({
  imageArea: {
    alignItems: 'center',
    marginTop: DeviceHelper.WINDOW.HEIGHT / 5,
    marginBottom: Spacing.SPACE_20,
  },
  image: {
    width: DeviceHelper.WINDOW.WIDTH * 0.6,
    height: DeviceHelper.WINDOW.WIDTH / 1.7,
  },
  txtLabel: {
    marginBottom: Spacing.SPACE_04,
    fontSize: FontSizes.SIZE_16,
  },
  txtDes: {
    paddingVertical: Spacing.SPACE_08,
    paddingHorizontal: Spacing.SPACE_20,
    textAlign: 'center',
    lineHeight: FontSizes.SIZE_20,
  },
  content: {
    alignItems: 'center',
  },
  container: {
    backgroundColor: Colors.neutralWhite,
    flex: 1,
  },
});
