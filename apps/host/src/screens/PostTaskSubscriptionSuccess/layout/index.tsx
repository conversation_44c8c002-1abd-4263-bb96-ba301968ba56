import React from 'react';
import {
  BlockView,
  BottomView,
  CText,
  FastImage,
  HostStackScreenProps,
  imgContentAlertSub,
  NavigationService,
  PrimaryButton,
  RouteName,
  TabActivityRouteName,
  useI18n,
} from '@btaskee/design-system';

import { styles } from './styles';

type PostTaskSubscriptionSuccessScreenProps =
  HostStackScreenProps<RouteName.PostTaskSubscriptionSuccess>;

export const PostTaskSubscriptionSuccessScreen =
  ({}: PostTaskSubscriptionSuccessScreenProps) => {
    const { t } = useI18n('common');

    const onDone = async () => {
      // go to tab Activity -> Subscription
      NavigationService.navigateToTabActivity(TabActivityRouteName.TabMonthly);
    };

    return (
      <BlockView style={styles.container}>
        <BlockView flex>
          <BlockView style={styles.imageArea}>
            <FastImage
              resizeMode={'cover'}
              source={imgContentAlertSub}
              style={styles.image}
            />
          </BlockView>
          <BlockView style={styles.content}>
            <CText
              bold
              style={styles.txtLabel}
            >
              {t('PREPAYMENT.PAYMENT_SUCCESS')}
            </CText>
            <CText style={styles.txtDes}>
              {t('BOOK_SUBSCRIPTION_SUCCESS_TEXT')}
            </CText>
          </BlockView>
        </BlockView>
        <BottomView>
          <PrimaryButton
            title={t('SEE_DETAILS')}
            onPress={onDone}
          />
        </BottomView>
      </BlockView>
    );
  };
