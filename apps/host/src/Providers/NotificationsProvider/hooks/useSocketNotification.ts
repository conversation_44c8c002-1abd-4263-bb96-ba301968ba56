import { useCallback } from 'react';
import {
  <PERSON><PERSON>,
  Endpoint<PERSON>eys,
  getTextWithLocale,
  Log,
  NavigationService,
  PaymentService,
  RouteName,
  SocketEndpointKeys,
  TaskManagementRouteName,
  useApiMutation,
  useAppLoadingStore,
  useI18n,
  useWebSocket,
} from '@btaskee/design-system';

import { IMessageNotification, NotificationValue } from '../types';

export const useSocketNotification = () => {
  const { t } = useI18n('common');
  const { showLoading, hideLoading } = useAppLoadingStore();

  const { mutateAsync: cancelPrepaymentAPI } = useApiMutation({
    key: EndpointKeys.cancelPrepayTask,
  });

  const _onTap = useCallback((message: IMessageNotification) => {
    switch (message.value) {
      case NotificationValue.PREPAYMENT:
        NavigationService.popToTopAndNavigate(RouteName.TaskManagement, {
          screen: TaskManagementRouteName.RepayTaskDetail,
          params: {
            taskId: message.data?.taskId!,
            isPaymentFailed: true,
          },
        });
        break;
      default:
        break;
    }
  }, []);

  const onMessage = useCallback(
    async (mess: IMessageNotification) => {
      Log.consoleLog(
        'NotificationsProvider -> Socket Notification -> mess:',
        mess,
      );
      // Extract the data from WebSocketMessage wrapper
      if (!mess.data) return;

      try {
        // Process specific notification types
        switch (mess.value) {
          case NotificationValue.PREPAYMENT:
            const title = getTextWithLocale(mess.data.title);
            const message = getTextWithLocale(mess.data.content);

            //Thanh toán thất bại
            if (
              mess.data?.taskId &&
              mess.data?.isPrepayTask &&
              !mess.data?.success
            ) {
              Alert.alert.open({
                title,
                message,
                actions: [
                  {
                    text: t('CANCEL_PAYMENT'),
                    style: 'cancel',
                    onPress: async () => {
                      NavigationService.popToTop();
                      await cancelPrepaymentAPI({
                        taskId: mess.data?.taskId!,
                      });
                    },
                  },
                  {
                    text: t('PREPAYMENT.REPAY'),
                    onPress: async () => {
                      showLoading();
                      await PaymentService.handlePrepayment(mess.data?.taskId!);
                      hideLoading();
                    },
                  },
                ],
              });
              return;
            }
            Alert.dropdownAlert.open({
              title,
              message,
              onTap: () => _onTap(mess),
            });
            return;
          default:
            break;
        }
      } catch (error) {}
    },
    [t, cancelPrepaymentAPI, showLoading, hideLoading, _onTap],
  );

  useWebSocket<any>({
    endPointKey: SocketEndpointKeys.notification,
    isRequireAuth: true,
    onMessage,
  });
};
