import { useCallback, useEffect, useMemo, useState } from 'react';
import { Platform } from 'react-native';
import DeviceInfo from 'react-native-device-info';
import { requestNotifications } from 'react-native-permissions';
import {
  <PERSON>ce<PERSON><PERSON>per,
  EndpointKeys,
  getVersionAppCode,
  getVersionAppName,
  Log,
  NavigationService,
  NotificationType,
  RouteName,
  useAgentStore,
  useApiMutation,
  useNotificationHandler,
  useUserStore,
} from '@btaskee/design-system';
import messaging from '@react-native-firebase/messaging';
import CleverTap from 'clevertap-react-native';
import { isEmpty } from 'lodash-es';

export const useNotificationController = () => {
  const { user } = useUserStore();
  const { onPressNotification } = useNotificationHandler();
  const { onInCreaseMessageUnread } = useAgentStore();

  const [isAcceptPermission, setIsAcceptPermission] = useState(false);
  const { mutateAsync: initRaixPushTokenAPI } = useApiMutation({
    key: EndpointKeys.initRaixPushToken,
  });

  const userId = useMemo(() => {
    return user?._id;
  }, [user]);

  const sendFCMTokenToServer = useCallback(
    async (tokenInfo: any) => {
      if (isEmpty(tokenInfo)) {
        return;
      }

      //check special character
      const tokenValue = DeviceHelper.isIos
        ? tokenInfo.apnToken
        : tokenInfo.fcmToken;

      if (!tokenValue || tokenValue.indexOf('"') > -1) {
        return;
      }

      const device: any = {
        token: DeviceHelper.isIos
          ? { apn: tokenInfo.apnToken }
          : { gcm: tokenInfo.fcmToken },
        userId,
        appName: 'bTaskeeAsker',
        metadata: {
          uuid: await DeviceInfo.getUniqueId(),
          platform: Platform.OS,
          version: DeviceInfo.getSystemVersion(),
          model: DeviceInfo.getDeviceId(),
          manufacturer: DeviceInfo.getBrand(),
          appVersion: getVersionAppName(),
          buildNumber: getVersionAppCode(),
          FCMToken: tokenInfo.fcmToken,
        },
      };

      // call api
      await initRaixPushTokenAPI(device);
    },
    [initRaixPushTokenAPI, userId],
  );

  const getFCMToken = useCallback(async () => {
    if (!isAcceptPermission) return;
    try {
      const tokenInfo: any = {};
      const fcmToken = await messaging().getToken();
      Log.consoleLog(`📱 FCM Token -> ${fcmToken}`);
      if (fcmToken) {
        tokenInfo.fcmToken = fcmToken;
        if (DeviceHelper.isAndroid) {
          CleverTap.setFCMPushToken(fcmToken);
        }
      }
      if (DeviceHelper.isIos) {
        const apnToken = await messaging().getAPNSToken();
        tokenInfo.apnToken = apnToken;
      }
      sendFCMTokenToServer(tokenInfo);
    } catch (error) {
      Log.consoleLog(`⭕️ Get FCM Token Failed`, error);
    }
  }, [isAcceptPermission, sendFCMTokenToServer]);

  const onMessage = useCallback(
    (remoteMessage: any) => {
      const type = Number(remoteMessage.data?.type || 0) as NotificationType;

      // Only use for ai agent notification
      switch (type) {
        case NotificationType.AI_AGENT:
          // Nếu không đang trong stack VoiceChat thì tăng message unread
          const currentStack = NavigationService.getCurrentScreenAndStack();
          if (currentStack?.stack === RouteName.VoiceChat) return;
          onInCreaseMessageUnread();
          break;
        default:
          break;
      }
    },
    [onInCreaseMessageUnread],
  );

  useEffect(() => {
    requestNotifications(['alert', 'sound']).then(({ status }) => {
      setIsAcceptPermission(status === 'granted');
    });
  }, []);

  useEffect(() => {
    getFCMToken();
  }, [getFCMToken]);

  useEffect(() => {
    messaging()
      .getInitialNotification()
      .then((remoteMessage) => {
        Log.consoleLog(
          '☁️ Notification -> getInitialNotification:',
          remoteMessage,
        );
        if (!remoteMessage?.data) return;
        onPressNotification?.(remoteMessage?.data);
      });

    const unsubscribeOnMessage = messaging().onMessage((remoteMessage) => {
      Log.consoleLog('☁️ Notification -> onMessage:', remoteMessage);
      onMessage(remoteMessage);
    });

    const unsubscribeOnNotificationOpenedApp =
      messaging().onNotificationOpenedApp((remoteMessage) => {
        Log.consoleLog(
          '☁️ Notification -> onNotificationOpenedApp:',
          remoteMessage,
        );
        if (!remoteMessage?.data) return;
        onPressNotification?.(remoteMessage?.data);
      });

    return () => {
      unsubscribeOnMessage();
      unsubscribeOnNotificationOpenedApp();
    };
  }, [onMessage, onPressNotification]);
};
