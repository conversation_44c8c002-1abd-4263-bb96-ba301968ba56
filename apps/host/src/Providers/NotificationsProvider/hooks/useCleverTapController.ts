import { useCallback, useEffect, useRef } from 'react';
import { NavigationService, useReward } from '@btaskee/design-system';
import CleverTap from 'clevertap-react-native';
import { debounce } from 'lodash-es';

let nameEvent: any = null;
let dataEvent: any = null;
CleverTap.addListener(
  CleverTap.CleverTapPushNotificationClicked,
  (event: any) => {
    nameEvent = CleverTap.CleverTapPushNotificationClicked;
    dataEvent = event;
  },
);

export const useCleverTapController = () => {
  const { onChooseService } = useReward();

  useEffect(() => {
    CleverTap.enableDeviceNetworkInfoReporting(true);
    CleverTap.registerForPush();
  }, []);

  const handleCleverTapEvent = useCallback((eventName: any, event: any) => {
    const customExtras = event?.customExtras || event || {};
    const { screenName, screenNameOfMiniApp, ...params } = customExtras;
    const { serviceId, promotionCode, paymentMethod } = params;
    if (promotionCode) {
      return onChooseService({
        promotionCode,
        serviceId,
        paymentMethod,
      });
    }
    NavigationService.navigateToScreen({
      screenNameOfMiniApp,
      screenName,
      params,
    });
  }, []);

  const onHandleCleverTapEvent = useRef(
    debounce(() => handleCleverTapEvent(nameEvent, dataEvent), 500),
  ).current;

  useEffect(() => {
    // CleverTap - catch the click event in app notification button
    CleverTap.addListener(
      CleverTap.CleverTapInAppNotificationButtonTapped,
      (event: any) => {
        handleCleverTapEvent(
          CleverTap.CleverTapInAppNotificationButtonTapped,
          event,
        );
      },
    );
    CleverTap.addListener(
      CleverTap.CleverTapPushNotificationClicked,
      (event: any) => {
        handleCleverTapEvent(CleverTap.CleverTapPushNotificationClicked, event);
      },
    );
    onHandleCleverTapEvent();

    return () => {
      CleverTap.removeListener(
        CleverTap.CleverTapInAppNotificationButtonTapped,
      );
      CleverTap.removeListener(CleverTap.CleverTapPushNotificationClicked);
    };
  }, []);
};
