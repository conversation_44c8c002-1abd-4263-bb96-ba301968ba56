import React from 'react';
import {
  AlertComponent,
  alertRef,
  BottomSheetModalProvider,
  CToast,
  cToastRef,
  DropdownAlert,
  dropdownAlertRef,
  GalleryModal,
  galleryModalRef,
} from '@btaskee/design-system';

interface AlertProviderProps {
  children: React.ReactNode;
}

export const AlertProvider = ({ children }: AlertProviderProps) => {
  return (
    <BottomSheetModalProvider>
      {children}
      <AlertComponent ref={alertRef} />
      <GalleryModal ref={galleryModalRef} />
      <DropdownAlert ref={dropdownAlertRef} />
      <CToast ref={cToastRef} />
    </BottomSheetModalProvider>
  );
};
