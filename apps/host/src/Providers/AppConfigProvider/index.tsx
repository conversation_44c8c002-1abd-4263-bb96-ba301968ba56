import React from 'react';

import { useAppInit } from '@hooks';
import { AppErrorScreen } from '@screens/AppError';

interface AppConfigProviderProps {
  children: React.ReactNode;
}

export const AppConfigProvider = ({ children }: AppConfigProviderProps) => {
  const { intiRemoteConfig, isError, isReady } = useAppInit();

  if (!isReady) {
    return (
      <AppErrorScreen
        isError={isError}
        refreshData={intiRemoteConfig}
      />
    );
  }

  return <>{children}</>;
};
