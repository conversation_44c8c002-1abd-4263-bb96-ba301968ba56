import React from 'react';
import {
  <PERSON><PERSON>ie<PERSON>,
  Config<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  useAppLoadingStore,
} from '@btaskee/design-system';
import LottieView from 'lottie-react-native';

import { appLoadingLottie } from '@lottie';

import { styles } from './styles';

const SIZE_IMAGE = Math.ceil(DeviceHelper.WINDOW.WIDTH / 3);

interface AppLoadingProviderProps {
  children: React.ReactNode;
}

export const AppLoadingProvider = ({ children }: AppLoadingProviderProps) => {
  const { isLoading } = useAppLoadingStore();
  return (
    <>
      {children}
      {Boolean(isLoading) && !ConfigHelpers.isE2ETesting ? (
        <BlockView style={styles.container}>
          <LottieView
            style={{ width: SIZE_IMAGE, height: SIZE_IMAGE }}
            source={appLoadingLottie}
            autoPlay
            loop
          />
        </BlockView>
      ) : null}
    </>
  );
};
