import pkg from '@btaskee/sdk';
import * as Repack from '@callstack/repack';
import { ReanimatedPlugin } from '@callstack/repack-plugin-reanimated';
import path from 'node:path';
import { fileURLToPath } from 'node:url';

const { getLocalIP, getSharedDependencies } = pkg;

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Rspack configuration enhanced with Re.Pack defaults for React Native.
 *
 * Learn about Rspack configuration: https://rspack.dev/config/
 * Learn about Re.Pack configuration: https://re-pack.dev/docs/guides/configuration
 */
export default (env) => {
  const { mode, platform = process.env.PLATFORM } = env;
  const hostIP = getLocalIP();
  // const hostIP = '**************';

  return {
    mode,
    context: __dirname,
    entry: './index.js',
    experiments: {
      incremental: mode === 'development',
    },
    resolve: {
      ...Repack.getResolveOptions(),
      alias: {
        '@navigation': path.resolve(__dirname, './src/navigation'),
        '@screens': path.resolve(__dirname, './src/screens'),
        '@app': path.resolve(__dirname, './src/App.tsx'),
        '@hooks': path.resolve(__dirname, './src/hooks'),
        '@images': path.resolve(__dirname, './src/assets/images'),
        '@lottie': path.resolve(__dirname, './src/assets/lottie'),
        '@components': path.resolve(__dirname, './src/components'),
        '@utils': path.resolve(__dirname, './src/utils'),
        '@types': path.resolve(__dirname, './src/types'),
        '@stores': path.resolve(__dirname, './src/stores'),
        '@providers': path.resolve(__dirname, './src/providers'),
      },
    },
    output: {
      uniqueName: 'sas-host',
      path: path.resolve(__dirname, 'dist', platform),
    },
    module: {
      rules: [
        ...Repack.getJsTransformRules(),
        ...Repack.getAssetTransformRules(),
      ],
    },
    plugins: [
      new Repack.RepackPlugin(),
      new ReanimatedPlugin(),
      new Repack.plugins.ModuleFederationPluginV2({
        name: 'host',
        dts: false,
        remotes: {
          payment: `payment@https://payment-347.firebaseapp.com/v1.0/payment.container.js.bundle`,
          elderlyCare: `elderlyCare@https://elderly-care-347.firebaseapp.com/v2.0/elderly-care.container.js.bundle`,
          community: `community@https://community-347.firebaseapp.com/community.container.js.bundle`,
          // taskManagement: `taskManagement@https://task-management-347.firebaseapp.com/task-management.container.js.bundle`,
          taskManagement: `taskManagement@http://${hostIP}:9011/${platform}/mf-manifest.json`,
          patientCare: `patientCare@https://patient-care-347.firebaseapp.com/patient-care.container.js.bundle`,
          promotion: `promotion@https://promotion-347.firebaseapp.com/promotion.container.js.bundle`,
          auth: `auth@https://auth-347.firebaseapp.com/auth.container.js.bundle`,
          // auth: `auth@http://${hostIP}:9001/${platform}/mf-manifest.json`,
          // cleaning: `cleaning@http://${hostIP}:9002/${platform}/mf-manifest.json`,
          cleaning: `cleaning@https://cleaning-347.firebaseapp.com/cleaning.container.js.bundle`,
          subscriptionCleaning: `subscriptionCleaning@https://cleaning-subscription-347.firebaseapp.com/cleaning-subscription.container.js.bundle`,
          // subscriptionCleaning: `subscriptionCleaning@http://${hostIP}:9010/${platform}/mf-manifest.json`,
          airConditioner: `airConditioner@https://ac-347.firebaseapp.com/air-conditioner.container.js.bundle`,
          // deepCleaning: `deepCleaning@https://deep-cleaning-347.firebaseapp.com/deep-cleaning.container.js.bundle`,
          deepCleaning: `deepCleaning@http://${hostIP}:9006/${platform}/mf-manifest.json`,
          voiceChat: `voiceChat@https://voice-chat-47.web.app/voice-chat.container.js.bundle`,
          childCare: `childCare@https://child-care-347.firebaseapp.com/child-care.container.js.bundle`,
          childCareSubscription: `childCareSubscription@https://child-care-subscription-347.firebaseapp.com/child-care-subscription.container.js.bundle`,
          // officeCleaning: `officeCleaning@https://office-cleaning-347.firebaseapp.com/office-cleaning.container.js.bundle`,
          officeCleaning: `officeCleaning@http://${hostIP}:9014/${platform}/mf-manifest.json`,
          elderlyCareSubscription: `elderlyCareSubscription@https://elderly-care-subscription-347.firebaseapp.com/elderly-care-subscription.container.js.bundle`,
          patientCareSubscription: `patientCareSubscription@https://patient-care-subscription-347.firebaseapp.com/patient-care-subscription.container.js.bundle`,
          // officeCleaningSubscription: `officeCleaningSubscription@https://office-cleaning-subscription-347.firebaseapp.com/office-cleaning-subscription.container.js.bundle`,
          officeCleaningSubscription: `officeCleaningSubscription@http://${hostIP}:9017/${platform}/mf-manifest.json`,
          // waterHeater: `waterHeater@https://water-heater-347.firebaseapp.com/water-heater.container.js.bundle`,
          waterHeater: `waterHeater@http://${hostIP}:9019/${platform}/mf-manifest.json`,
          washingMachine: `washingMachine@https://washing-machine-347.firebaseapp.com/washing-machine.container.js.bundle`,
          disinfection: `disinfection@https://disinfection-347.firebaseapp.com/disinfection.container.js.bundle`,
          // homeCooking: `homeCooking@https://home-cooking-347.firebaseapp.com/home-cooking.container.js.bundle`,
          homeCooking: `homeCooking@http://${hostIP}:9022/${platform}/mf-manifest.json`,
          officeCarpetCleaning: `officeCarpetCleaning@https://office-carpet-cleaning-347.firebaseapp.com/office-carpet-cleaning.container.js.bundle`,
          // officeCarpetCleaning: `officeCarpetCleaning@http://${hostIP}:9023/${platform}/mf-manifest.json`,
          industrialCleaning: `industrialCleaning@https://industrial-cleaning-347.firebaseapp.com/industrial-cleaning.container.js.bundle`,
          // industrialCleaning: `industrialCleaning@http://${hostIP}:9024/${platform}/mf-manifest.json`,
          sofaCleaning: `sofaCleaning@https://sofa-cleaning-347.firebaseapp.com/sofa-cleaning.container.js.bundle`,
          massage: `massage@https://massage-347.firebaseapp.com/massage.container.js.bundle`,
          sofaCleaningThailand: `sofaCleaningThailand@https://sofa-cleaning-thailand-347.firebaseapp.com/sofa-cleaning-thailand.container.js.bundle`,
          ironing: `ironing@https://ironing-347.firebaseapp.com/ironing.container.js.bundle`,
          // laundry: `laundry@https://laundry-347.firebaseapp.com/laundry.container.js.bundle`,
          laundry: `laundry@http://${hostIP}:9029/${platform}/mf-manifest.json`,
          homeMoving: `homeMoving@http://${hostIP}:9030/${platform}/mf-manifest.json`,
          // homeMoving: `homeMoving@https://home-moving-347.firebaseapp.com/homeMoving.container.js.bundle`,
          // housekeeping: `housekeeping@https://housekeeping-347.firebaseapp.com/housekeeping.container.js.bundle`,
          housekeeping: `housekeeping@http://${hostIP}:9031/${platform}/mf-manifest.json`,
          // account: `account@https://account-347.firebaseapp.com/account.container.js.bundle`,
          account: `account@http://${hostIP}:9032/${platform}/mf-manifest.json`,
          // chat: `chat@https://chat-347.firebaseapp.com/chat.container.js.bundle`,
          chat: `chat@http://${hostIP}:9034/${platform}/mf-manifest.json`,
          bReward: `bReward@https://breward-347.firebaseapp.com/bReward.container.js.bundle`,
          beautyCare: `beautyCare@https://beauty-care-347.firebaseapp.com/beauty-care.container.js.bundle`,
          comboVoucher: `comboVoucher@https://combo-voucher-147.firebaseapp.com/combo-voucher.container.js.bundle`,
          map: `map@https://map-147.firebaseapp.com/map.container.js.bundle`,
          // map: `map@http://${hostIP}:9037/${platform}/mf-manifest.json`,
          quickPostTask: `quickPostTask@https://quick-post-task-147.firebaseapp.com/quick-post-task.container.js.bundle`,
          b2b: `b2b@http://${hostIP}:9038/${platform}/mf-manifest.json`,
        },
        shared: getSharedDependencies({ eager: true }),
      }),
    ],
  };
};
