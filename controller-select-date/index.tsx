import React from 'react';
import { FieldPath, FieldValues, useController } from 'react-hook-form';

import { SelectDate } from '@components/common/select-date';

import { ControllerSelectDateProps } from './type';

export const ControllerSelectDate: <
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>(
  props: ControllerSelectDateProps<TFieldValues, TName>,
) => React.ReactNode = ({ name, control, defaultValue, ...props }) => {
  const {
    field: { onChange, value },
  } = useController({ name, control, defaultValue });

  return (
    <SelectDate
      value={value}
      onchange={(dateString) => {
        onChange(dateString);
      }}
      {...props}
    />
  );
};
