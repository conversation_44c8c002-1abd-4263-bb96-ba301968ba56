import React, { useMemo, useRef, useState } from 'react';
import { useController } from 'react-hook-form';
import { StyleSheet } from 'react-native';
import isEqual from 'lodash/isEqual';

import { ModalBottomList } from '@components/common/modal-bottom-list';
import { SelectValue } from '@components/common/select-value';
import { ModalizeHandle } from '@components/customs/modalize/type';
import { TextInput } from '@components/customs/text-input';
import { View } from '@components/customs/view';
import { useTranslation } from '@hooks/useTranslations';
import { CityModel } from '@models/address/city.model';
import { DistrictModel } from '@models/address/district.model';
import { WardModel } from '@models/address/ward.model';
import { useSettings } from '@redux/settings/useSettings';

import { ControllerAddressProps } from './type';

export const ControllerAddress: (props: ControllerAddressProps) => React.ReactNode = ({
  name,
  control,
  defaultValue,
}) => {
  const { t } = useTranslation();
  const { listCity, listDistrict, listWard } = useSettings();
  const [dataModal, setDataModal] = useState<(CityModel | DistrictModel | WardModel)[]>([]);
  const modalBottomRef = useRef<ModalizeHandle>(null);
  const [placeholderSearchModal, setPlaceholderSearchModal] = useState('');
  const [titleModal, setTitleModal] = useState('');

  const {
    field: { onChange, value: valueForm = {} },
    formState: { errors },
  } = useController({ name, control, defaultValue });
  const displayValueCity = useMemo(() => {
    return listCity.find((item) => valueForm?.city?.id === item.id)?.name || '';
  }, [valueForm?.city?.id, listCity]);

  const displayValueDistrict = useMemo(() => {
    return listDistrict.find((item) => valueForm?.district?.id === item.id)?.name || '';
  }, [valueForm?.district?.id, listDistrict]);

  const displayValueWard = useMemo(() => {
    return listWard.find((item) => valueForm?.ward?.id === item.id)?.name || '';
  }, [valueForm?.ward?.id, listWard]);

  const onPressCity = () => {
    setPlaceholderSearchModal(t('SEARCH_CITY_NAME'));
    setTitleModal(t('SELECT_FIELD', { name: t('CITY') }));
    setDataModal(listCity);
    modalBottomRef.current?.open();
  };

  const onPressDistrict = () => {
    setPlaceholderSearchModal(t('SEARCH_DISTRICT_NAME'));
    setTitleModal(t('SELECT_FIELD', { name: t('DISTRICT') }));
    const cityId = valueForm?.city.id;
    const listMatchDistrict = listDistrict.filter((item) => item.cityId === cityId);
    setDataModal(listMatchDistrict);
    modalBottomRef.current?.open();
  };

  const onPressWard = () => {
    setPlaceholderSearchModal(t('SEARCH_WARD_NAME'));
    setTitleModal(t('SELECT_FIELD', { name: t('WARD') }));
    const districtId = valueForm?.district.id;
    const listMatchWard = listWard.filter((item) => item.districtId === districtId);
    setDataModal(listMatchWard);
    modalBottomRef.current?.open();
  };

  const onPressItem = (item: CityModel | DistrictModel | WardModel) => {
    if (CityModel.isModel(item)) {
      onChange({
        ...valueForm,
        city: item,
        district: null,
        ward: null,
      });
    }
    if (DistrictModel.isModel(item)) {
      onChange({
        ...valueForm,
        district: item,
        ward: null,
      });
    }
    if (WardModel.isModel(item)) {
      onChange({
        ...valueForm,
        ward: item,
      });
    }
    modalBottomRef.current?.close();
  };

  const onChangeStreet = (text: string) => {
    onChange({
      ...valueForm,
      street: text,
    });
  };
  const onChangeAddressNo = (text: string) => {
    onChange({
      ...valueForm,
      addressNo: text,
    });
  };

  return (
    <>
      <View style={styles.container}>
        <SelectValue
          label={t('CITY')}
          isRequire
          value={displayValueCity}
          onPress={onPressCity}
          errorText={errors[name]?.city?.message}
        />
        <SelectValue
          label={t('DISTRICT')}
          isRequire
          value={displayValueDistrict}
          onPress={onPressDistrict}
          editable={!!valueForm?.city?.id}
          errorText={errors[name]?.district?.message}
        />
        <SelectValue
          label={t('WARD')}
          isRequire
          value={displayValueWard}
          onPress={onPressWard}
          editable={!!valueForm?.district?.id}
          errorText={errors[name]?.ward?.message}
        />
        <TextInput
          label={t('STREET')}
          isRequire
          value={valueForm.street}
          editable={!!valueForm?.ward?.id}
          onChangeText={onChangeStreet}
          errorText={errors[name]?.street?.message}
        />
        <TextInput
          label={t('ADDRESS_NO')}
          value={valueForm.addressNo}
          editable={!!valueForm?.street}
          onChangeText={onChangeAddressNo}
        />
      </View>
      <ModalBottomList
        ref={modalBottomRef}
        title={titleModal}
        data={dataModal}
        onPressItem={onPressItem}
        placeholderSearch={placeholderSearchModal}
        itemAsChecked={(item) => {
          let checked = false;
          if (CityModel.isModel(item)) {
            checked = isEqual(valueForm?.city, item);
          }
          if (DistrictModel.isModel(item)) {
            checked = isEqual(valueForm?.district, item);
          }
          if (WardModel.isModel(item)) {
            checked = isEqual(valueForm?.ward, item);
          }
          return checked;
        }}
        itemAsString={(item) => item.name || ''}
      />
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
});
