import React, { forwardRef, useEffect, useImperativeHandle, useRef } from 'react';
import { useForm } from 'react-hook-form';
import { StyleSheet } from 'react-native';
import { yupResolver } from '@hookform/resolvers/yup';
import moment from 'moment';

import { Box } from '@components/common/box';
import { ConditionView } from '@components/common/condition-view';
import { ModalBottomList } from '@components/common/modal-bottom-list';
import { ModalBottomListHandle } from '@components/common/modal-bottom-list/type';
import { SizedBox } from '@components/common/sized-box';
import { ControllerFormRadio } from '@components/controllers/controller-form-radio';
import { ControllerSelectDate } from '@components/controllers/controller-select-date';
import { ControllerSelectValue } from '@components/controllers/controller-select-value';
import { ControllerTextInput } from '@components/controllers/controller-text-input';
import { Text } from '@components/customs/text';
import { View } from '@components/customs/view';
import { DateTimeHelpers, FormatDate } from '@helpers/date-time.helpers';
import { FormatHelpers } from '@helpers/format.helpers';
import { useTranslation } from '@hooks/useTranslations';
import { useSettings } from '@redux/settings/useSettings';
import { useSchema } from '@screens/medical-register-screen/hooks/useSchema';
import { COLORS } from '@theme/colors';
import { SIZES } from '@theme/sizes';

import { TitleBlock } from '../title-block';
import { HealthInformationPageHandle, HealthInformationPageProps } from './type';

export const HealthInformationPage = forwardRef<
  HealthInformationPageHandle,
  HealthInformationPageProps
>(({}, ref) => {
  const { t } = useTranslation();
  const { activeServicePack, listVisitTime } = useSettings();
  const { schemaHealthInformation } = useSchema();

  const modalListRef = useRef<ModalBottomListHandle>(null);

  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
    setValue,
    watch,
  } = useForm({
    resolver: yupResolver(schemaHealthInformation),
    mode: 'onChange',
  });

  const visitDateValue = watch('visitDate');
  const visitTimeValue = watch('visitTime');
  const servicePackValue = watch('servicePack');

  useEffect(() => {
    if (visitDateValue && visitTimeValue?.endTime) {
      if (
        DateTimeHelpers.checkTodayOverTimeDate(
          visitTimeValue.endTime,
          moment(visitDateValue, FormatDate.client).format(),
        )
      ) {
        setValue('visitTime', null);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visitDateValue]);

  useImperativeHandle(ref, () => ({
    handleSubmit,
  }));

  return (
    <>
      <View>
        <SizedBox height={SIZES.VIEW.PADDING_TOP} />
        <Box paddingVertical={10}>
          <TitleBlock title={t('TITLE_STEP_2_HEALTH')} isChecked={isValid} />
          <SizedBox height={25} />
          <ControllerSelectDate
            control={control}
            name="visitDate"
            isRequire
            label={t('VISIT_DATE_TIME')}
            calendarProps={{ minDate: moment(new Date()).format(FormatDate.valueCalendar) }}
            errorText={errors.visitDate?.message}
          />
          <ControllerFormRadio
            control={control}
            name="visitTime"
            isRequire
            label={t('VISIT_TIME')}
            data={listVisitTime}
            itemAsString={(item) => item.name as string}
            itemAsChecked={(item) => item.id === visitTimeValue?.id}
            errorText={errors.visitTime?.message}
            itemAsEditable={(item) =>
              !DateTimeHelpers.checkTodayOverTimeDate(
                item.endTime,
                moment(visitDateValue, FormatDate.client).format(),
              )
            }
          />
          <ControllerSelectValue
            control={control}
            name="servicePack"
            isRequire
            label={t('SERVICE_PACK')}
            onPress={() => {
              modalListRef.current?.open();
            }}
            valueAsString={(item) => item?.sPName as string}
            errorText={errors.servicePack?.message}
          />
          <ConditionView
            condition={!!servicePackValue?.price}
            viewTrue={
              <>
                <View
                  paddingHorizontal={25}
                  paddingVertical={10}
                  style={styles.descriptionContainer}
                >
                  <View flexDirection="row" paddingVertical={5}>
                    <Text type="boldNormal">{`${t('PRICE')}: `}</Text>
                    <Text type="boldNormal" color={COLORS.ERROR}>
                      {t('PRICE_CODE', {
                        price: FormatHelpers.formatMoney(servicePackValue?.price),
                      })}
                    </Text>
                  </View>
                  <ConditionView
                    condition={!!servicePackValue?.servicePackDetails?.length}
                    viewTrue={
                      <View paddingVertical={5}>
                        <Text type="boldNormal">{t('SERVICE_PACK_DETAIL')}</Text>
                        {servicePackValue?.servicePackDetails?.map((e) => {
                          return (
                            <View key={e.sPName}>
                              <SizedBox height={5} />
                              <Text key={e.id} type="fontNormal">
                                - {e?.sPName}
                              </Text>
                            </View>
                          );
                        })}
                      </View>
                    }
                  />
                  <ConditionView
                    condition={
                      !!servicePackValue?.description &&
                      !servicePackValue?.servicePackDetails?.length
                    }
                    viewTrue={
                      <View flexDirection="row" paddingVertical={5}>
                        <Text type="boldNormal">{`${t('DESCRIBE')}: `}</Text>
                        <Text type="fontNormal">{servicePackValue?.description}</Text>
                      </View>
                    }
                  />
                </View>
                <SizedBox height={30} />
              </>
            }
          />

          <ControllerTextInput
            control={control}
            name="notes"
            label={t('SYMPTOM')}
            marginBottom={0}
            multiline
          />
        </Box>
        <SizedBox height={SIZES.VIEW.PADDING_BOTTOM} />
      </View>
      <ModalBottomList
        ref={modalListRef}
        data={activeServicePack}
        title={t('SELECT_FIELD', { name: t('SERVICE_PACK') })}
        placeholderSearch={t('SEARCH_SERVICE_PACK_NAME')}
        itemAsString={(item) => item.sPName as string}
        onPressItem={(item) => {
          setValue('servicePack', item);
          modalListRef.current?.close();
        }}
        itemAsChecked={(item) => item.id === servicePackValue?.id}
      />
    </>
  );
});

const styles = StyleSheet.create({
  container: {},
  descriptionContainer: {
    borderWidth: 1,
    borderRadius: 5,
    borderColor: COLORS.BORDER_INPUT,
  },
});
