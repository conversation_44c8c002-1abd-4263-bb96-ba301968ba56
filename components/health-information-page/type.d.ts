import { UseFormHandleSubmit } from 'react-hook-form';

import { ActiveServicePack } from '@models/active-service-pack.model';
import { VisitTimeModel } from '@models/visit-time.model';
import { Maybe } from '@src/types';

export interface HealthInformationPageProps {}

export interface HealthInformationPageHandle {
  handleSubmit: UseFormHandleSubmit<FormValuesHealthInformation, undefined>;
}

export type FormValuesHealthInformation = {
  visitDate?: string;
  visitTime?: Maybe<VisitTimeModel>;
  servicePack?: ActiveServicePack;
  notes?: string;
};
