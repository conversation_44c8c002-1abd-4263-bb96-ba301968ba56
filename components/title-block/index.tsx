import * as React from 'react';
import AntDesign from 'react-native-vector-icons/AntDesign';

import { ConditionView } from '@components/common/condition-view';
import { RequireIcon } from '@components/common/require-icon';
import { SizedBox } from '@components/common/sized-box';
import { Text } from '@components/customs/text';
import { View } from '@components/customs/view';
import { COLORS } from '@theme/colors';

import { TitleBlockProps } from './type';

export const TitleBlock = ({ title, isChecked, isRequire }: TitleBlockProps) => {
  return (
    <View flexDirection="row" alignItems="center">
      <View flexDirection="row">
        <Text type="boldTitle" textDecorationLine="underline">
          {title}
        </Text>
        <ConditionView condition={isRequire} viewTrue={<RequireIcon />} />
      </View>
      <ConditionView
        condition={isChecked}
        viewTrue={
          <>
            <SizedBox width={15} />
            <AntDesign name="checkcircle" size={16} color={COLORS.SUCCESS} />
          </>
        }
      />
    </View>
  );
};
