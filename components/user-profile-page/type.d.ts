import { UseFormHandleSubmit } from 'react-hook-form';

import { Gender } from '@constants/index';
import { IAdd<PERSON>, Maybe } from '@src/types';

export interface UserProfilePageProps {}
export interface UserProfilePageHandle {
  handleSubmit: UseFormHandleSubmit<FormValuesUserProfile, undefined>;
}

export type FormValuesUserProfile = {
  patientId?: Maybe<number>;
  fileNum?: string;
  fullName: string;
  gender: Maybe<Gender>;
  dob: string;
  mobile: string;
  email?: Maybe<string>;
  address?: IAddress;
};
