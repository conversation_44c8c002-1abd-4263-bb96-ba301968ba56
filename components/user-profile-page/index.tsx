import React, { forwardRef, useEffect, useImperativeHand<PERSON>, useState } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import moment from 'moment';

import { Api } from '@api/index';
import { IconButton } from '@components/common/ icon-button';
import { AvatarList } from '@components/common/avatar-list';
import { Box } from '@components/common/box';
import { SelectItemsProps } from '@components/common/select-items/type';
import { SizedBox } from '@components/common/sized-box';
import { ControllerAddress } from '@components/controllers/controller-address';
import { ControllerFormRadio } from '@components/controllers/controller-form-radio';
import { ControllerSelectDate } from '@components/controllers/controller-select-date';
import { ControllerTextInput } from '@components/controllers/controller-text-input';
import { ScrollView } from '@components/customs/scroll-view';
import { Text } from '@components/customs/text';
import { View } from '@components/customs/view';
import { Gender } from '@constants/index';
import { FormatDate } from '@helpers/date-time.helpers';
import { useConstants } from '@hooks/useConstant';
import { useTranslation } from '@hooks/useTranslations';
import { UserProfileModel } from '@models/user-profile.model';
import { useSchema } from '@screens/medical-register-screen/hooks/useSchema';
import { Maybe } from '@src/types';
import { COLORS } from '@theme/colors';
import { SIZES } from '@theme/sizes';

import { TitleBlock } from '../title-block';
import { FormValuesUserProfile, UserProfilePageHandle, UserProfilePageProps } from './type';

const DEFAULT_VALUE: FormValuesUserProfile = {
  patientId: null,
  fileNum: '',
  fullName: '',
  gender: null,
  dob: '',
  mobile: '',
  address: {},
  email: '',
};

export const UserProfilePage = forwardRef<UserProfilePageHandle, UserProfilePageProps>(
  ({}, ref) => {
    const { t } = useTranslation();
    const { genderList } = useConstants();
    const { schemaUserProfile } = useSchema();
    const [listUserProfile, setListUserProfile] = useState<UserProfileModel[]>([]);
    const [profileSelected, setProfileSelected] = useState<Maybe<UserProfileModel>>(null);
    const [loadingProfile, setLoadingProfile] = useState(true);

    const {
      control,
      handleSubmit,
      formState: { errors, isValid },
      setValue,
      watch,
      reset,
    } = useForm({
      resolver: yupResolver(schemaUserProfile),
      mode: 'onChange',
    });

    const genderValue = watch('gender');

    useImperativeHandle(ref, () => ({
      handleSubmit,
    }));

    useEffect(() => {
      getListUserProfile();
    }, []);

    const getListUserProfile = async () => {
      try {
        const res = await Api.getLogInUserProfile({ profileOnly: true });
        setListUserProfile(res.data || []);
      } catch (error) {
      } finally {
        setLoadingProfile(false);
      }
    };

    const onPressItemProfile: SelectItemsProps<UserProfileModel>['onPressItem'] = (item) => {
      setProfileSelected(item);
      reset({
        patientId: item.patientId,
        fileNum: item.fileNum || '',
        fullName: item.fullName || '',
        gender: item.gender as Gender,
        dob: moment(item.dob).format(FormatDate.client),
        mobile: item.mobile || '',
        address: {
          city: { id: item.cityId, name: item.city },
          district: { id: item.districtId, name: item.district },
          ward: { id: item.wardId, name: item.ward },
          street: item.street || '',
          addressNo: item.addressNo || '',
        },
        email: item.email as string,
      });
    };

    const onRefreshProfile = () => {
      setProfileSelected(null);
      reset(DEFAULT_VALUE);
    };

    return (
      <>
        <ScrollView>
          <SizedBox height={SIZES.VIEW.PADDING_TOP} />
          <Box paddingVertical={10}>
            <TitleBlock title={t('SELECT_OLD_PROFILE')} />
            <SizedBox height={10} />
            <AvatarList
              data={listUserProfile}
              itemAsString={(item) => item.fullName as string}
              keyExtractor={(item) => item.fileNum as string}
              itemAsSelected={(item) => item.fileNum === profileSelected?.fileNum}
              onPressItem={onPressItemProfile}
              loading={loadingProfile}
            />
            <SizedBox height={SIZES.VIEW.PADDING_HORIZONTAL} />
            <View flexDirection="row" justifyContent="flex-end">
              <IconButton
                backgroundColor={COLORS.LIGHT_GREY}
                justifyContent="flex-start"
                alignItems="flex-start"
                paddingHorizontal={15}
                paddingVertical={5}
                style={{ borderRadius: SIZES.BUTTON.RADIUS }}
                onPress={onRefreshProfile}
              >
                <Text type="fontNormal">{t('REFRESH_PROFILE')}</Text>
              </IconButton>
            </View>
          </Box>
          <SizedBox height={SIZES.VIEW.PADDING_HORIZONTAL} />
          <Box paddingVertical={10}>
            <TitleBlock title={t('TITLE_STEP_1')} isChecked={isValid} />
            <SizedBox height={25} />
            <ControllerTextInput
              control={control}
              name="fileNum"
              label={t('PATIENT_CODE')}
              keyboardType="numeric"
              editable={!profileSelected?.fileNum}
            />
            <ControllerTextInput
              control={control}
              name="fullName"
              isRequire
              label={t('FULL_NAME')}
              errorText={errors.fullName?.message}
            />
            <ControllerFormRadio
              control={control}
              name="gender"
              label={t('GENDER')}
              isRequire
              data={genderList}
              itemAsString={(item) => item.displayValue || ''}
              itemAsChecked={(item) => item.key === genderValue}
              onPressItem={(item) => {
                setValue('gender', item.key);
              }}
              errorText={errors.gender?.message}
            />
            <ControllerSelectDate
              control={control}
              name="dob"
              isRequire
              label={t('DOB')}
              titleModal={t('CHOOSE_DOB')}
              calendarProps={{
                maxDate: moment().format(FormatDate.valueCalendar),
              }}
              errorText={errors.dob?.message}
            />
            <ControllerTextInput
              control={control}
              name="mobile"
              isRequire
              label={t('PHONE_NUMBER')}
              errorText={errors.mobile?.message}
              keyboardType="numeric"
            />
            <ControllerAddress control={control} name="address" />
            <ControllerTextInput
              control={control}
              name="email"
              label={t('EMAIL')}
              keyboardType="email-address"
              errorText={errors.email?.message}
            />
          </Box>
          <SizedBox height={SIZES.VIEW.PADDING_BOTTOM} />
        </ScrollView>
      </>
    );
  },
);
