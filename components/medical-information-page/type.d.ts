import { UseFormHandleSubmit } from 'react-hook-form';
import { Maybe } from 'yup';

import { IDoctorScheduleSelectedItem } from '@components/doctor-schedule-item/type';
import { SpecialistModel } from '@models/specialist.model';
import { RouteName } from '@routes/route-name';
import { ParamsNavigationList } from '@routes/types-routes';

export interface MedicalInformationPageProps {
  defaultValue?: ParamsNavigationList[RouteName.MedicalRegister]['medicalInformation'];
}

export interface MedicalInformationPageHandle {
  handleSubmit: UseFormHandleSubmit<FormValuesMedicalInformation, undefined>;
}

export type FormValuesMedicalInformation = {
  visitDate?: string;
  specialist?: SpecialistModel;
  doctorSchedule?: Maybe<IDoctorScheduleSelectedItem>;
  notes?: string;
};
