import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { isEmpty } from 'lodash';
import moment from 'moment';

import { Api } from '@api/index';
import { AvatarList } from '@components/common/avatar-list';
import { Box } from '@components/common/box';
import { ConditionView } from '@components/common/condition-view';
import { RequireIcon } from '@components/common/require-icon';
import { SizedBox } from '@components/common/sized-box';
import { ControllerSelectDate } from '@components/controllers/controller-select-date';
import { ControllerTextInput } from '@components/controllers/controller-text-input';
import { Text } from '@components/customs/text';
import { View } from '@components/customs/view';
import { DoctorScheduleItem } from '@components/doctor-schedule-item';
import { FormatDate } from '@helpers/date-time.helpers';
import { FormatHelpers } from '@helpers/format.helpers';
import { useTranslation } from '@hooks/useTranslations';
import { DoctorScheduleModel } from '@models/doctor-schedule.model';
import { useSettings } from '@redux/settings/useSettings';
import { SkeletonDoctorScheduleItem } from '@screens/doctor-schedule-screen/components/skeleton-doctor-schedule-item';
import { useSchema } from '@screens/medical-register-screen/hooks/useSchema';
import { COLORS } from '@theme/colors';
import { SIZES } from '@theme/sizes';

import { TitleBlock } from '../title-block';
import { MedicalInformationPageHandle, MedicalInformationPageProps } from './type';

export const MedicalInformationPage = forwardRef<
  MedicalInformationPageHandle,
  MedicalInformationPageProps
>(({ defaultValue }, ref) => {
  const { t } = useTranslation();
  const { specialties } = useSettings();
  const { schemaMedicalInformation } = useSchema();

  const [doctorScheduleList, setDoctorScheduleList] = useState<DoctorScheduleModel[]>([]);
  const [loading, setLoading] = useState(false);

  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
    setValue,
    watch,
  } = useForm({
    resolver: yupResolver(schemaMedicalInformation),
    mode: 'onChange',
    defaultValues: {
      visitDate: defaultValue?.visitDate,
      specialist: defaultValue?.specialist,
      doctorSchedule: defaultValue?.itemDoctorSelected,
    },
  });

  const visitDateValue = watch('visitDate');
  const specialistValue = watch('specialist');
  const doctorScheduleValue = watch('doctorSchedule');

  useImperativeHandle(ref, () => ({
    handleSubmit,
  }));

  useEffect(() => {
    setValue('doctorSchedule', null);
    getDoctorSchedules();

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visitDateValue, specialistValue?.id]);

  useEffect(() => {
    if (defaultValue?.itemDoctorSelected) {
      setValue('doctorSchedule', defaultValue?.itemDoctorSelected);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [doctorScheduleList]);

  const getDoctorSchedules = async () => {
    try {
      setLoading(true);
      const specialityId = specialistValue?.id;
      if (visitDateValue && specialityId) {
        const date = moment(visitDateValue, FormatDate.client).format(FormatDate.server);
        const res = await Api.getDoctorSchedules({
          endDate: date,
          startDate: date,
          specialityId,
        });
        setDoctorScheduleList(res.data || []);
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <SizedBox height={SIZES.VIEW.PADDING_TOP} />
      <Box paddingVertical={10}>
        <TitleBlock title={t('SPECIALIST')} isRequire />
        <SizedBox height={10} />
        <Controller
          control={control}
          name="specialist"
          render={({ field: { onChange, value } }) => {
            return (
              <>
                <AvatarList
                  data={specialties}
                  itemAsString={(item) => item.caption?.trim() as string}
                  keyExtractor={(item) => item.id?.toString() as string}
                  itemAsSelected={(item) => item.id === value?.id}
                  onPressItem={(item) => {
                    onChange(item);
                  }}
                  colorAvatar={COLORS.ORANGE}
                />
                <SizedBox height={10} />
                <View paddingHorizontal={20}>
                  <Text type="fontErrorText" color={COLORS.ERROR} textAlign="right">
                    {errors.specialist?.message}
                  </Text>
                </View>
              </>
            );
          }}
        />
      </Box>
      <SizedBox height={SIZES.VIEW.PADDING_HORIZONTAL} />
      <Box paddingVertical={10}>
        <TitleBlock title={t('TITLE_STEP_2')} isChecked={isValid && !!doctorScheduleValue} />
        <SizedBox height={25} />
        <ControllerSelectDate
          control={control}
          name="visitDate"
          isRequire
          label={t('VISIT_DATE_TIME')}
          calendarProps={{ minDate: moment(new Date()).format(FormatDate.valueCalendar) }}
          errorText={errors.visitDate?.message}
        />
        <ConditionView
          condition={!isEmpty(doctorScheduleList)}
          viewTrue={
            <Controller
              control={control}
              name="doctorSchedule"
              render={({ field: { onChange, value } }) => (
                <View>
                  <View
                    flexDirection="row"
                    paddingHorizontal={20}
                    alignItems="center"
                    justifyContent="space-between"
                  >
                    <View flexDirection="row">
                      <Text type="boldNormal">{t('DOCTOR_SCHEDULE')}</Text>
                      <RequireIcon />
                    </View>
                    <ConditionView
                      condition={!!errors.doctorSchedule?.message}
                      viewTrue={
                        <Text type="fontErrorText" color={COLORS.ERROR}>
                          {errors.doctorSchedule?.message}
                        </Text>
                      }
                      viewFalse={
                        <ConditionView
                          condition={!!doctorScheduleValue}
                          viewTrue={
                            <Text type="boldNormal" color={COLORS.ERROR}>
                              {t('PRICE_CODE', {
                                price: FormatHelpers.formatMoney(
                                  doctorScheduleValue?.doctor?.price,
                                ),
                              })}
                            </Text>
                          }
                        />
                      }
                    />
                  </View>
                  <View flex={1}>
                    <SizedBox height={10} />
                    <ConditionView
                      condition={loading || !doctorScheduleList.length}
                      viewTrue={<SkeletonDoctorScheduleItem />}
                      viewFalse={doctorScheduleList.map((item) => {
                        return (
                          <DoctorScheduleItem
                            key={item.scheduleDate}
                            item={item}
                            onPressItemDoctor={onChange}
                            itemSelected={value}
                          />
                        );
                      })}
                    />
                  </View>
                  <SizedBox height={20} />
                </View>
              )}
            />
          }
        />
        <ControllerTextInput
          control={control}
          name="notes"
          label={t('SYMPTOM')}
          marginBottom={0}
          multiline
        />
      </Box>
      <SizedBox height={SIZES.VIEW.PADDING_BOTTOM} />
    </>
  );
});
