import * as React from 'react';
import { FieldPath, FieldValues, useController } from 'react-hook-form';

import { TextInputPassword } from '../../common/text-input-password';
import { ControllerTextInputProps } from '../controller-text-input/type';

export const ControllerTextInputPassword: <
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>(
  props: ControllerTextInputProps<TFieldValues, TName>,
) => React.ReactNode = ({ name, control, defaultValue, onChangeText, ...props }) => {
  const {
    field: { onChange, value },
  } = useController({ name, control, defaultValue });

  const onChangeTextInput = (text: string) => {
    onChange(text);
    if (onChangeText) {
      onChangeText(text);
    }
  };

  return <TextInputPassword value={value} onChangeText={onChangeTextInput} {...props} />;
};
