import { useMemo } from 'react';
import * as yup from 'yup';

import { Gender, Regex } from '@constants/index';
import { useTranslation } from '@hooks/useTranslations';

import { FormValuesHealthInformation } from '../components/health-information-page/type';
import { FormValuesMedicalInformation } from '../components/medical-information-page/type';
import { FormValuesUserProfile } from '../components/user-profile-page/type';

export const useSchema = () => {
  const { t } = useTranslation();
  const schemaUserProfile: yup.ObjectSchema<FormValuesUserProfile> = useMemo(() => {
    return yup.object({
      patientId: yup.number().nullable(),
      fileNum: yup.string(),
      fullName: yup
        .string()
        .trim()
        .required(
          t('PLS_ENTER', {
            name: t('FULL_NAME'),
          }),
        ),
      gender: yup.mixed<Gender>().required(
        t('PLS_SELECT', {
          name: t('GENDER'),
        }),
      ),
      mobile: yup
        .string()
        .required(
          t('PLS_ENTER', {
            name: t('PHONE_NUMBER'),
          }),
        )
        .matches(Regex.phoneNumberVN, t('INVALID', { name: t('PHONE_NUMBER') })),
      dob: yup.string().required(
        t('PLS_SELECT', {
          name: t('DOB'),
        }),
      ),
      address: yup.object({
        city: yup.object().required(
          t('PLS_SELECT', {
            name: t('CITY'),
          }),
        ),
        district: yup.object().required(
          t('PLS_SELECT', {
            name: t('DISTRICT'),
          }),
        ),
        ward: yup.object().required(
          t('PLS_SELECT', {
            name: t('WARD'),
          }),
        ),
        street: yup.string().required(
          t('PLS_ENTER', {
            name: t('STREET'),
          }),
        ),
      }),
      email: yup
        .string()
        .nullable()
        .email(t('INVALID', { name: t('EMAIL') })),
    });
  }, [t]);

  const schemaMedicalInformation: yup.ObjectSchema<FormValuesMedicalInformation> = useMemo(() => {
    return yup.object({
      visitDate: yup.string().required(
        t('PLS_SELECT', {
          name: t('VISIT_DATE_TIME'),
        }),
      ),
      specialist: yup.object().required(
        t('PLS_SELECT', {
          name: t('SPECIALIST'),
        }),
      ),
      doctorSchedule: yup.object().required(
        t('PLS_SELECT', {
          name: t('DOCTOR_SCHEDULE'),
        }),
      ),
      notes: yup.string(),
    });
  }, [t]);

  const schemaHealthInformation: yup.ObjectSchema<FormValuesHealthInformation> = useMemo(() => {
    return yup.object({
      visitDate: yup.string().required(
        t('PLS_SELECT', {
          name: t('VISIT_DATE_TIME'),
        }),
      ),
      visitTime: yup.object().required(
        t('PLS_SELECT', {
          name: t('VISIT_TIME'),
        }),
      ),
      servicePack: yup.object().required(
        t('PLS_SELECT', {
          name: t('SERVICE_PACK'),
        }),
      ),
      notes: yup.string(),
    });
  }, [t]);

  return { schemaUserProfile, schemaMedicalInformation, schemaHealthInformation };
};
