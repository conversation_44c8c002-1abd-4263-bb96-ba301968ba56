import { FieldPath, FieldValues, PathValue, UseControllerProps } from 'react-hook-form';

import { SelectValueProps } from '@components/common/select-value/type';

export type ControllerSelectValueProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
> = SelectValueProps &
  UseControllerProps<TFieldValues, TName> & {
    valueAsString?: (value: PathValue<TFieldValues, TName>) => string;
  };
