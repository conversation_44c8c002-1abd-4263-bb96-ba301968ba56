import React, { useEffect } from 'react';
import { FieldPath, FieldValues, useController } from 'react-hook-form';

import { SelectValue } from '@components/common/select-value';

import { ControllerSelectValueProps } from './type';

export const ControllerSelectValue: <
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>(
  props: ControllerSelectValueProps<TFieldValues, TName>,
) => React.ReactNode = ({ name, control, defaultValue, valueAsString, ...props }) => {
  const {
    field: { onChange, value: valueForm },
  } = useController({ name, control, defaultValue });
  const displayValue = typeof valueForm === 'string' ? valueForm : valueAsString?.(valueForm);
  useEffect(() => {
    onChange(valueForm);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [displayValue]);

  return <SelectValue value={displayValue} {...props} />;
};
