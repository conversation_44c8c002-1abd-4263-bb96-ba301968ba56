import {
  Alert,
  DateTimeHelpers,
  <PERSON><PERSON><PERSON><PERSON>per,
  Endpoint<PERSON>eys,
  getPhoneNumber,
  handleError,
  IApiError,
  PaymentService,
  PostTaskHelpers,
  unFormatMoney,
  useApiMutation,
  useAppLoadingStore,
  useAppStore,
  usePostTaskAction,
  useUserStore,
} from '@btaskee/design-system';
import { isEmpty } from 'lodash-es';

import { usePostTaskStore } from '@stores';

import { useI18n } from './useI18n';
import { useTracking } from './useTracking';

// Utility functions for building task data
const buildCookingDetailData = (cookingData: {
  numberOfPeopleEating?: number;
  numberOfDish?: number;
  isGoMarket?: boolean;
  haveFruit?: boolean;
  arrivalTime?: any;
  listOfDish?: any;
  taste?: any;
  costGoMarket?: any;
}) => {
  const {
    numberOfPeopleEating,
    numberOfDish,
    isGoMarket,
    haveFruit,
    arrivalTime,
    listOfDish,
    taste,
    costGoMarket,
  } = cookingData;

  const detailData: any = {
    numberEater: numberOfPeopleEating,
    numberDish: numberOfDish,
    isGoMarket: Boolean(isGoMarket),
    haveFruit: Boolean(haveFruit),
    eatingTime: arrivalTime,
  };

  if (listOfDish?.length > 0) {
    detailData.listOfDish = listOfDish;
  }

  if (taste?.length > 0) {
    detailData.taste = taste;
  }

  if (costGoMarket && Number(costGoMarket) > 0) {
    detailData.costGoMarket = unFormatMoney(costGoMarket.toString());
  }

  return detailData;
};

export const usePostTask = () => {
  const { setPrice, setLoadingPrice, resetState, setIsBookedTask } =
    usePostTaskStore();
  const { trackingPostTaskSuccess } = useTracking();
  const { showLoading, hideLoading } = useAppLoadingStore();
  const { handlePostTaskError } = usePostTaskAction();
  const { user } = useUserStore();
  const { t } = useI18n();
  const { isoCode } = useAppStore();

  const { mutate: getPriceHomeCooking } = useApiMutation({
    key: EndpointKeys.getPriceHomeCooking,
    options: {
      onMutate: () => {
        setLoadingPrice(true);
        showLoading();
      },
      onSettled: () => {
        setLoadingPrice(false);
        hideLoading();
      },
    },
  });

  const { mutate: postTaskHomeCooking } = useApiMutation({
    key: EndpointKeys.postTaskHomeCooking,
    options: {
      onSuccess: async (data: any) => {
        if (data?.bookingId) {
          trackingPostTaskSuccess();
          resetState();
          setIsBookedTask(true);
          await PaymentService.onPostTaskSuccess({
            bookingId: data.bookingId,
            isPrepayment: data.isPrepayment,
          });
        }
      },
      onError: (error: IApiError) => {
        handlePostTaskError(error);
      },
      onMutate: () => {
        showLoading();
      },
      onSettled() {
        hideLoading();
      },
    },
  });

  const { mutate: checkTaskSameTime } = useApiMutation({
    key: EndpointKeys.checkTaskSameTime,
  });

  const { mutate: bookTaskForceTasker } =
    useApiMutation<EndpointKeys.bookTaskForceTasker>({
      key: EndpointKeys.bookTaskForceTasker,
      options: {
        onSuccess: async (data: any) => {
          if (data?.bookingId) {
            resetState();
            await PaymentService.onPostTaskSuccess({
              bookingId: data.bookingId,
              isPrepayment: data.isPrepayment,
            });
          }
        },
        onError: (error: IApiError) => {
          handlePostTaskError(error);
        },
        onMutate: () => {
          showLoading();
        },
        onSettled() {
          hideLoading();
        },
      },
    });

  const { mutate: getPricingTaskDateOptionsAPI } = useApiMutation({
    key: EndpointKeys.getPricingTaskDateOptions,
    options: {
      onMutate: () => {
        setLoadingPrice(true);
        showLoading();
      },
      onSettled: () => {
        setLoadingPrice(false);
        hideLoading();
      },
    },
  });

  const buildPricingData = () => {
    const currentState = usePostTaskStore.getState();
    const {
      address,
      forceTasker,
      date,
      duration,
      isAutoChooseTasker,
      service,
      paymentMethod,
      numberOfPeopleEating,
      numberOfDish,
      isGoMarket,
      haveFruit,
      dateOptions,
      promotion,
    } = currentState;

    if (!address || !date || !duration) {
      return null;
    }

    const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

    const task: any = {
      timezone,
      date: DateTimeHelpers.formatToString({ timezone, date }),
      autoChooseTasker: isAutoChooseTasker,
      taskPlace: {
        country: address?.country,
        city: address?.city,
        district: address?.district,
      },
      homeType: address?.homeType,
      duration,
      cookingDetail: {
        numberEater: numberOfPeopleEating,
        numberDish: numberOfDish,
        isGoMarket: Boolean(isGoMarket),
        haveFruit: Boolean(haveFruit),
        eatingTime: DateTimeHelpers.formatToString({ timezone, date }),
      },
      ...PostTaskHelpers.formatDataToParams({ paymentMethod, promotion }),
    };

    if (dateOptions.length > 1) {
      task.dateOptions = dateOptions.map((dateOption) => ({
        eatingTime: dateOption?.date,
      }));
    }

    if (!task.cookingDetail?.numberEater || !task.cookingDetail?.numberDish) {
      return null;
    }

    if (!isEmpty(forceTasker)) {
      task.forceTasker = forceTasker;
    }
    if (!isEmpty(dateOptions)) {
      task.dateOptions = dateOptions;
    }

    return {
      task,
      service: { _id: service?._id || '' },
      isoCode: isoCode || '',
    };
  };

  const getPrice = async (): Promise<void> => {
    const pricingData = buildPricingData();

    if (!pricingData) {
      setPrice(null);
      return;
    }

    if (!isEmpty(pricingData?.task?.dateOptions)) {
      return getPricingTaskDateOptionsAPI(pricingData, {
        onSuccess: (result: any) => {
          setPrice(result);
        },
        onError: (error: IApiError) => {
          handleError(error);
          setPrice(null);
        },
      });
    }

    setLoadingPrice(true);

    await getPriceHomeCooking(pricingData, {
      onSuccess: (result: any) => {
        setPrice(result);
      },
      onError: (error: IApiError) => {
        handleError(error);
        setPrice(null);
      },
    });

    setLoadingPrice(false);
  };

  const buildTaskData = () => {
    const currentState = usePostTaskStore.getState();
    const {
      address,
      forceTasker,
      dateOptions,
      duration,
      service,
      paymentMethod,
      isApplyNoteForAllTask,
      note,
      isFavouriteTasker,
      relatedTask,
      numberOfPeopleEating,
      numberOfDish,
      isGoMarket,
      haveFruit,
      arrivalTime,
      listOfDish,
      taste,
      pet,
      costGoMarket,
      isEnableSchedule,
      schedule,
      promotion,
    } = currentState;

    const isAddressMaybeWrong = Boolean(address?.isAddressMaybeWrong);
    const isTet = service?.isTet || false;
    const city = address?.city;
    const timezone = DateTimeHelpers.getTimezoneByCity(city);

    const task: any = {
      address: address?.address,
      contactName: address?.contact || user?.name,
      lat: address?.lat,
      lng: address?.lng,
      phone: getPhoneNumber(
        address?.phoneNumber || user?.phone || '',
        address?.countryCode || user?.countryCode || '',
      ),
      countryCode: address?.countryCode || user?.countryCode,
      description: address?.description,
      askerId: user?._id,
      autoChooseTasker: true,
      date: DateTimeHelpers.formatToString({ date: arrivalTime, timezone }),
      timezone,
      deviceInfo: DeviceHelper.getDeviceInfo(),
      duration,
      homeType: address?.homeType,
      houseNumber: address?.description,
      isoCode,
      serviceId: service?._id,
      taskPlace: {
        country: address?.country,
        city,
        district: address?.district,
      },
      isSendToFavTaskers: isFavouriteTasker,
      updateTaskNoteToUser: isApplyNoteForAllTask,
      shortAddress: address?.shortAddress,
      cookingDetail: buildCookingDetailData({
        numberOfPeopleEating,
        numberOfDish,
        isGoMarket,
        haveFruit,
        arrivalTime,
        listOfDish,
        taste,
        costGoMarket,
      }),
      ...PostTaskHelpers.formatDataToParams({ paymentMethod, promotion }),
    };

    // Add optional fields
    if (isAddressMaybeWrong) {
      task.taskPlace.isAddressMaybeWrong = true;
    }

    if (isTet) {
      task.isTetBooking = true;
    }

    if (note?.trim()) {
      task.taskNote = note.trim();
    }

    if (pet) {
      task.pet = pet;
    }

    if (forceTasker?._id) {
      task.forceTasker = {
        taskerId: forceTasker?._id,
        isResent: Boolean((forceTasker as any)?.isResent),
      };
    }

    if (dateOptions) {
      task.dateOptions = dateOptions;
    }

    if (isEnableSchedule && schedule && schedule.length > 0) {
      task.weekday = schedule;
    }

    if (!isEmpty(relatedTask?.relatedTaskId)) {
      task.source = {
        from: relatedTask?.service?.name,
        taskId: relatedTask?.relatedTaskId,
      };
    }

    // Date options for flexible scheduling
    if (dateOptions && dateOptions.length > 1) {
      task.dateOptions = dateOptions;
    }

    // Asker chọn Tasker

    return task;
  };

  const executeTaskPosting = async (): Promise<any> => {
    const taskData = buildTaskData();

    // Clean up dateOptions if needed
    if (isEmpty(taskData?.dateOptions)) {
      delete taskData.dateOptions;
    }

    // Check if this is a force tasker booking
    if (!isEmpty(taskData?.forceTasker)) {
      return bookTaskForceTasker(taskData);
    }

    // Normal home cooking task posting
    return postTaskHomeCooking(taskData);
  };

  const handleSameTimeConflict = async (): Promise<void> => {
    Alert.alert?.open({
      title: t('DIALOG_TITLE_INFORMATION'),
      message: t('TASK_SAME_TIME_MESSAGE'),
      actions: [
        { text: t('CLOSE'), style: 'cancel' },
        {
          text: t('OK'),
          onPress: async () => {
            setTimeout(async () => {
              await executeTaskPosting();
            }, 300);
          },
        },
      ],
    });
  };

  const postTask = async (): Promise<any> => {
    const currentState = usePostTaskStore.getState();
    const { date, service, address } = currentState;
    const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

    // Check for conflicting tasks at the same time
    return checkTaskSameTime(
      {
        taskDate: DateTimeHelpers.formatToString({ date: date!, timezone }),
        serviceId: service?._id || '',
      },
      {
        onSuccess: async (result: any) => {
          if (result === true) {
            return handleSameTimeConflict();
          }
          return executeTaskPosting();
        },
      },
    );
  };
  return { getPrice, postTask };
};
