import {
  <PERSON><PERSON>,
  DateT<PERSON><PERSON>el<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Endpoint<PERSON>eys,
  getPhoneNumber,
  handleError,
  IApiError,
  PaymentService,
  PostTaskHelpers,
  useApiMutation,
  useAppLoadingStore,
  useAppStore,
  usePostTaskAction,
  useUserStore,
} from '@btaskee/design-system';
import { debounce, isEmpty } from 'lodash-es';

import { usePostTaskStore } from '@stores';

import { useI18n } from './useI18n';
import { useTracking } from './useTracking';

export const usePostTask = () => {
  const {
    setPrice,
    setLoadingPrice,
    setDuration,
    resetState,
    setIsBookedTask,
  } = usePostTaskStore();
  const { showLoading, hideLoading } = useAppLoadingStore();
  const { handlePostTaskError } = usePostTaskAction();
  const { user } = useUserStore();
  const { isoCode } = useAppStore();
  const { trackingPostTaskSuccess } = useTracking();
  const { t } = useI18n();

  const { mutate: getPriceWaterHeater } = useApiMutation({
    key: EndpointKeys.getPriceWaterHeater,
    options: {
      onMutate: () => {
        setLoadingPrice(true);
        showLoading();
      },
      onSettled: () => {
        setLoadingPrice(false);
        hideLoading();
      },
    },
  });

  const { mutate: postTaskWaterHeater } = useApiMutation({
    key: EndpointKeys.postTaskWaterHeater,
    options: {
      onSuccess: async (data: any) => {
        if (!data?.bookingId) return;
        trackingPostTaskSuccess();
        resetState();
        setIsBookedTask(true);

        // Payment processing (includes navigation)
        await PaymentService.onPostTaskSuccess({
          bookingId: data.bookingId,
          isPrepayment: data.isPrepayment,
        });
      },
      onError: (error: IApiError) => {
        handlePostTaskError(error);
      },
      onMutate: () => {
        showLoading();
      },
      onSettled() {
        hideLoading();
      },
    },
  });

  const { mutate: checkTaskSameTime } = useApiMutation({
    key: EndpointKeys.checkTaskSameTime,
  });

  const buildPricingData = () => {
    const currentState = usePostTaskStore.getState();
    const {
      address,
      date,
      duration,
      isAutoChooseTasker,
      service,
      paymentMethod,
      tasksWaterHeater,
      promotion,
    } = currentState;

    if (!address || !date || isEmpty(tasksWaterHeater)) {
      return null;
    }

    const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

    const task: any = {
      timezone,
      date: DateTimeHelpers.formatToString({ timezone, date }),
      autoChooseTasker: isAutoChooseTasker,
      taskPlace: {
        country: address?.country,
        city: address?.city,
        district: address?.district,
      },
      homeType: address?.homeType,
      duration,
      detailWaterHeater: tasksWaterHeater,
      ...PostTaskHelpers.formatDataToParams({ paymentMethod, promotion }),
    };

    return {
      task,
      service: { _id: service?._id || '' },
      isoCode: isoCode || '',
    };
  };

  const getPrice = async (): Promise<void> => {
    const pricingData = buildPricingData();

    if (!pricingData) {
      setPrice(null);
      return;
    }

    setLoadingPrice(true);

    await getPriceWaterHeater(pricingData, {
      onSuccess: (result: any) => {
        setPrice(result);
        setDuration(result?.duration || 0);
      },
      onError: (error: IApiError) => {
        handleError(error);
        setPrice(null);
      },
    });

    setLoadingPrice(false);
  };

  const buildTaskData = () => {
    const currentState = usePostTaskStore.getState();
    const {
      address,
      date,
      duration,
      isAutoChooseTasker,
      service,
      paymentMethod,
      isApplyNoteForAllTask,
      note,
      isFavouriteTasker,
      relatedTask,
      tasksWaterHeater,
      promotion,
    } = currentState;

    const isAddressMaybeWrong = Boolean(address?.isAddressMaybeWrong);
    const isTet = service?.isTet || false;
    const city = address?.city;
    const timezone = DateTimeHelpers.getTimezoneByCity(city);

    const task: any = {
      address: address?.address,
      contactName: address?.contact || user?.name,
      lat: address?.lat,
      lng: address?.lng,
      phone: getPhoneNumber(
        address?.phoneNumber || user?.phone || '',
        address?.countryCode || user?.countryCode || '',
      ),
      countryCode: address?.countryCode || user?.countryCode,
      description: address?.description,
      askerId: user?._id,
      autoChooseTasker: isAutoChooseTasker,
      date: DateTimeHelpers.formatToString({ date, timezone }),
      timezone,
      deviceInfo: DeviceHelper.getDeviceInfo(),
      duration,
      homeType: address?.homeType,
      houseNumber: address?.description,
      isoCode,
      serviceId: service?._id,
      taskPlace: {
        country: address?.country,
        city,
        district: address?.district,
      },
      isSendToFavTaskers: isFavouriteTasker,
      updateTaskNoteToUser: isApplyNoteForAllTask,
      shortAddress: address?.shortAddress,
      detailWaterHeater: tasksWaterHeater,
      ...PostTaskHelpers.formatDataToParams({ paymentMethod, promotion }),
    };

    // Add optional fields
    if (isAddressMaybeWrong) {
      task.taskPlace.isAddressMaybeWrong = true;
    }

    if (isTet) {
      task.isTetBooking = true;
    }

    if (note?.trim()) {
      task.taskNote = note.trim();
    }

    if (!isEmpty(relatedTask?.relatedTaskId)) {
      task.source = {
        from: relatedTask?.service?.name,
        taskId: relatedTask?.relatedTaskId,
      };
    }

    return task;
  };

  const executeTaskPosting = debounce(async () => {
    const taskData = buildTaskData();
    postTaskWaterHeater(taskData);
  }, 300);

  const handleSameTimeConflict = async (): Promise<void> => {
    Alert.alert?.open({
      title: t('DIALOG_TITLE_INFORMATION'),
      message: t('TASK_SAME_TIME_MESSAGE'),
      actions: [
        { text: t('CLOSE'), style: 'cancel' },
        {
          text: t('OK'),
          onPress: async () => {
            executeTaskPosting();
          },
        },
      ],
    });
  };

  const postTask = async (): Promise<any> => {
    const currentState = usePostTaskStore.getState();
    const { date, service, address } = currentState;
    const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

    // Check for conflicting tasks at the same time
    return checkTaskSameTime(
      {
        taskDate: DateTimeHelpers.formatToString({ date: date!, timezone }),
        serviceId: service?._id || '',
      },
      {
        onSuccess: async (result: any) => {
          if (result === true) {
            return handleSameTimeConflict();
          }
          return executeTaskPosting();
        },
      },
    );
  };

  return { getPrice, postTask };
};
