import {
  Al<PERSON>,
  DateT<PERSON>Hel<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Endpoint<PERSON>eys,
  getPhoneNumber,
  handleError,
  i18n,
  IApiError,
  PaymentService,
  PostTaskHelpers,
  useApiMutation,
  useAppLoadingStore,
  useAppStore,
  usePostTaskAction,
  useUserStore,
} from '@btaskee/design-system';
import { isEmpty } from 'lodash-es';

import { usePostTaskStore } from '@stores';

// Utility functions for building task data
const buildDisinfectionDetailData = (disinfectionData: {
  area?: any;
  customArea?: any;
  space?: any;
}) => {
  const { area, customArea, space } = disinfectionData;
  const detailData: any = {
    space: space?.name,
    spaceText: space?.text,
  };

  if (area?.from && area?.to) {
    detailData.area = {
      from: area?.from,
      to: area?.to,
      name: area?.name,
    };
  }

  if (customArea && Number(customArea) > 0) {
    detailData.customArea = customArea;
  }

  return detailData;
};

export const usePostTask = () => {
  const { setPrice, setLoadingPrice, resetState, setDuration } =
    usePostTaskStore();
  const { showLoading, hideLoading } = useAppLoadingStore();
  const { user } = useUserStore();
  const { isoCode } = useAppStore();
  const { handlePostTaskError } = usePostTaskAction();

  const { mutate: getPriceDisinfection } = useApiMutation({
    key: EndpointKeys.getPriceDisinfection,
    options: {
      onMutate: () => {
        setLoadingPrice(true);
        showLoading();
      },
      onSettled: () => {
        setLoadingPrice(false);
        hideLoading();
      },
    },
  });

  const { mutate: postTaskDisinfection } = useApiMutation({
    key: EndpointKeys.postTaskDisinfection,
    options: {
      onSuccess: async (data: any) => {
        if (data?.bookingId) {
          resetState();
          await PaymentService.onPostTaskSuccess({
            bookingId: data.bookingId,
            isPrepayment: data.isPrepayment,
          });
        }
      },
      onError: (error: IApiError) => {
        handlePostTaskError(error);
      },
      onMutate: () => {
        showLoading();
      },
      onSettled() {
        hideLoading();
      },
    },
  });

  const { mutate: checkTaskSameTime } = useApiMutation({
    key: EndpointKeys.checkTaskSameTime,
  });

  const buildPricingData = () => {
    const currentState = usePostTaskStore.getState();
    const {
      address,
      date,
      duration,
      isAutoChooseTasker,
      service,
      paymentMethod,
      area,
      customArea,
      promotion,
    } = currentState;

    if (!address || !date || (!area && !customArea)) {
      return null;
    }

    const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

    const task: any = {
      timezone,
      date: DateTimeHelpers.formatToString({ timezone, date }),
      autoChooseTasker: isAutoChooseTasker,
      taskPlace: {
        country: address?.country,
        city: address?.city,
        district: address?.district,
      },
      homeType: address?.homeType,
      duration,
      ...PostTaskHelpers.formatDataToParams({ paymentMethod, promotion }),
    };

    if (area?.from && area?.to) {
      task.disinfectionDetail = {
        area: area,
      };
    }
    if (customArea && Number(customArea) > 0) {
      task.disinfectionDetail = {
        customArea: customArea,
      };
    }

    return { task, service: { _id: service?._id }, isoCode };
  };

  const getPrice = async (): Promise<void> => {
    const pricingData = buildPricingData();

    if (!pricingData) {
      setPrice(null);
      return;
    }

    setLoadingPrice(true);

    await getPriceDisinfection(pricingData, {
      onSuccess: (result: any) => {
        setPrice(result);
        setDuration(result?.duration || 0);
      },
      onError: (error: IApiError) => {
        handleError(error);
        setPrice(null);
      },
    });

    setLoadingPrice(false);
  };

  const buildTaskData = () => {
    const currentState = usePostTaskStore.getState();
    const {
      address,
      date,
      duration,
      service,
      paymentMethod,
      isApplyNoteForAllTask,
      note,
      isFavouriteTasker,
      relatedTask,
      space,
      area,
      customArea,
      promotion,
    } = currentState;

    const isAddressMaybeWrong = Boolean(address?.isAddressMaybeWrong);
    const isTet = service?.isTet || false;
    const city = address?.city;
    const timezone = DateTimeHelpers.getTimezoneByCity(city);

    const task: any = {
      address: address?.address,
      contactName: address?.contact || user?.name,
      lat: address?.lat,
      lng: address?.lng,
      phone: getPhoneNumber(
        address?.phoneNumber || user?.phone || '',
        address?.countryCode || user?.countryCode || '',
      ),
      countryCode: address?.countryCode || user?.countryCode,
      description: address?.description,
      askerId: user?._id,
      autoChooseTasker: true,
      date: DateTimeHelpers.formatToString({ date, timezone }),
      timezone,
      deviceInfo: DeviceHelper.getDeviceInfo(),
      duration,
      homeType: address?.homeType,
      houseNumber: address?.description,
      isoCode,
      serviceId: service?._id,
      taskPlace: {
        country: address?.country,
        city,
        district: address?.district,
      },
      isSendToFavTaskers: isFavouriteTasker,
      updateTaskNoteToUser: isApplyNoteForAllTask,
      shortAddress: address?.shortAddress,
      disinfectionDetail: buildDisinfectionDetailData({
        area,
        customArea,
        space,
      }),
      ...PostTaskHelpers.formatDataToParams({ paymentMethod, promotion }),
    };

    // Add optional fields
    if (isAddressMaybeWrong) {
      task.taskPlace.isAddressMaybeWrong = true;
    }

    if (isTet) {
      task.isTetBooking = true;
    }

    if (note?.trim()) {
      task.taskNote = note.trim();
    }

    if (!isEmpty(relatedTask?.relatedTaskId)) {
      task.source = {
        from: relatedTask?.service?.name,
        taskId: relatedTask?.relatedTaskId,
      };
    }

    return task;
  };

  const executeTaskPosting = async (): Promise<any> => {
    const taskData = buildTaskData();
    return postTaskDisinfection(taskData);
  };

  const handleSameTimeConflict = async (): Promise<void> => {
    Alert.alert?.open({
      title: i18n.t('DIALOG_TITLE_INFORMATION'),
      message: i18n.t('TASK_SAME_TIME_MESSAGE'),
      actions: [
        { text: i18n.t('CLOSE'), style: 'cancel' },
        {
          text: i18n.t('OK'),
          onPress: async () => {
            setTimeout(async () => {
              await executeTaskPosting();
            }, 300);
          },
        },
      ],
    });
  };

  const postTask = async (): Promise<any> => {
    const currentState = usePostTaskStore.getState();
    const { date, service, address } = currentState;
    const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

    // Check for conflicting tasks at the same time
    return checkTaskSameTime(
      {
        taskDate: DateTimeHelpers.formatToString({ date: date!, timezone }),
        serviceId: service?._id || '',
      },
      {
        onSuccess: async (result: any) => {
          if (result === true) {
            return handleSameTimeConflict();
          }
          return executeTaskPosting();
        },
      },
    );
  };

  return { getPrice, postTask };
};
