import {
  <PERSON><PERSON>,
  DateT<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Endpoint<PERSON>eys,
  getPhoneNumber,
  handleError,
  IApiError,
  PaymentService,
  PostTaskHelpers,
  useApiMutation,
  useAppLoadingStore,
  useAppStore,
  usePostTaskAction,
  useUserStore,
} from '@btaskee/design-system';
import { isEmpty } from 'lodash-es';

import { usePostTaskStore } from '@stores';

import { useI18n } from './useI18n';

// Utility functions for building task data - REMOVED: Now using PostTaskHelpers.formatDataToParams

export const usePostTask = () => {
  const {
    service,
    setPrice,
    setLoadingPrice,
    setDuration,
    resetState,
    setIsBookedTask,
  } = usePostTaskStore();
  const { showLoading, hideLoading } = useAppLoadingStore();
  const { user } = useUserStore();
  const { isoCode } = useAppStore();
  const { t } = useI18n();

  const { handlePostTaskError } = usePostTaskAction();

  const { mutate: getPriceAirConditioner } = useApiMutation({
    key: EndpointKeys.getPriceAirConditioner,
    options: {
      onMutate: () => {
        setLoadingPrice(true);
        showLoading();
      },
      onSettled: () => {
        setLoadingPrice(false);
        hideLoading();
      },
    },
  });

  const { mutate: postTaskAirConditioner } = useApiMutation({
    key: EndpointKeys.postTaskAirConditioner,
    options: {
      onSuccess: async (data: any) => {
        const bookingId = data?.bookingId;

        if (bookingId) {
          setIsBookedTask(true);
          // Payment processing (includes navigation)
          await PaymentService.onPostTaskSuccess({
            bookingId,
            isPrepayment: data.isPrepayment,
          });
          resetState();
        }
      },
      onError: (error: IApiError) => {
        handlePostTaskError(error);
      },
      onMutate: () => {
        showLoading();
      },
      onSettled() {
        hideLoading();
      },
    },
  });

  const { mutate: checkTaskSameTime } = useApiMutation({
    key: EndpointKeys.checkTaskSameTime,
  });

  const buildPricingData = () => {
    const currentState = usePostTaskStore.getState();
    const {
      address,
      date,
      duration,
      paymentMethod,
      selectedAirConditioner,
      addons,
      promotion,
    } = currentState;

    if (!address || !date || !duration) {
      return null;
    }

    const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

    const task: any = {
      timezone,
      date: DateTimeHelpers.formatToString({ timezone, date }),
      autoChooseTasker: true,
      taskPlace: {
        country: address?.country,
        city: address?.city,
        district: address?.district,
      },
      homeType: address?.homeType,
      duration,
      detailAirConditioner: selectedAirConditioner,
      // Add payment method and promotion using PostTaskHelpers
      ...PostTaskHelpers.formatDataToParams({ paymentMethod, promotion }),
    };

    if (!isEmpty(addons)) {
      task.addons = addons;
    }

    return {
      task,
      service: { _id: service?._id || '' },
      isoCode: isoCode || '',
    };
  };

  const getPrice = async (): Promise<void> => {
    const pricingData = buildPricingData();

    if (!pricingData) {
      setPrice(null);
      return;
    }

    setLoadingPrice(true);

    await getPriceAirConditioner(pricingData, {
      onSuccess: (result: any) => {
        setPrice(result);
        setDuration(result?.duration || 0);
      },
      onError: (error: IApiError) => {
        handleError(error);
        setPrice(null);
      },
    });

    setLoadingPrice(false);
  };

  const buildTaskData = () => {
    const currentState = usePostTaskStore.getState();
    const {
      address,
      date,
      duration,
      addons,
      paymentMethod,
      isApplyNoteForAllTask,
      note,
      relatedTask,
      promotion,
      selectedAirConditioner,
    } = currentState;

    const isAddressMaybeWrong = Boolean(address?.isAddressMaybeWrong);
    const isTet = service?.isTet || false;
    const city = address?.city;
    const timezone = DateTimeHelpers.getTimezoneByCity(city);

    const task: any = {
      address: address?.address,
      contactName: address?.contact || user?.name,
      lat: address?.lat,
      lng: address?.lng,
      phone: getPhoneNumber(
        address?.phoneNumber || user?.phone || '',
        address?.countryCode || user?.countryCode || '',
      ),
      countryCode: address?.countryCode || user?.countryCode,
      description: address?.description,
      askerId: user?._id,
      autoChooseTasker: true,
      date: DateTimeHelpers.formatToString({ date, timezone }),
      timezone,
      deviceInfo: DeviceHelper.getDeviceInfo(),
      duration,
      homeType: address?.homeType,
      houseNumber: address?.description,
      isoCode,
      serviceId: service?._id,
      taskPlace: {
        country: address?.country,
        city,
        district: address?.district,
      },
      updateTaskNoteToUser: isApplyNoteForAllTask,
      shortAddress: address?.shortAddress,
      addons: addons || [],
      detailAirConditioner: selectedAirConditioner,
      // Add payment method and promotion using PostTaskHelpers
      ...PostTaskHelpers.formatDataToParams({ paymentMethod, promotion }),
    };

    // Add optional fields
    if (isAddressMaybeWrong) {
      task.taskPlace.isAddressMaybeWrong = true;
    }

    if (isTet) {
      task.isTetBooking = true;
    }

    if (note?.trim()) {
      task.taskNote = note.trim();
    }

    if (!isEmpty(relatedTask?.relatedTaskId)) {
      task.source = {
        from: relatedTask?.service?.name,
        taskId: relatedTask?.relatedTaskId,
      };
    }

    return task;
  };

  const executeTaskPosting = async (): Promise<any> => {
    const taskData = buildTaskData();
    return postTaskAirConditioner(taskData);
  };

  const handleSameTimeConflict = async (): Promise<void> => {
    Alert.alert?.open({
      title: t('DIALOG_TITLE_INFORMATION'),
      message: t('TASK_SAME_TIME_MESSAGE'),
      actions: [
        { text: t('CLOSE'), style: 'cancel' },
        {
          text: t('OK'),
          onPress: async () => {
            setTimeout(async () => {
              await executeTaskPosting();
            }, 300);
          },
        },
      ],
    });
  };

  const postTask = async (): Promise<any> => {
    const currentState = usePostTaskStore.getState();
    const { date, address } = currentState;
    const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

    // Check for conflicting tasks at the same time
    return checkTaskSameTime(
      {
        taskDate: DateTimeHelpers.formatToString({ date: date!, timezone }),
        serviceId: service?._id || '',
      },
      {
        onSuccess: async (result: any) => {
          if (result === true) {
            return handleSameTimeConflict();
          }
          return executeTaskPosting();
        },
      },
    );
  };

  return { getPrice, postTask };
};
