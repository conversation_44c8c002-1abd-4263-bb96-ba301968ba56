import {
  Al<PERSON>,
  DateTimeHel<PERSON>,
  <PERSON>ceHelper,
  EndpointKeys,
  GenderMassage,
  getPhoneNumber,
  handleError,
  IApiError,
  IDate,
  IPackageOption,
  PaymentService,
  PostTaskHelpers,
  useApiMutation,
  useAppLoadingStore,
  useAppStore,
  usePostTaskAction,
  useUserStore,
} from '@btaskee/design-system';
import { usePostTaskStore } from '@store';
import { debounce, isEmpty } from 'lodash-es';

import { useI18n } from '@hooks';

import { useTracking } from './useTracking';

export const usePostTask = () => {
  const { t } = useI18n();

  const { setPrice, setLoadingPrice, resetState, setIsBookedTask } =
    usePostTaskStore();
  const { handlePostTaskError } = usePostTaskAction();
  const { trackingPostTaskSuccess } = useTracking();
  const { showLoading, hideLoading } = useAppLoadingStore();

  const { user } = useUserStore();
  const { isoCode } = useAppStore();

  const { mutate: getPriceMassage } =
    useApiMutation<EndpointKeys.getPriceMassage>({
      key: EndpointKeys.getPriceMassage,
      options: {
        onMutate: () => {
          setLoadingPrice(true);
          showLoading();
        },
        onSettled: () => {
          setLoadingPrice(false);
          hideLoading();
        },
      },
    });

  const { mutate: checkTaskSameTime } = useApiMutation({
    key: EndpointKeys.checkTaskSameTime,
    options: {
      onMutate: () => {
        showLoading();
      },
      onSettled() {
        hideLoading();
      },
    },
  });

  const { mutate: getOutstandingPayment } = useApiMutation({
    key: EndpointKeys.getOutstandingPayment,
  });

  const { mutate: bookTaskForceTasker } =
    useApiMutation<EndpointKeys.bookTaskForceTasker>({
      key: EndpointKeys.bookTaskForceTasker,
      options: {
        onSuccess: async (data) => {
          // success
          if (data?.bookingId) {
            resetState();
            // Payment processing (includes navigation)
            await PaymentService.onPostTaskSuccess({
              bookingId: data.bookingId,
              isPrepayment: data.isPrepayment,
            });
          }
        },
        onError: (error: IApiError) => {
          handlePostTaskError(error);
        },
        onMutate: () => {
          showLoading();
        },
        onSettled() {
          hideLoading();
        },
      },
    });
  const { mutate: postTaskMassage } =
    useApiMutation<EndpointKeys.postTaskMassage>({
      key: EndpointKeys.postTaskMassage,
      options: {
        onSuccess: async (data) => {
          // success
          if (data?.bookingId) {
            setIsBookedTask(true);
            trackingPostTaskSuccess();
            resetState();
            // Payment processing (includes navigation)
            await PaymentService.onPostTaskSuccess({
              bookingId: data.bookingId,
              isPrepayment: data.isPrepayment,
            });
          }
        },
        onError: (error: IApiError) => {
          handlePostTaskError(error);
        },
        onMutate: () => {
          showLoading();
        },
        onSettled() {
          hideLoading();
        },
      },
    });

  const getDataPricing = () => {
    // Get the latest state directly from the store
    const currentState = usePostTaskStore.getState();
    const {
      address,
      date,
      duration,
      isAutoChooseTasker,
      service,
      forceTasker,
      dateOptions,
      paymentMethod,
      firstSelectedPackage,
      secondSelectedPackage,
      typeNumberOfCustomer,
      firstGender,
      secondGender,
      promotion,
    } = currentState;

    if (!address || !date) {
      return null;
    }

    const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

    // base info
    const task = {
      timezone,
      date: DateTimeHelpers.formatToString({ timezone, date }),
      autoChooseTasker: isAutoChooseTasker,
      taskPlace: {
        country: address?.country,
        city: address?.city,
        district: address?.district,
      },
      homeType: address?.homeType,
      duration: duration,
      ...PostTaskHelpers.formatDataToParams({ paymentMethod, promotion }),
    };

    if (!isEmpty(forceTasker)) {
      task.forceTasker = forceTasker;
    }
    if (!isEmpty(dateOptions)) {
      task.dateOptions = dateOptions;
    }

    const packagesData = [];
    if (firstSelectedPackage) {
      packagesData.push({ ...firstSelectedPackage, taskerGender: firstGender });
    }
    if (secondSelectedPackage) {
      packagesData.push({
        ...secondSelectedPackage,
        taskerGender: secondGender,
      });
    }
    const detailMassage = {
      packages: packagesData,
    };
    if (typeNumberOfCustomer) {
      detailMassage.type = typeNumberOfCustomer;
    }
    task.detailMassage = detailMassage;

    // Neu khong co package nao thi ket thuc
    if (isEmpty(packagesData)) {
      return null;
    }

    // Nếu không phải với Tasker yêu thích(forceTasker) và không ẩn gender và có package nào không có gender thì không gọi api get price
    if (
      isEmpty(forceTasker) &&
      !service?.detailService?.massage?.isHideGender &&
      packagesData.some((e) => !e.taskerGender)
    ) {
      return null;
    }
    return { task, service: { _id: service?._id }, isoCode };
  };

  const getPrice = debounce(async () => {
    // refactor data after call get price
    const data = getDataPricing();

    // data is null, no get price
    if (!data) {
      // set price is nul --> hide price button.
      return setPrice(null);
    }

    // call get price API
    getPriceMassage(data, {
      onSuccess: (result) => {
        setPrice(result);
      },
      onError: (error) => {
        handleError(error);
        setPrice(null);
      },
    });
  }, 150);

  const _handleRefactorDataPostTask = ({
    dataPackage,
    gender,
    defaultGender,
  }: {
    dataPackage: IPackageOption;
    gender: GenderMassage;
    defaultGender: GenderMassage;
  }) => {
    // Nếu đã có gender thì lấy luôn
    if (
      dataPackage?.taskerGender &&
      dataPackage?.taskerGender !== GenderMassage.Both &&
      dataPackage?.taskerGender !== GenderMassage.Random
    ) {
      return dataPackage;
    }
    // Nếu gender là both thi lấy gender default cho tasker
    if (gender === GenderMassage.Both) {
      return { ...dataPackage, taskerGender: defaultGender };
    }
    if (gender === GenderMassage.Random && !isEmpty(dataPackage)) {
      const { taskerGender, ...otherData } = dataPackage;
      return otherData;
    }
    // Nguoc lai, lưu gender với tasker
    return { ...dataPackage, taskerGender: gender };
  };

  const _refactorDataPostTask = () => {
    // Get the latest state directly from the store
    const currentState = usePostTaskStore.getState();
    const {
      address,
      date,
      duration,
      isAutoChooseTasker,
      service,
      forceTasker,
      dateOptions,
      paymentMethod,
      isFavouriteTasker,
      isApplyNoteForAllTask,
      note,
      firstSelectedPackage,
      secondSelectedPackage,
      typeOfExecutionOrder,
      firstGender,
      secondGender,
      firstAskerGenderPackage,
      secondAskerGenderPackage,
      promotion,
    } = currentState;

    const isAddressMaybeWrong = Boolean(address?.isAddressMaybeWrong);
    const isTet = service?.isTet || false;
    const city = address?.city;
    const timezone = DateTimeHelpers.getTimezoneByCity(city);

    // base task info
    const task = {
      address: address?.address,
      contactName: address?.contact || user?.name,
      lat: address?.lat,
      lng: address?.lng,
      phone: address?.phoneNumber || user?.phone,
      countryCode: address?.countryCode || user?.countryCode,
      description: address?.description,
      askerId: user?._id,
      autoChooseTasker: isAutoChooseTasker,
      date: DateTimeHelpers.formatToString({ date: date, timezone }),
      timezone,
      deviceInfo: DeviceHelper.getDeviceInfo(),
      duration: duration,
      homeType: address?.homeType,
      houseNumber: address?.description,
      isSendToFavTaskers: Boolean(isFavouriteTasker),
      isoCode,
      payment: {
        method: paymentMethod?.value,
      },
      serviceId: service?._id,
      taskPlace: {
        country: address?.country,
        city,
        district: address?.district,
      },
      updateTaskNoteToUser: isApplyNoteForAllTask,
      shortAddress: address?.shortAddress,
      ...PostTaskHelpers.formatDataToParams({ paymentMethod, promotion }),
    };

    // Refactor phone number - refill 0 at first
    task.phone = getPhoneNumber(task.phone || '', task.countryCode || '');

    // Địa chỉ có thể bị sai => khi book task, send to slack cho team vận hành xử lý
    if (isAddressMaybeWrong) {
      task.taskPlace.isAddressMaybeWrong = isAddressMaybeWrong;
    }

    if (isTet) {
      task.isTetBooking = true;
    }

    if (note && note?.trim()) {
      task.taskNote = note?.trim();
    }

    // Asker chọn Tasker
    if (forceTasker?._id) {
      delete task.isSendToFavTaskers;
      task.autoChooseTasker = true;
      task.forceTasker = {
        taskerId: forceTasker?._id,
        isResent: Boolean(forceTasker?.isResent),
      };
    }

    // Asker chọn lịch của Tasker
    if (dateOptions) {
      task.dateOptions = dateOptions;
    }

    // detail home moving data
    const packagesData = [];
    if (firstSelectedPackage) {
      packagesData.push(
        _handleRefactorDataPostTask({
          dataPackage: {
            ...firstSelectedPackage,
            askerGender: firstAskerGenderPackage,
          },
          gender: firstGender,
          defaultGender: GenderMassage.Male,
        }),
      );
    }
    if (secondSelectedPackage) {
      packagesData.push(
        _handleRefactorDataPostTask({
          dataPackage: {
            ...secondSelectedPackage,
            askerGender: secondAskerGenderPackage,
          },
          gender: secondGender,
          defaultGender: GenderMassage.Female,
        }),
      );
    }
    const detailMassage = {
      packages: packagesData,
    };
    if (typeOfExecutionOrder && !isEmpty(secondSelectedPackage)) {
      detailMassage.type = typeOfExecutionOrder;
    }
    task.detailMassage = detailMassage;

    return task;
  };

  const _postTaskProvider = async () => {
    const dataTask = _refactorDataPostTask();
    if (isEmpty(dataTask?.dateOptions)) {
      delete dataTask?.dateOptions;
    }
    if (!isEmpty(dataTask?.forceTasker)) {
      bookTaskForceTasker(dataTask);
      return;
    }
    postTaskMassage(dataTask);
  };

  /**
   * @description function booking task
   * @param payload {{object}} data of task
   */
  const postTask = async () => {
    const currentState = usePostTaskStore.getState();
    const { date, service, address } = currentState;
    const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

    // check task same time
    checkTaskSameTime(
      {
        taskDate: DateTimeHelpers.formatToString({
          date: date as IDate,
          timezone,
        }),
        serviceId: service?._id || '',
      },
      {
        onSuccess: (data) => {
          _addTask({ isExistTask: !data });
        },
      },
    );
  };

  /**
   * @description function booking task
   * @param payload {{object}} data of task
   */
  const _addTask = debounce(
    async ({ isExistTask }: { isExistTask: boolean }) => {
      // time ok
      if (isExistTask) {
        // call api book task
        _postTaskProvider();
        return;
      }

      // same time, alert for user
      return Alert.alert.open({
        title: t('DIALOG_TITLE_INFORMATION'),
        message: t('TASK_SAME_TIME_MESSAGE'),
        actions: [
          { text: t('CLOSE'), style: 'cancel' },
          {
            text: t('OK'),
            onPress: async () => {
              // wait modal close
              setTimeout(async () => {
                _postTaskProvider();
              }, 300);
            },
          },
        ],
      });
    },
    300,
  );

  return { getPrice, postTask };
};
