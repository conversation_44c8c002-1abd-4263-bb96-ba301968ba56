import {
  getTextWithLocale,
  IEventTaskAbandoned,
  IEventTaskPostSuccess,
  SERVICES,
  TrackingActions,
  TrackingPostTaskStep,
  TrackingScreenNames,
  TrackingServices,
} from '@btaskee/design-system';
import { usePostTaskStore } from '@store';

/**
 * Massage Service Tracking Hook
 *
 * Provides comprehensive tracking functionality for massage service following
 * the multi-provider tracking architecture with provider-agnostic interface.
 *
 * Features:
 * - Screen view tracking
 * - User action tracking (back, next, changes)
 * - Task abandonment tracking
 * - Task success tracking
 * - Massage-specific data tracking (duration, typeNumberOfCustomer, currentPackage, firstOptions, secondOptions, etc.)
 * - App state change tracking
 * - Navigation tracking patterns
 */
export const useTracking = () => {
  const { setStepPostTask, setIsBookedTask } = usePostTaskStore();

  /**
   * Track back action from choose duration screen
   * Called when user navigates back from duration selection
   */
  const trackingBackChooseDuration = () => {
    const currentState = usePostTaskStore.getState();
    const {
      duration,
      isAutoChooseTasker,
      service,
      gender,
      addons,
      isFavouriteTasker,
      isPremium,
      pet,
      typeNumberOfCustomer,
      currentPackage,
      firstOptions,
      secondOptions,
    } = currentState;

    TrackingServices.trackingServiceClick({
      screenName: TrackingScreenNames.DetailInformation,
      serviceName: SERVICES.MASSAGE,
      action: TrackingActions.Back,
      isTetBooking: service?.isTet,
      additionalInfo: {
        duration,
        premiumService: isPremium,
        addOnServices:
          addons && addons.map((addOn) => getTextWithLocale(addOn.text)),
        typeNumberOfCustomer,
        currentPackage: currentPackage?.name,
        firstOptions: firstOptions?.name,
        secondOptions: secondOptions?.name,
        options: {
          houseWithPets: pet,
          manuallyChooseTasker: !isAutoChooseTasker,
          prioritizeFavoriteTaskers: isFavouriteTasker,
          chooseTaskerGender: gender ? 'ON' : 'OFF',
        },
      },
    });
  };

  /**
   * Track duration selection next step
   * Called when user proceeds from duration selection to date/time selection
   */
  const trackingChooseDurationNextStep = () => {
    const currentState = usePostTaskStore.getState();
    const {
      service,
      isPremium,
      addons,
      duration,
      pet,
      isAutoChooseTasker,
      isFavouriteTasker,
      gender,
      typeNumberOfCustomer,
      currentPackage,
      firstOptions,
      secondOptions,
    } = currentState;

    TrackingServices.trackingServiceClick({
      screenName: TrackingScreenNames.DetailInformation,
      serviceName: SERVICES.MASSAGE,
      action: TrackingActions.Next,
      isTetBooking: service?.isTet,
      additionalInfo: {
        duration,
        premiumService: isPremium,
        addOnServices:
          addons && addons.map((addOn) => getTextWithLocale(addOn.text)),
        typeNumberOfCustomer,
        currentPackage: currentPackage?.name,
        firstOptions: firstOptions?.name,
        secondOptions: secondOptions?.name,
        options: {
          houseWithPets: pet,
          manuallyChooseTasker: !isAutoChooseTasker,
          prioritizeFavoriteTaskers: isFavouriteTasker,
          chooseTaskerGender: gender ? 'ON' : 'OFF',
        },
      },
    });
  };

  /**
   * Track screen view for choose duration screen
   */
  const trackingChooseDurationScreenView = () => {
    TrackingServices.trackingScreenView({
      screenName: TrackingScreenNames.DetailInformation,
      serviceName: SERVICES.MASSAGE,
      entryPoint: TrackingScreenNames.ChooseAddress,
    });
  };

  /**
   * Track screen view for choose date time screen
   */
  const trackingChooseDateTimeScreenView = () => {
    TrackingServices.trackingScreenView({
      screenName: TrackingScreenNames.ChooseWorkingTime,
      serviceName: SERVICES.MASSAGE,
      entryPoint: TrackingScreenNames.DetailInformation,
    });
  };

  /**
   * Track next/back actions for choose date time screen
   */
  const trackingNextBackActionChooseDateTime = (action: TrackingActions) => {
    const currentState = usePostTaskStore.getState();
    const {
      service,
      duration,
      date,
      typeNumberOfCustomer,
      currentPackage,
      firstOptions,
      secondOptions,
    } = currentState;

    TrackingServices.trackingServiceClick({
      screenName: TrackingScreenNames.ChooseWorkingTime,
      serviceName: SERVICES.MASSAGE,
      action: action,
      isTetBooking: service?.isTet,
      additionalInfo: {
        duration,
        scheduledDate: date,
        typeNumberOfCustomer,
        currentPackage: currentPackage?.name,
        firstOptions: firstOptions?.name,
        secondOptions: secondOptions?.name,
      },
    });
  };

  /**
   * Track screen view for confirm payment screen
   */
  const trackingConfirmPaymentScreenView = () => {
    TrackingServices.trackingScreenView({
      screenName: TrackingScreenNames.ConfirmPayment,
      serviceName: SERVICES.MASSAGE,
      entryPoint: TrackingScreenNames.ChooseWorkingTime,
    });
  };

  /**
   * Track next/back actions for confirm payment screen
   */
  const trackingNextBackActionConfirmPayment = (action: TrackingActions) => {
    const currentState = usePostTaskStore.getState();
    const {
      service,
      duration,
      date,
      paymentMethod,
      promotion,
      typeNumberOfCustomer,
      currentPackage,
      firstOptions,
      secondOptions,
    } = currentState;

    TrackingServices.trackingServiceClick({
      screenName: TrackingScreenNames.ConfirmPayment,
      serviceName: SERVICES.MASSAGE,
      action,
      isTetBooking: service?.isTet,
      additionalInfo: {
        duration,
        scheduledDate: date,
        typeNumberOfCustomer,
        currentPackage: currentPackage?.name,
        firstOptions: firstOptions?.name,
        secondOptions: secondOptions?.name,
        paymentMethod: {
          method: paymentMethod?.value,
          promotion: promotion?.code,
        },
      },
    });
  };

  /**
   * Track task abandonment when user exits the flow
   * Called when user navigates back or exits the app during task creation
   */
  const trackingPostTaskAbandoned = async (action: TrackingActions) => {
    const currentState = usePostTaskStore.getState();
    const {
      service,
      isBookedTask,
      price,
      address,
      duration,
      date,
      promotion,
      stepPostTask,
    } = currentState;

    setStepPostTask(TrackingPostTaskStep.STEP_2);

    // If the task has been booked, the event will not be recorded
    if (isBookedTask) {
      return setIsBookedTask(false);
    }

    const params: IEventTaskAbandoned = {
      action: action,
      step: stepPostTask,
      serviceId: service?._id,
      serviceName: SERVICES.MASSAGE,
      price: price?.finalCost,
      duration: duration,
      address: address?.address,
      district: address?.district,
      city: address?.city,
      country: address?.country,
      date: date,
      promotionCode: promotion?.code,
      isTetBooking: service?.isTet,
    };

    TrackingServices.trackingTaskAbandoned(params);
  };

  /**
   * Track successful task completion
   * Called when user successfully books a massage service
   */
  const trackingPostTaskSuccess = () => {
    const currentState = usePostTaskStore.getState();
    const {
      service,
      duration,
      date,
      address,
      promotion,
      price,
      schedule,
      isPremium,
      forceTasker,
    } = currentState;

    const params: IEventTaskPostSuccess = {
      serviceId: service?._id,
      serviceName: SERVICES.MASSAGE,
      duration: duration,
      address: address?.address,
      district: address?.district,
      city: address?.city,
      country: address?.country,
      date: date,
      promotionCode: promotion?.code,
      taskValue: price?.finalCost,
      schedule: schedule,
      isPremium: isPremium,
      forceTaskerId: forceTasker?._id,
    };
    TrackingServices.trackingTaskPostSuccess(params);
  };

  /**
   * Track address selection and navigation to duration selection
   * Called when user selects an address and moves to ChooseDuration screen
   */
  const trackingChooseAddressNextStep = () => {
    const currentState = usePostTaskStore.getState();
    const { address } = currentState;

    TrackingServices.trackingServiceClick({
      screenName: TrackingScreenNames.ChooseAddress,
      serviceName: SERVICES.MASSAGE,
      action: TrackingActions.Next,
      additionalInfo: {
        address: address?.address,
        district: address?.district,
        city: address?.city,
        country: address?.country,
      },
    });
  };

  return {
    trackingBackChooseDuration,
    trackingChooseDurationNextStep,
    trackingNextBackActionChooseDateTime,
    trackingChooseDateTimeScreenView,
    trackingChooseDurationScreenView,
    trackingPostTaskAbandoned,
    trackingConfirmPaymentScreenView,
    trackingNextBackActionConfirmPayment,
    trackingPostTaskSuccess,
    trackingChooseAddressNextStep,
  };
};
