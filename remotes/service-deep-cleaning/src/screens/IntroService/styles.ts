import { StyleSheet } from 'react-native';
import {
  BorderRadius,
  Colors,
  DeviceHelper,
  Spacing,
} from '@btaskee/design-system';

const width = DeviceHelper.WINDOW.WIDTH;

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.neutralBackground,
  },
  imageStyle: {
    width: width,
    height: 230,
  },
  imgIntro: {
    width: width - Spacing.SPACE_32,
    height: (width - Spacing.SPACE_32) * 0.5,
  },
  wrap_image: {
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: BorderRadius.RADIUS_08,
    overflow: 'hidden',
    marginHorizontal: Spacing.SPACE_16,
  },
  txt_note: {
    color: Colors.neutral800,
    lineHeight: Spacing.SPACE_24,
  },
  wrapItemNote: {
    flexDirection: 'row',
    marginTop: Spacing.SPACE_16,
  },
  wrapBottom: {
    marginTop: Spacing.SPACE_04,
  },
  contentContainerStyle: {
    paddingHorizontal: Spacing.SPACE_16,
    paddingBottom: '20%',
  },
  content: {
    marginTop: -150,
    flex: 1,
  },
  txtMarkdown: {
    color: Colors.neutral800,
    lineHeight: Spacing.SPACE_24,
  },
  txtStyle: {
    marginTop: 0,
    paddingTop: 0,
  },
});
