import React from 'react';
import {
  bg<PERSON><PERSON>er,
  <PERSON>View,
  BottomView,
  Colors,
  ConditionView,
  CText,
  DeepCleaningRouteName,
  DeepCleaningStackScreenProps,
  FastImage,
  FontSizes,
  IconImage,
  IconImageProps,
  Markdown,
  PrimaryButton,
  ScrollView,
  SizedBox,
  Spacing,
} from '@btaskee/design-system';

import { useAppNavigation, useI18n } from '@hooks';
import { bgIntro, intro1, intro2 } from '@images';
import { usePostTaskStore } from '@stores';

import styles from './styles';

const IntroItem = ({
  icon,
  title,
}: {
  icon: IconImageProps['source'];
  title: string;
}) => {
  return (
    <BlockView
      flex
      style={styles.wrapItemNote}
    >
      <IconImage source={icon} />
      <SizedBox width={Spacing.SPACE_12} />
      <BlockView flex>
        <Markdown
          text={title}
          textStyle={styles.txtMarkdown}
          paragraphStyle={styles.txtStyle}
        />
      </BlockView>
    </BlockView>
  );
};

export type IntroServiceProps =
  DeepCleaningStackScreenProps<DeepCleaningRouteName.IntroService>;

export const IntroService = ({ route }: IntroServiceProps) => {
  const { t } = useI18n();
  const navigation = useAppNavigation();
  const { setIsFirstOpen } = usePostTaskStore();
  const isHideButton = route?.params?.isHideButton;

  const onSubmit = async () => {
    // Set first open, and no show this intro again
    setIsFirstOpen(false);
    return navigation.replace(DeepCleaningRouteName.ChooseAddress);
  };

  const onBack = () => navigation.goBack();

  return (
    <BlockView style={styles.container}>
      <BlockView flex>
        <BlockView>
          <FastImage
            style={styles.imageStyle}
            resizeMode={'cover'}
            source={bgHeader}
          />
        </BlockView>

        <BlockView style={styles.content}>
          <BlockView style={styles.wrap_image}>
            <FastImage
              resizeMode="cover"
              source={bgIntro}
              style={styles.imgIntro}
            />
          </BlockView>
          <ScrollView
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.contentContainerStyle}
          >
            <CText
              bold
              color={Colors.orange500}
              size={FontSizes.SIZE_18}
              margin={{ top: Spacing.SPACE_16, bottom: Spacing.SPACE_12 }}
            >
              {t('DEEP_CLEANING_SERVICE')}
            </CText>

            <Markdown
              text={t('INTRO_DEEP_CLEANING_SERVICE_1')}
              textStyle={styles.txtMarkdown}
              paragraphStyle={styles.txtStyle}
            />

            <BlockView style={styles.wrapBottom}>
              <IntroItem
                icon={intro1}
                title={t('INTRO_DEEP_CLEANING_SERVICE_2')}
              />
              <IntroItem
                icon={intro2}
                title={t('INTRO_DEEP_CLEANING_SERVICE_3')}
              />
              <CText margin={{ top: Spacing.SPACE_16 }}>
                {t('INTRO_DEEP_CLEANING_SERVICE_4')}
              </CText>
            </BlockView>
          </ScrollView>
        </BlockView>
      </BlockView>

      <ConditionView
        condition={Boolean(isHideButton)}
        viewFalse={
          <BottomView>
            <PrimaryButton
              testID="btnSubmitIntroService"
              onPress={onSubmit}
              title={t('INTRO_START_EXPERIENCE')}
            />
          </BottomView>
        }
        viewTrue={
          <BottomView>
            <PrimaryButton
              testID="btnSubmitIntroService"
              onPress={onBack}
              title={t('BTN_BACK')}
            />
          </BottomView>
        }
      />
    </BlockView>
  );
};
