import {
  Alert,
  DateTimeHel<PERSON>,
  <PERSON><PERSON><PERSON>el<PERSON>,
  Endpoint<PERSON>eys,
  getPhoneNumber,
  handleError,
  IApiError,
  IDate,
  PaymentService,
  PostTaskHelpers,
  useApiMutation,
  useAppLoadingStore,
  useAppStore,
  usePostTaskAction,
  useUserStore,
} from '@btaskee/design-system';
import { IDataBooking, IParamsGetPrice } from '@types';
import { debounce } from 'lodash-es';

import { usePostTaskStore } from '@stores';

import { useI18n } from './useI18n';
import { useTracking } from './useTracking';

export const usePostTask = () => {
  const { t } = useI18n();
  const { setPrice, setLoadingPrice, resetState, setIsBookedTask } =
    usePostTaskStore();
  const { showLoading, hideLoading } = useAppLoadingStore();
  const { user } = useUserStore();
  const { isoCode } = useAppStore();
  const { trackingPostTaskSuccess } = useTracking();
  const { handlePostTaskError } = usePostTaskAction();

  const { mutate: getPriceDeepCleaning } =
    useApiMutation<EndpointKeys.getPriceDeepCleaning>({
      key: EndpointKeys.getPriceDeepCleaning,
      options: {
        onMutate: () => {
          setLoadingPrice(true);
          showLoading();
        },
        onSettled: () => {
          setLoadingPrice(false);
          hideLoading();
        },
      },
    });

  const { mutate: checkTaskSameTime } = useApiMutation({
    key: EndpointKeys.checkTaskSameTime,
    options: {
      onMutate: () => {
        showLoading();
      },
      onSettled() {
        hideLoading();
      },
    },
  });

  const { mutate: postTaskDeepCleaning } =
    useApiMutation<EndpointKeys.postTaskDeepCleaning>({
      key: EndpointKeys.postTaskDeepCleaning,
      options: {
        onSuccess: async (data) => {
          const bookingId = data?.bookingId;

          if (!bookingId) return;

          setIsBookedTask(true);
          trackingPostTaskSuccess();
          resetState();
          // Payment processing (includes navigation and resetState)
          await PaymentService.onPostTaskSuccess({
            bookingId,
            isPrepayment: data.isPrepayment,
          });
        },
        onError: (error: IApiError) => {
          handlePostTaskError(error);
        },
        onMutate: () => {
          showLoading();
        },
        onSettled() {
          hideLoading();
        },
      },
    });

  const getDataPricing = () => {
    // Get the latest state directly from the store
    const currentState = usePostTaskStore.getState();
    const {
      address,
      date,
      duration,
      isAutoChooseTasker,
      service,
      paymentMethod,
      isPremium,
      promotion,
      area,
      numberOfTasker,
    } = currentState;

    if (!address || !date || !duration) {
      return null;
    }

    const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

    // base info
    const task: IParamsGetPrice['task'] = {
      timezone,
      date: DateTimeHelpers.formatToString({ timezone, date }),
      autoChooseTasker: isAutoChooseTasker,
      taskPlace: {
        country: address?.country,
        city: address?.city,
        district: address?.district,
      },
      homeType: address?.homeType as any,
      duration: duration,
      // Add payment method and promotion using PostTaskHelpers
      ...PostTaskHelpers.formatDataToParams({ paymentMethod, promotion }),
    } as any;

    if (area && numberOfTasker) {
      (task as any).detailDeepCleaning = {
        numberOfTaskersDeepCleaning: numberOfTasker,
        area: area,
      };
    }
    // Check premium service
    if (isPremium) {
      task.isPremium = true;
    }

    return {
      task: task as any,
      service: { _id: service?._id || '' },
      isoCode: isoCode || '',
    };
  };

  const getPrice = debounce(async () => {
    // refactor data after call get price
    const data = getDataPricing();

    // data is null, no get price
    if (!data) {
      // set price is nul --> hide price button.
      return setPrice(null);
    }

    // call get price API
    getPriceDeepCleaning(data as any, {
      onSuccess: (result) => {
        setPrice(result);
      },
      onError: (error) => {
        handleError(error);
        setPrice(null);
      },
    });
  }, 150);

  const _refactorDataPostTask = () => {
    // Get the latest state directly from the store
    const currentState = usePostTaskStore.getState();
    const {
      address,
      date,
      duration,
      isAutoChooseTasker,
      service,
      paymentMethod,
      isApplyNoteForAllTask,
      note,
      promotion,
      area,
      numberOfTasker,
    } = currentState;

    const isAddressMaybeWrong = Boolean(address?.isAddressMaybeWrong);
    const isTet = service?.isTet || false;
    const city = address?.city;
    const timezone = DateTimeHelpers.getTimezoneByCity(city);

    // base task info
    const task: IDataBooking = {
      address: address?.address,
      contactName: address?.contact || user?.name,
      lat: address?.lat,
      lng: address?.lng,
      phone: address?.phoneNumber || user?.phone,
      countryCode: address?.countryCode || user?.countryCode,
      description: address?.description,
      askerId: user?._id,
      autoChooseTasker: isAutoChooseTasker,
      date: DateTimeHelpers.formatToString({
        date: date,
        timezone,
      }),
      timezone,
      deviceInfo: DeviceHelper.getDeviceInfo(),
      duration: duration,
      homeType: address?.homeType,
      houseNumber: address?.description,
      isoCode,
      serviceId: service?._id,
      taskPlace: {
        country: address?.country,
        city,
        district: address?.district,
      },
      updateTaskNoteToUser: isApplyNoteForAllTask,
      shortAddress: address?.shortAddress,
      // Add payment method and promotion using PostTaskHelpers
      ...PostTaskHelpers.formatDataToParams({ paymentMethod, promotion }),
    };

    // Refactor phone number - refill 0 at first
    task.phone = getPhoneNumber(task.phone || '', task.countryCode || '');

    // Địa chỉ có thể bị sai => khi book task, send to slack cho team vận hành xử lý
    if (isAddressMaybeWrong) {
      task.taskPlace.isAddressMaybeWrong = isAddressMaybeWrong;
    }

    if (isTet) {
      task.isTetBooking = true;
    }

    if (note && note?.trim()) {
      task.taskNote = note?.trim();
    }

    // Add detailDeepCleaning
    task.detailDeepCleaning = {
      numberOfTaskersDeepCleaning: numberOfTasker,
      areaDeepCleaning: area,
    };

    return task;
  };

  /**
   * @description function booking task
   * @param payload {{object}} data of task
   */
  const postTask = async () => {
    const currentState = usePostTaskStore.getState();
    const { date, service, address } = currentState;
    const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

    // check task same time
    checkTaskSameTime(
      {
        taskDate: DateTimeHelpers.formatToString({
          date: date as IDate,
          timezone,
        }),
        serviceId: service?._id || '',
      },
      {
        onSuccess: (data) => {
          _addTask({ isExistTask: !data });
        },
      },
    );
  };

  /**
   * @description function booking task
   * @param payload {{object}} data of task
   */
  const _addTask = debounce(
    async ({ isExistTask }: { isExistTask: boolean }) => {
      const dataTask = _refactorDataPostTask();
      // time ok
      if (isExistTask) {
        // call api book task
        postTaskDeepCleaning(dataTask);
        return;
      }

      // same time, alert for user
      return Alert.alert.open({
        title: t('DIALOG_TITLE_INFORMATION'),
        message: t('TASK_SAME_TIME_MESSAGE'),
        actions: [
          { text: t('CLOSE'), style: 'cancel' },
          {
            text: t('OK'),
            onPress: async () => {
              // wait modal close
              setTimeout(async () => {
                postTaskDeepCleaning(dataTask);
              }, 300);
            },
          },
        ],
      });
    },
    300,
  );

  return { getPrice, postTask };
};
