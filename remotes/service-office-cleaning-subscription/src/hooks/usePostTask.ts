import {
  DateT<PERSON>Hel<PERSON>,
  <PERSON><PERSON><PERSON>elper,
  EndpointKeys,
  handleError,
  IApiError,
  IDate,
  PaymentService,
  PostTaskHelpers,
  useApiMutation,
  useAppLoadingStore,
  useAppStore,
  usePostTaskAction,
  useUserStore,
} from '@btaskee/design-system';
import { debounce, isEmpty } from 'lodash-es';

import { usePostTaskStore } from '@stores';

import { useTracking } from './useTracking';

export const usePostTask = () => {
  const { isoCode } = useAppStore();
  const { user } = useUserStore();
  const { showLoading, hideLoading } = useAppLoadingStore();
  const { trackingSubscriptionSuccess } = useTracking();

  const { setPrice, setLoadingPrice, resetState, setIsBookedTask } =
    usePostTaskStore();

  const { handlePostTaskError } = usePostTaskAction();

  const { mutate: getPriceOfficeCleaningSubscription } = useApiMutation({
    key: EndpointKeys.getPriceOfficeCleaningSubscription,
    options: {
      onMutate: () => {
        setLoadingPrice(true);
        showLoading();
      },
      onSettled: () => {
        setLoadingPrice(false);
        hideLoading();
      },
    },
  });

  const { mutate: postTaskOfficeCleaningSubscription } = useApiMutation({
    key: EndpointKeys.postTaskOfficeCleaningSubscription,
    options: {
      onSuccess: async (data) => {
        const paymentMethod = usePostTaskStore.getState().paymentMethod;
        trackingSubscriptionSuccess();

        resetState();
        setIsBookedTask(true);
        await PaymentService.onPostTaskSubscriptionSuccess({
          paymentMethod,
          data: data?.data,
        });
      },
      onError: (error: IApiError) => {
        handlePostTaskError(error);
      },
      onMutate: () => {
        showLoading();
      },
      onSettled() {
        hideLoading();
      },
    },
  });

  const getDataPricing = async () => {
    // Get the latest state directly from the store
    const currentState = usePostTaskStore.getState();
    const {
      address,
      duration,
      addons,
      schedule,
      month,
      promotion,
      paymentMethod,
      detailOfficeCleaning,
      service: currentService,
      requirements,
    } = currentState;

    if (
      isEmpty(schedule) ||
      !duration ||
      !month ||
      isEmpty(detailOfficeCleaning)
    ) {
      return null;
    }
    const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

    const schedules = schedule.map((e) =>
      DateTimeHelpers.formatToString({ timezone, date: e }),
    );

    const params = {
      schedule: schedules,
      timezone,
      service: {
        _id: currentService?._id || '',
      },
      task: {
        taskPlace: {
          country: address?.country,
          city: address?.city,
          district: address?.district,
        },
        duration: duration,
        autoChooseTasker: true,
        homeType: address?.homeType,
      },
      month,
      isoCode: isoCode || '',
      ...PostTaskHelpers.formatDataToParams({ paymentMethod, promotion }),
    };

    params.task.detailOfficeCleaning = detailOfficeCleaning;

    if (requirements) {
      params.task.requirements = requirements;
    }

    if (!isEmpty(addons)) {
      params.task.addons = addons;
    }

    return params;
  };

  const getPrice = debounce(async () => {
    // refactor data after call get price
    const data = await getDataPricing();

    // data is null, no get price
    if (!data) {
      // set price is nul --> hide price button.
      return setPrice(null);
    }

    // call get price API
    getPriceOfficeCleaningSubscription(data, {
      onSuccess: (result) => {
        setPrice(result);
      },
      onError: (error) => {
        handleError(error);
        setPrice(null);
      },
    });
  }, 150);

  const _refactorDataPostTask = () => {
    const currentState = usePostTaskStore.getState();
    const {
      address,
      homeNumber,
      promotion,
      startDate,
      endDate,
      weekdays,
      duration,
      paymentMethod,
      note,
      schedule,
      month,
      service: taskService,
      detailOfficeCleaning,
      requirements,
      vatInfo,
    } = currentState;
    const isAddressMaybeWrong = Boolean(address?.isAddressMaybeWrong);
    const city = address?.city;
    const timezone = DateTimeHelpers.getTimezoneByCity(city);

    // base task info
    const task = {
      startDate: DateTimeHelpers.formatToString({
        date: startDate as IDate,
        timezone,
      }),
      endDate: DateTimeHelpers.formatToString({
        date: endDate as IDate,
        timezone,
      }),
      timezone,
      taskPlace: {
        country: address?.country,
        city: address?.city,
        district: address?.district,
      },
      weekday: weekdays,
      duration: duration,
      serviceId: taskService?._id,
      homeType: address?.homeType,
      address: address?.address,
      contactName: address?.contact,
      location: {
        lat: address?.lat,
        lng: address?.lng,
      },
      countryCode: address?.countryCode || user?.countryCode,
      description: homeNumber,
      deviceInfo: DeviceHelper.getDeviceInfo(),
      houseNumber: homeNumber,
      taskNote: note?.trim(),
      isoCode: isoCode || '',
      shortAddress: address?.shortAddress,
      month: month,
      ...PostTaskHelpers.formatDataToParams({ paymentMethod, promotion }),
    };

    if (schedule && schedule.length > 0) {
      task.schedule = schedule.map((e) =>
        DateTimeHelpers.formatToString({ date: e, timezone }),
      );
    }

    // Địa chỉ có thể bị sai => khi book task, send to slack cho team vận hành xử lý
    if (isAddressMaybeWrong) {
      task.taskPlace.isAddressMaybeWrong = isAddressMaybeWrong;
    }

    if (!isEmpty(detailOfficeCleaning)) {
      task.detailOfficeCleaning = detailOfficeCleaning;
    }

    if (requirements) {
      task.requirements = requirements;
    }

    if (vatInfo) {
      task.vatInfo = vatInfo;
    }

    return task;
  };

  const postTask = async () => {
    const dataTask = _refactorDataPostTask();

    // tracking post task subscription
    // const minTask = service?.minTaskOfSubscription || 4;
    // const dataTracking = {
    //   taskDone: user?.taskDone || 0,
    //   serviceName: getServiceTextToTracking(service?.name),
    //   address: _.get(dataTask, 'address', null),
    //   startDate: _.get(dataTask, 'startDate', null),
    // };
    // await dispatch(setIsBookedTask(true));
    // trackingCleverTapSubscriptionBooked(dataTracking);

    // check task same time
    postTaskOfficeCleaningSubscription(dataTask);
  };

  return { getPrice, postTask };
};
