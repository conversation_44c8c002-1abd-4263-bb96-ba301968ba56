/**
 * @Filename: deep-cleaning/layout/index.js
 * @Description:
 * @CreatedAt: 21/10/2020
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @UpdatedAt: 12/1/2021
 * @UpdatedBy: <PERSON><PERSON>, <PERSON><PERSON>
 **/
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { AppState, AppStateStatus, LayoutAnimation } from 'react-native';
import { TabView } from 'react-native-tab-view';
import {
  BlockView,
  checkSupportCity,
  Colors,
  CText,
  getTextWithLocale,
  IAddress,
  Icon,
  IconAssets,
  IconImage,
  isIOS,
  NotSupportCity,
  PostTaskHelpers,
  PriceButton,
  SERVICES,
  Spacing,
  TouchableOpacity,
  TrackingActions,
  TypeTabBar,
  WashingMachineRouteName,
} from '@btaskee/design-system';
import { useIsFocused } from '@react-navigation/native';
import { find, get } from 'lodash-es';

import { SceneWashingMachine } from '@components';
import { useAppNavigation, useI18n, usePostTask, useTracking } from '@hooks';
import { MainStackParamList, NavigationProps } from '@navigation/type';
import { usePostTaskStore } from '@stores';

import { styles } from './styles';

const renderTitle = ({
  navigation,
  address,
}: {
  navigation: NavigationProps<keyof MainStackParamList>;
  address: IAddress;
}) => {
  return (
    <BlockView
      flex
      row
      horizontal
      jBetween
    >
      <BlockView
        flex
        row
      >
        <IconImage
          source={IconAssets.icLocation}
          size={24}
          color={Colors.red500}
        />
        <BlockView
          flex
          margin={{ left: Spacing.SPACE_08 }}
        >
          <CText>{address?.shortAddress}</CText>
          <CText
            bold
            numberOfLines={1}
            margin={{ right: Spacing.SPACE_16 }}
          >
            {address?.address}
          </CText>
        </BlockView>
      </BlockView>
      <TouchableOpacity
        onPress={() =>
          navigation?.navigate(WashingMachineRouteName.IntroService, {
            isHideButton: true,
          })
        }
      >
        <Icon
          name={'icMoreInformation'}
          size={24}
        />
      </TouchableOpacity>
    </BlockView>
  );
};

export const ChooseService = ({ route }) => {
  const {
    address,
    service,
    price,
    setDateTime,
    date,
    previousServiceId,
    tasksWashingMachine,
    updateWMTasks,
  } = usePostTaskStore();
  const { getPrice } = usePostTask();
  const { t } = useI18n();
  const {
    trackingChooseServiceScreenView,
    trackingBackNextActionChooseService,
    trackingPostTaskAbandoned,
  } = useTracking();
  const isFocused = useIsFocused();

  const navigation = useAppNavigation();

  const cityFromAddress = get(address, 'city', null);
  const washingMachineService = get(
    service,
    'detailService.washingMachine',
    null,
  );
  const [tabSelected, setTabSelected] = useState(0);

  const detailWashingMachine = useMemo(() => {
    if (!washingMachineService || !cityFromAddress) {
      return [];
    }
    const listCitySupport = washingMachineService?.city || [];
    const detail = find(listCitySupport, { name: cityFromAddress })?.type || [];
    return detail.map((e) => ({ ...e, key: e.name }));
  }, [cityFromAddress, washingMachineService]);

  React.useEffect(() => {
    // Did mount here
    // Check selected service difference with previous service: reset states before begining
    // The same service: keep state for user continue the booking
    if (previousServiceId && previousServiceId !== service._id) {
      // resetState();
    }
    !date &&
      setDateTime(
        PostTaskHelpers.getDefaultDateTime(
          {
            serviceName: SERVICES.ELDERLY_CARE_SUBSCRIPTION,
            defaultTaskTime: service?.defaultTaskTime,
          },
          service?.defaultTaskTime,
          address?.city,
        ),
      );
  }, []);

  useEffect(() => {
    navigation.setOptions({
      headerTitle: () => renderTitle({ navigation, address }),
    });
  }, [navigation, address]);

  useEffect(() => {
    trackingChooseServiceScreenView(route?.params?.entryPoint);
  }, [route?.params?.entryPoint]);

  useEffect(() => {
    getPrice();
  }, [tasksWashingMachine]);

  const handleTrackingActionStep2 = useCallback(() => {
    if (!isFocused) {
      return null;
    }
    trackingBackNextActionChooseService(TrackingActions.Back);
  }, [isFocused]);

  useEffect(() => {
    const unsubscribe = navigation.addListener('beforeRemove', () => {
      handleTrackingActionStep2();
    });

    return unsubscribe; // Cleanup listener khi component unmount
  }, []);

  const handleAppStateChange = (nextAppState: AppStateStatus) => {
    if (isIOS) {
      if (nextAppState === 'inactive') {
        trackingPostTaskAbandoned(TrackingActions.EXITED_APP);
      }
    } else if (nextAppState === 'background') {
      trackingPostTaskAbandoned(TrackingActions.EXITED_APP);
    }
  };

  useEffect(() => {
    const subscribe = AppState.addEventListener('change', handleAppStateChange);
    return () => {
      trackingPostTaskAbandoned(TrackingActions.TAP_HEADER_BACK);
      subscribe?.remove();
    };
  }, []);

  const _onConfirmed = () => {
    trackingBackNextActionChooseService(TrackingActions.Next);
    // if (checkSupportCityAndAlert(address?.city)) {
    // setStepPostTask(TRACKING_STEP.STEP_3);
    navigation.navigate(WashingMachineRouteName.ChooseDateTime);
    // }
  };

  const onCreate = (task) => {
    const newTasks = [task, ...tasksWashingMachine];
    updateWMTasks(newTasks);
  };

  const onRemoveItem = (index) => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    const newTasks = [...tasksWashingMachine].filter((e, i) => i !== index);
    updateWMTasks(newTasks);
  };

  const renderScene = ({ route }) => {
    return (
      <SceneWashingMachine
        route={route}
        tasksWashingMachine={tasksWashingMachine}
        onCreate={onCreate}
        onRemoveItem={onRemoveItem}
      />
    );
  };

  // Check support city
  if (!checkSupportCity(service?.city, address?.city)) {
    return <NotSupportCity />;
  }

  return (
    <BlockView style={styles.container}>
      <BlockView flex>
        <CText
          bold
          style={styles.txtPanel}
        >
          {t('CHOOSE_WASHING_MACHINE')}
        </CText>
        <TabView
          navigationState={{
            index: tabSelected,
            routes: detailWashingMachine,
          }}
          renderScene={renderScene}
          renderTabBar={({ navigationState }) => {
            const routes = navigationState?.routes;
            return (
              <BlockView
                center
                row
                style={styles.tabBarContainer}
              >
                {routes.map((route, index) => {
                  const isFocused = tabSelected === index;
                  const isLastItem = index === routes.length - 1;
                  const count = tasksWashingMachine.filter(
                    (e) => e.name === route.name,
                  ).length;
                  return (
                    <TypeTabBar
                      testID={`btnTabBar-${index}`}
                      count={count}
                      isFocused={isFocused}
                      key={route.name}
                      style={styles.tabBar(isLastItem)}
                      title={getTextWithLocale(route.text)}
                      onPress={() => setTabSelected(index)}
                    />
                  );
                })}
              </BlockView>
            );
          }}
          swipeEnabled={false}
          onIndexChange={setTabSelected}
        />
      </BlockView>

      <PriceButton
        pricePostTask={price}
        testID="btnNextStep2"
        onPress={_onConfirmed}
        fromScreen={service?.name}
      />
    </BlockView>
  );
};
