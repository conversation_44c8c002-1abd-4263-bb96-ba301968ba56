import React, { useCallback, useEffect } from 'react';
import {
  BlockView,
  BookingButton,
  ConditionView,
  DateTimeHelpers,
  LocationPostTask,
  PaymentDetail,
  PaymentDetailStep4WithDateOptions,
  PaymentMethodBlock,
  ScrollView,
  SERVICES,
  TrackingActions,
  TrackingPostTaskStep,
  TrackingScreenNames,
  TYPE_OF_PAYMENT,
  usePostTaskAction,
} from '@btaskee/design-system';
import { isEmpty } from 'lodash-es';

import { TaskDetail } from '@components';
import { useAppNavigation, usePostTask, useTracking } from '@hooks';
import { usePostTaskStore } from '@stores';

import { styles } from './styles';

export const ConfirmAndPayment = () => {
  const { postTask } = usePostTask();
  const {
    setAddress,
    address,
    homeNumber,
    paymentMethod,
    promotion,
    setPromotion,
    price,
    isBookedTask,
    setPaymentMethod,
    setStepPostTask,
    date,
  } = usePostTaskStore();
  const { getPaymentMethodWhenBooking } = usePostTaskAction();

  const navigation = useAppNavigation();
  const {
    trackingConfirmPaymentScreenView,
    trackingBackNextActionConfirmPayment,
  } = useTracking();

  const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

  useEffect(() => {
    const paymentMethodDefault = getPaymentMethodWhenBooking({
      promotion,
      price: price,
      paymentMethod: paymentMethod,
    });
    if (!isEmpty(paymentMethodDefault)) {
      setPaymentMethod(paymentMethodDefault);
    }
  }, []);

  useEffect(() => {
    setStepPostTask(TrackingPostTaskStep.STEP_3);
    trackingConfirmPaymentScreenView();
  }, []);

  const handleTrackingBack = useCallback(() => {
    if (isBookedTask) {
      return null;
    }
    trackingBackNextActionConfirmPayment(TrackingActions.Back);
  }, [isBookedTask]);

  useEffect(() => {
    const unsubscribe = navigation.addListener('beforeRemove', () => {
      handleTrackingBack();
    });

    return unsubscribe; // Cleanup listener khi component unmount
  }, []);

  const _onPosTask = async () => {
    trackingBackNextActionConfirmPayment(TrackingActions.Next);

    await postTask();
  };

  return (
    <BlockView style={styles.container}>
      <BlockView flex>
        <ScrollView
          scrollIndicatorInsets={{ right: 1 }}
          testID="scrollViewStep4"
          contentContainerStyle={styles.content}
        >
          <LocationPostTask
            address={address}
            homeNumber={homeNumber}
            setAddress={setAddress}
          />

          <TaskDetail />

          <ConditionView
            condition={!isEmpty(price?.dateOptions)}
            viewTrue={
              <PaymentDetailStep4WithDateOptions
                dateOptions={price?.dateOptions}
                timezone={timezone}
                paymentMethod={paymentMethod}
              />
            }
            viewFalse={<PaymentDetail price={price} />}
          />

          <PaymentMethodBlock
            serviceName={SERVICES.WASHING_MACHINE}
            type={TYPE_OF_PAYMENT.bookTask}
            entryPoint={TrackingScreenNames.ConfirmPayment}
            promotionOptions={{
              currentPromotion: promotion,
              taskCost: price?.cost,
              taskDate: date,
              taskPlace: {
                country: address?.country,
                city: address?.city,
                district: address?.district,
              },
            }}
            paymentMethodOptions={{
              currentPaymentMethod: paymentMethod,
            }}
            onChangePromotion={setPromotion}
            onChangePaymentMethod={setPaymentMethod}
          />
        </ScrollView>
      </BlockView>
      <BookingButton
        price={price}
        onPostTask={_onPosTask}
      />
    </BlockView>
  );
};
