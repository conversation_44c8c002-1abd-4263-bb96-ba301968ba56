import {
  Al<PERSON>,
  DateTimeHel<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>per,
  Endpoint<PERSON>eys,
  getPhoneNumber,
  handleError,
  i18n,
  IApiError,
  PaymentService,
  PostTaskHelpers,
  useApiMutation,
  useAppLoadingStore,
  useAppStore,
  usePostTaskAction,
  useUserStore,
} from '@btaskee/design-system';
import { isEmpty } from 'lodash-es';

import { usePostTaskStore } from '@stores';

// Utility functions for building task data
const buildDetailChildCareData = (childCareData: {
  numberOfChildren: number;
  detailChildren: any[];
}) => {
  const { numberOfChildren, detailChildren } = childCareData;
  return {
    numberOfChildren,
    detailChildren,
  };
};

// buildTaskPaymentData function removed - now using PostTaskHelpers.formatDataToParams

export const usePostTask = () => {
  const { setPrice, setLoadingPrice, resetState, setIsBookedTask } =
    usePostTaskStore();
  const { showLoading, hideLoading } = useAppLoadingStore();
  const { handlePostTaskError } = usePostTaskAction();
  const { user } = useUserStore();
  const { isoCode } = useAppStore();

  const { mutate: getOutstandingPayment } = useApiMutation({
    key: EndpointKeys.getOutstandingPayment,
  });

  const { mutate: getPriceChildCare } = useApiMutation({
    key: EndpointKeys.getPriceChildCare,
    options: {
      onMutate: () => {
        setLoadingPrice(true);
        showLoading();
      },
      onSettled: () => {
        setLoadingPrice(false);
        hideLoading();
      },
    },
  });

  const { mutate: getPricingTaskDateOptionsAPI } = useApiMutation({
    key: EndpointKeys.getPricingTaskDateOptions,
    options: {
      onMutate: () => {
        setLoadingPrice(true);
        showLoading();
      },
      onSettled: () => {
        setLoadingPrice(false);
        hideLoading();
      },
    },
  });

  const { mutate: postTaskChildCare } = useApiMutation({
    key: EndpointKeys.postTaskChildCare,
    options: {
      onSuccess: async (data: any) => {
        const bookingId = data?.bookingId;

        if (bookingId) {
          setIsBookedTask(true);
          // Payment processing (includes navigation)
          await PaymentService.onPostTaskSuccess({
            bookingId,
            isPrepayment: data.isPrepayment,
          });
          resetState();
        }
      },
      onError: (error: IApiError) => {
        handlePostTaskError(error);
      },
      onMutate: () => {
        showLoading();
      },
      onSettled() {
        hideLoading();
      },
    },
  });

  const { mutate: checkTaskSameTime } = useApiMutation({
    key: EndpointKeys.checkTaskSameTime,
  });

  const { mutate: bookTaskForceTasker } =
    useApiMutation<EndpointKeys.bookTaskForceTasker>({
      key: EndpointKeys.bookTaskForceTasker,
      options: {
        onSuccess: async (data: any) => {
          const bookingId = data?.bookingId;

          if (bookingId) {
            setIsBookedTask(true);
            // Payment processing (includes navigation)
            await PaymentService.onPostTaskSuccess({
              bookingId,
              isPrepayment: data.isPrepayment,
            });
            resetState();
          }
        },
        onError: (error: IApiError) => {
          handlePostTaskError(error);
        },
        onMutate: () => {
          showLoading();
        },
        onSettled() {
          hideLoading();
        },
      },
    });

  const buildPricingData = () => {
    const currentState = usePostTaskStore.getState();
    const {
      address,
      date,
      duration,
      isAutoChooseTasker,
      service,
      paymentMethod,
      detailChildren,
      numberOfChildren,
      forceTasker,
      dateOptions,
      addons,
      requirements,
      isPremium,
      promotion,
    } = currentState;

    if (!address || !date || !duration) {
      return null;
    }

    const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

    const NumberOfChild = {
      empty: 0,
      single: 1,
      double: 2,
    };

    // Check data before get price, no detail children -> no call api get price
    if (
      detailChildren?.length === NumberOfChild.empty ||
      (numberOfChildren === NumberOfChild.double &&
        detailChildren?.length === NumberOfChild.single)
    ) {
      return null;
    }

    const task: any = {
      timezone,
      date: DateTimeHelpers.formatToString({ timezone, date: date! }),
      autoChooseTasker: isAutoChooseTasker,
      taskPlace: {
        country: address?.country,
        city: address?.city,
        district: address?.district,
      },
      homeType: address?.homeType,
      duration,
      detailChildCare: buildDetailChildCareData({
        numberOfChildren,
        detailChildren,
      }),
      // Add payment method and promotion using PostTaskHelpers
      ...PostTaskHelpers.formatDataToParams({ paymentMethod, promotion }),
    };

    if (!isEmpty(forceTasker)) {
      task.forceTasker = forceTasker;
    }
    if (!isEmpty(dateOptions)) {
      task.dateOptions = dateOptions;
    }

    // addOnService
    if (!isEmpty(requirements)) {
      task.requirements = requirements.map((req: any) => ({ type: req.type })); // send type only
    }
    if (!isEmpty(addons)) {
      task.addons = addons;
    }
    // Check premium service
    if (isPremium) {
      task.isPremium = true;
    }

    return {
      task,
      service: { _id: service?._id || '' },
      isoCode: isoCode || '',
    };
  };

  const getPrice = async (): Promise<void> => {
    const pricingData = buildPricingData();

    if (!pricingData) {
      setPrice(null);
      return;
    }

    if (!isEmpty(pricingData?.task?.dateOptions)) {
      return getPricingTaskDateOptionsAPI(pricingData, {
        onSuccess: (result: any) => {
          setPrice(result);
        },
        onError: (error: IApiError) => {
          handleError(error);
          setPrice(null);
        },
      });
    }

    // call get price API
    getPriceChildCare(pricingData, {
      onSuccess: (result: any) => {
        setPrice(result);
      },
      onError: (error: IApiError) => {
        handleError(error);
        setPrice(null);
      },
    });
  };

  const buildTaskData = () => {
    const currentState = usePostTaskStore.getState();
    const {
      address,
      date,
      duration,
      service,
      paymentMethod,
      isApplyNoteForAllTask,
      note,
      detailChildren,
      numberOfChildren,
      isFavouriteTasker,
      isEnableSchedule,
      schedule,
      pet,
      relatedTask,
      forceTasker,
      dateOptions,
      promotion,
    } = currentState;

    const isAddressMaybeWrong = Boolean(address?.isAddressMaybeWrong);
    const isTet = service?.isTet || false;
    const city = address?.city;
    const timezone = DateTimeHelpers.getTimezoneByCity(city);

    // base task info
    const task: any = {
      address: address?.address,
      contactName: address?.contact || user?.name,
      lat: address?.lat,
      lng: address?.lng,
      phone: getPhoneNumber(
        address?.phoneNumber || user?.phone || '',
        address?.countryCode || user?.countryCode || '',
      ),
      countryCode: address?.countryCode || user?.countryCode,
      description: address?.description,
      askerId: user?._id,
      autoChooseTasker: true,
      date: DateTimeHelpers.formatToString({ date: date!, timezone }),
      timezone,
      deviceInfo: DeviceHelper.getDeviceInfo(),
      duration,
      homeType: address?.homeType,
      houseNumber: address?.description,
      isoCode,
      ...PostTaskHelpers.formatDataToParams({ paymentMethod, promotion }),
      serviceId: service?._id,
      taskPlace: {
        country: address?.country,
        city,
        district: address?.district,
      },
      isSendToFavTaskers: isFavouriteTasker,
      updateTaskNoteToUser: isApplyNoteForAllTask,
      shortAddress: address?.shortAddress,
      detailChildCare: buildDetailChildCareData({
        numberOfChildren,
        detailChildren,
      }),
    };

    // Add optional fields
    if (isAddressMaybeWrong) {
      task.taskPlace.isAddressMaybeWrong = true;
    }

    if (isTet) {
      task.isTetBooking = true;
    }

    if (note?.trim()) {
      task.taskNote = note.trim();
    }

    if (pet) {
      task.pet = pet;
    }

    if (isEnableSchedule && schedule && schedule.length > 0) {
      task.weekday = schedule;
    }

    if (!isEmpty(relatedTask?.relatedTaskId)) {
      task.source = {
        from: relatedTask?.service?.name,
        taskId: relatedTask?.relatedTaskId,
      };
    }

    // Asker chọn Tasker
    if (forceTasker?._id) {
      delete task.isSendToFavTaskers;
      task.autoChooseTasker = true;
      task.forceTasker = {
        taskerId: forceTasker?._id,
        isResent: Boolean((forceTasker as any)?.isResent),
      };
    }

    // Asker chọn lịch của Tasker
    if (dateOptions) {
      task.dateOptions = dateOptions;
    }

    // Payment and promotion data already added via PostTaskHelpers.formatDataToParams above

    return task;
  };

  const executeTaskPosting = async (): Promise<any> => {
    const taskData = buildTaskData();

    // Clean up dateOptions if needed
    if (isEmpty(taskData?.dateOptions)) {
      delete taskData.dateOptions;
    }

    // Check if this is a force tasker booking
    if (!isEmpty(taskData?.forceTasker)) {
      return bookTaskForceTasker(taskData);
    }

    // Normal child care task posting
    return postTaskChildCare(taskData);
  };

  const handleSameTimeConflict = async (): Promise<void> => {
    Alert.alert?.open({
      title: i18n.t('DIALOG_TITLE_INFORMATION'),
      message: i18n.t('TASK_SAME_TIME_MESSAGE'),
      actions: [
        { text: i18n.t('CLOSE'), style: 'cancel' },
        {
          text: i18n.t('OK'),
          onPress: async () => {
            setTimeout(async () => {
              await executeTaskPosting();
            }, 300);
          },
        },
      ],
    });
  };

  const postTask = async (): Promise<any> => {
    const currentState = usePostTaskStore.getState();
    const { date, service, address } = currentState;
    const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

    // Check for conflicting tasks at the same time
    return checkTaskSameTime(
      {
        taskDate: DateTimeHelpers.formatToString({ date: date!, timezone }),
        serviceId: service?._id || '',
      },
      {
        onSuccess: async (result: any) => {
          if (result === true) {
            return handleSameTimeConflict();
          }
          return executeTaskPosting();
        },
      },
    );
  };

  return { getPrice, postTask };
};
