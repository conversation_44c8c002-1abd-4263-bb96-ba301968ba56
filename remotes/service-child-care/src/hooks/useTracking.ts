import {
  DateTimeHelpers,
  getTextWithLocale,
  IAddons,
  IEventTaskAbandoned,
  IEventTaskPostSuccess,
  LOCALES,
  SERVICES,
  TrackingActions,
  TrackingPostTaskStep,
  TrackingScreenNames,
  TrackingServices,
  TypeFormatDate,
} from '@btaskee/design-system';
import { isEmpty } from 'lodash-es';

import { usePostTaskStore } from '@stores';

/**
 * Child-Care Service Tracking Hook
 *
 * Provides comprehensive tracking functionality for child-care service following
 * the multi-provider tracking architecture with provider-agnostic interface.
 *
 * Features:
 * - Screen view tracking
 * - User action tracking (back, next, changes)
 * - Task abandonment tracking
 * - Task success tracking
 * - Child-care specific data tracking (numberOfChildren, detailChildren)
 */
export const useTracking = () => {
  /**
   * Track back action from duration selection screen
   * Includes comprehensive child-care service data
   */
  const trackingBackNextActionChooseDuration = (action: TrackingActions) => {
    const currentState = usePostTaskStore.getState();
    const {
      duration,
      isAutoChooseTasker,
      service,
      gender,
      addons,
      isFavouriteTasker,
      isPremium,
      pet,
      numberOfChildren,
      detailChildren,
    } = currentState;

    TrackingServices.trackingServiceClick({
      screenName: TrackingScreenNames.DetailInformation,
      serviceName: SERVICES.CHILD_CARE,
      action: action,
      isTetBooking: service?.isTet,
      additionalInfo: {
        numberOfChildren,
        detailChildren:
          detailChildren &&
          detailChildren?.map((item: any) => ({
            text: getTextWithLocale(item.age.text, LOCALES.en),
            type: item.age.type,
            weight: item.weight,
          })),
        duration,
        premiumService: isPremium,
        addOnServices:
          addons && addons.map((addOn) => getTextWithLocale(addOn.text)),
        options: {
          houseWithPets: pet,
          manuallyChooseTasker: !isAutoChooseTasker,
          prioritizeFavoriteTaskers: isFavouriteTasker,
          chooseTaskerGender: gender ? 'ON' : 'OFF',
        },
      },
    });
  };

  /**
   * Track duration change action
   */
  const trackingChangeDuration = ({
    oldDuration,
    newDuration,
  }: {
    oldDuration?: number;
    newDuration: number;
  }) => {
    const currentState = usePostTaskStore.getState();
    const { service } = currentState;

    TrackingServices.trackingServiceClick({
      screenName: TrackingScreenNames.DetailInformation,
      serviceName: SERVICES.CHILD_CARE,
      action: TrackingActions.ChangeDuration,
      isTetBooking: service?.isTet,
      additionalInfo: {
        oldDuration,
        newDuration,
      },
    });
  };

  /**
   * Track date-time selection actions (back/next)
   */
  const trackingNextBackActionChooseDateTime = ({
    action,
  }: {
    action: TrackingActions;
  }) => {
    const currentState = usePostTaskStore.getState();
    const { service, address, date, note, schedule } = currentState;
    const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

    TrackingServices.trackingServiceClick({
      screenName: TrackingScreenNames.ChooseWorkingTime,
      serviceName: SERVICES.CHILD_CARE,
      action,
      isTetBooking: service?.isTet,
      additionalInfo: {
        workingTime: {
          date: DateTimeHelpers.formatToString({
            timezone,
            date,
            typeFormat: TypeFormatDate.DateShort,
          }),
          time: DateTimeHelpers.formatToString({
            timezone,
            date,
            typeFormat: TypeFormatDate.TimeHourMinute,
          }),
          weeklySchedule: isEmpty(schedule) ? null : schedule,
        },
        note,
      },
    });
  };

  /**
   * Track addon service changes
   */
  const trackingChangeAddons = (newService: IAddons[]) => {
    const currentState = usePostTaskStore.getState();
    const { service } = currentState;

    TrackingServices.trackingServiceClick({
      screenName: TrackingScreenNames.DetailInformation,
      serviceName: SERVICES.CHILD_CARE,
      action: TrackingActions.ChangeAddOnService,
      isTetBooking: service?.isTet,
      additionalInfo: {
        addOnServices:
          newService &&
          newService.map((addOn) => getTextWithLocale(addOn.text)),
      },
    });
  };

  /**
   * Track task abandonment
   * Called when user exits the booking flow without completing
   */
  const trackingPostTaskAbandoned = async (action: TrackingActions) => {
    const currentState = usePostTaskStore.getState();
    const {
      service,
      isBookedTask,
      price,
      address,
      duration,
      date,
      promotion,
      stepPostTask,
      setStepPostTask,
      setIsBookedTask,
    } = currentState;

    setStepPostTask(TrackingPostTaskStep.STEP_2);

    // If the task has been booked, the event will not be recorded
    if (isBookedTask) {
      return setIsBookedTask(false);
    }

    const params: IEventTaskAbandoned = {
      action: action,
      step: stepPostTask,
      serviceId: service?._id,
      serviceName: SERVICES.CHILD_CARE,
      price: price?.finalCost,
      duration: duration,
      address: address?.address,
      district: address?.district,
      city: address?.city,
      country: address?.country,
      date: date || undefined,
      promotionCode: promotion?.code,
      isTetBooking: service?.isTet,
    };
    TrackingServices.trackingTaskAbandoned(params);
  };

  /**
   * Track next/back actions on confirm payment screen
   */
  const trackingNextBackActionConfirmPayment = ({
    action,
  }: {
    action: TrackingActions;
  }) => {
    const currentState = usePostTaskStore.getState();
    const { service, forceTasker, paymentMethod, address, promotion } =
      currentState;

    TrackingServices.trackingServiceClick({
      screenName: TrackingScreenNames.ConfirmPayment,
      serviceName: SERVICES.CHILD_CARE,
      action,
      isTetBooking: service?.isTet,
      featureName: isEmpty(forceTasker) ? '' : 'BookWithFavTasker',
      taskerID: forceTasker?._id,
      additionalInfo: {
        phoneNumber: address?.phoneNumber,
        contactName: address?.contact,
        paymentMethod: {
          method: paymentMethod?.name,
          promotion: promotion?.code,
        },
      },
    });
  };

  /**
   * Track successful task posting
   * Called after task is successfully created
   */
  const trackingPostTaskSuccess = () => {
    const currentState = usePostTaskStore.getState();
    const {
      service,
      duration,
      date,
      address,
      promotion,
      price,
      schedule,
      isPremium,
      forceTasker,
    } = currentState;

    const params: IEventTaskPostSuccess = {
      serviceId: service?._id,
      serviceName: SERVICES.CHILD_CARE,
      duration: duration,
      address: address?.address,
      district: address?.district,
      city: address?.city,
      country: address?.country,
      date: date || undefined,
      promotionCode: promotion?.code,
      taskValue: price?.finalCost,
      schedule: schedule,
      isPremium: isPremium,
      forceTaskerId: forceTasker?._id,
    };
    TrackingServices.trackingTaskPostSuccess(params);
  };

  return {
    trackingBackNextActionChooseDuration,
    trackingChangeDuration,
    trackingNextBackActionChooseDateTime,
    trackingChangeAddons,
    trackingPostTaskAbandoned,
    trackingNextBackActionConfirmPayment,
    trackingPostTaskSuccess,
  };
};
