import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
  BlockView,
  BookingButton,
  CModal,
  CModalHandle,
  Colors,
  ConditionView,
  CText,
  DatePicker,
  DateTimeHelpers,
  FontSizes,
  LocationPostTask,
  PaymentDetailStep4WithDateOptions,
  PaymentMethodBlock,
  ScrollView,
  SERVICES,
  Spacing,
  TimePicker,
  TouchableOpacity,
  TrackingActions,
  TrackingPostTaskStep,
  TrackingScreenNames,
  TrackingServices,
  TYPE_OF_PAYMENT,
  usePostTaskAction,
  useSettingsStore,
} from '@btaskee/design-system';
import { isEmpty } from 'lodash-es';

import { useAppNavigation, useI18n, usePostTask, useTracking } from '@hooks';
import { usePostTaskStore } from '@stores';

// import PaymentMethod from './components/payment-method';
import TaskDetail from '../../components/TaskDetail';
// import { TRACKING_ACTION } from '@tracking/types';
import { styles } from './styles';

export const ConfirmAndPayment = () => {
  const { t } = useI18n();

  const { getPrice, postTask } = usePostTask();
  const { getPaymentMethodWhenBooking } = usePostTaskAction();

  const {
    setAddress,
    service,
    date,
    setDateTime,
    address,
    homeNumber,
    paymentMethod,
    promotion,
    setPromotion,
    price,
    isBookedTask,
    setPaymentMethod,
    setStepPostTask,
  } = usePostTaskStore();
  const { settings } = useSettingsStore();

  const navigation = useAppNavigation();
  const { trackingNextBackActionConfirmPayment, trackingPostTaskSuccess } =
    useTracking();
  // const { setPaymentMethodWhenBooking } = useBookTask();
  // const { trackingNextBackActionPostTaskStep4 } = useAppTracking();

  const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);
  const [isTet, setIsTet] = useState(service?.isTet);
  const [selectedDate, setSelectedDate] = useState(date);
  const modalRef = useRef<CModalHandle | null>(null);
  // const refModalLogin = useRef<Modalize | null>(null);

  useEffect(() => {
    const paymentMethodDefault = getPaymentMethodWhenBooking({
      promotion,
      price: price,
      paymentMethod: paymentMethod,
    });
    if (!isEmpty(paymentMethodDefault)) {
      setPaymentMethod(paymentMethodDefault);
    }
  }, []);

  useEffect(() => {
    setStepPostTask(TrackingPostTaskStep.STEP_4);
    TrackingServices.trackingServiceView({
      screenName: TrackingScreenNames.ConfirmPayment,
      serviceName: SERVICES.CHILD_CARE,
      entryPoint: TrackingScreenNames.ChooseWorkingTime,
    });
  }, []);

  const handleTrackingBack = useCallback(() => {
    if (!isBookedTask) {
      return null;
    }
    trackingNextBackActionConfirmPayment({ action: TrackingActions.Back });
  }, [isBookedTask]);

  useEffect(() => {
    const unsubscribe = navigation.addListener('beforeRemove', () => {
      handleTrackingBack();
    });

    return unsubscribe; // Cleanup listener khi component unmount
  }, []);

  const removeTetInService = () => {
    // Clear isTet in service
    delete service?.isTet;
    // setService(service);
    setIsTet(false);
    // setPaymentMethodWhenBooking();
  };

  const _changeToRegularBooking = () => {
    // Require change date if the choosen date is over regular range
    const maxDate = DateTimeHelpers.toDayTz({ timezone })
      .add(6, 'day')
      .endOf('date');
    const isAfter = DateTimeHelpers.checkIsAfter({
      timezone,
      firstDate: date,
      secondDate: maxDate,
    });
    if (isAfter) {
      // Set default date is 2PM tomorrow
      const tomorrow = DateTimeHelpers.toDayTz({ timezone })
        .add(1, 'day')
        .hour(14)
        .startOf('hour');
      setSelectedDate(tomorrow.toDate());

      // Show change new date modal
      modalRef?.current?.open && modalRef?.current?.open();
    } else {
      removeTetInService();
    }
  };

  // update date time when user change
  const onChangeDateTime = (dateObj) => {
    // check spam, call api with same data
    const isSame = DateTimeHelpers.checkIsSame({
      timezone,
      firstDate: date,
      secondDate: dateObj,
    });
    if (isSame) return null;

    setSelectedDate(dateObj);
  };

  const _changeNewDate = async () => {
    setDateTime(selectedDate);
    // recaculate duration and estimated time, only Home Cooking service
    getPrice();
    removeTetInService();
  };

  const _onPosTask = async () => {
    trackingNextBackActionConfirmPayment({ action: TrackingActions.Next });
    await postTask();
  };

  return (
    <BlockView style={styles.container}>
      <BlockView flex>
        <ScrollView
          scrollIndicatorInsets={{ right: 1 }}
          testID="scrollViewStep4"
          contentContainerStyle={styles.content}
        >
          <LocationPostTask
            testID="location-post-task-section"
            address={address}
            homeNumber={homeNumber}
            setAddress={setAddress}
          />

          <TaskDetail testID="task-detail-section" />

          <ConditionView
            condition={!isEmpty(price?.dateOptions)}
            viewTrue={
              <PaymentDetailStep4WithDateOptions
                dateOptions={price?.dateOptions}
                timezone={timezone}
                paymentMethod={paymentMethod}
              />
            }
          />

          <PaymentMethodBlock
            serviceName={service?.name}
            entryPoint={TrackingScreenNames.ConfirmPayment}
            type={TYPE_OF_PAYMENT.bookTask}
            promotionOptions={{
              currentPromotion: promotion,
              taskCost: price?.cost,
              taskDate: date,
              taskPlace: {
                country: address?.country,
                city: address?.city,
                district: address?.district,
              },
            }}
            paymentMethodOptions={{
              currentPaymentMethod: paymentMethod,
            }}
            onChangePromotion={setPromotion}
            onChangePaymentMethod={setPaymentMethod}
          />

          <ConditionView
            condition={Boolean(isTet)}
            viewTrue={
              <TouchableOpacity
                style={styles.btn}
                onPress={() => _changeToRegularBooking()}
              >
                <CText
                  center
                  bold
                  style={{
                    color: Colors.green500,
                    fontSize: FontSizes.SIZE_16,
                  }}
                >
                  {t('TET_BOOKING_TO_NORMAL_TASK')}
                </CText>
                <CText
                  center
                  size={FontSizes.SIZE_16}
                  margin={{ top: Spacing.SPACE_04 }}
                >
                  {t('TET_BOOKING_TO_NORMAL_TASK_DESCRIPTION')}
                </CText>
              </TouchableOpacity>
            }
          />
          <ConditionView
            condition={Boolean(isTet)}
            viewTrue={
              <CModal
                hideButtonClose
                ref={modalRef}
                title={t('TET_BOOKING_TO_NOMAL_NOTE_TITLE')}
                actions={[
                  {
                    text: t('TET_BOOKING_TO_NOMAL_NOTE_DONE'),
                    onPress: _changeNewDate,
                    disabled: DateTimeHelpers.checkIsSame({
                      timezone,
                      firstDate: date,
                      secondDate: selectedDate,
                    }),
                  },
                ]}
              >
                <CText>{t('TET_BOOKING_TO_NORMAL_TASK_CHANGE_DATE')}</CText>
                <BlockView>
                  <DatePicker
                    title={t('STEP_4_UPDATE_CALENDAR_TITLE')}
                    value={selectedDate}
                    onChange={onChangeDateTime}
                    settingSystem={settings?.settingSystem}
                    timezone={timezone}
                  />

                  <TimePicker
                    noShowTitle={false}
                    title={t('STEP_4_UPDATE_TIME_TITLE')}
                    value={selectedDate}
                    onChange={onChangeDateTime}
                    settingSystem={settings?.settingSystem}
                    timezone={timezone}
                  />
                </BlockView>
              </CModal>
            }
          />
        </ScrollView>
      </BlockView>
      <BookingButton
        testID="btnSubmitPostTask"
        onPostTask={_onPosTask}
        price={price}
      />
    </BlockView>
  );
};
