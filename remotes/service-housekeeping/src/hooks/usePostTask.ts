import {
  Al<PERSON>,
  DateTimeHelpers,
  <PERSON><PERSON><PERSON>elper,
  EndpointKeys,
  getPhoneNumber,
  handleError,
  IApiError,
  IDate,
  IHouseKeepingOption,
  PaymentService,
  PostTaskHelpers,
  TrackingPostTaskStep,
  useApiMutation,
  useAppLoadingStore,
  useAppStore,
  usePostTaskAction,
  useUserStore,
} from '@btaskee/design-system';
import { debounce, isEmpty } from 'lodash-es';

import { useI18n, useTracking } from '@hooks';
import { usePostTaskStore } from '@stores';

export const usePostTask = () => {
  const { t } = useI18n();
  const { handlePostTaskError } = usePostTaskAction();

  const {
    setPrice,
    setLoadingPrice,
    resetState,
    setDateTime,
    setIsBookedTask,
    setStepPostTask,
  } = usePostTaskStore();
  const { showLoading, hideLoading } = useAppLoadingStore();

  const { user, getUser } = useUserStore();
  const { isoCode } = useAppStore();
  const { trackingPostTaskSuccess } = useTracking();

  const { mutate: onCreateHousekeepingLocation } = useApiMutation({
    key: EndpointKeys.createHousekeepingLocation,
    options: {
      onMutate: () => {
        showLoading();
      },
      onSettled() {
        hideLoading();
      },
      onError: (error: IApiError) => {
        handleError(error);
      },
      onSuccess: () => {
        getUser();
      },
    },
  });

  const { mutate: onUpdateRoomHousekeepingLocation } = useApiMutation({
    key: EndpointKeys.updateRoomHousekeepingLocation,
    options: {
      onMutate: () => {
        showLoading();
      },
      onSettled() {
        hideLoading();
      },
      onError: (error: IApiError) => {
        handleError(error);
      },
      onSuccess: () => {
        getUser();
      },
    },
  });

  const { mutate: onDeleteRoomHousekeepingLocation } = useApiMutation({
    key: EndpointKeys.deleteRoomHousekeepingLocation,
    options: {
      onMutate: () => {
        showLoading();
      },
      onSettled() {
        hideLoading();
      },
      onError: (error: IApiError) => {
        handleError(error);
      },
    },
  });

  const { mutate: getPriceHousekeeping } =
    useApiMutation<EndpointKeys.getPriceHousekeeping>({
      key: EndpointKeys.getPriceHousekeeping,
      options: {
        onMutate: () => {
          setLoadingPrice(true);
          showLoading();
        },
        onSettled: () => {
          setLoadingPrice(false);
          hideLoading();
        },
      },
    });

  const { mutate: checkTaskSameTime } = useApiMutation({
    key: EndpointKeys.checkTaskSameTime,
    options: {
      onMutate: () => {
        showLoading();
      },
      onSettled() {
        hideLoading();
      },
    },
  });

  const { mutate: postTaskHousekeeping } =
    useApiMutation<EndpointKeys.postTaskHousekeeping>({
      key: EndpointKeys.postTaskHousekeeping,
      options: {
        onSuccess: async (data) => {
          if (data?.bookingId) {
            trackingPostTaskSuccess();
            resetState();
            setIsBookedTask(true);
            await PaymentService.onPostTaskSuccess({
              bookingId: data.bookingId,
              isPrepayment: data.isPrepayment,
            });
          }
        },
        onError: (error: IApiError) => {
          handlePostTaskError(error);
        },
        onMutate: () => {
          showLoading();
        },
        onSettled() {
          hideLoading();
        },
      },
    });

  const getDataPricing = () => {
    // Get the latest state directly from the store
    const currentState = usePostTaskStore.getState();
    const {
      address,
      date,
      duration,
      isAutoChooseTasker,
      service,
      forceTasker,
      dateOptions,
      addons,
      paymentMethod,
      requirements,
      rooms,
      options,
      homeType,
      promotion,
    } = currentState;

    if (!address || !date) {
      return null;
    }

    const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

    // base info
    const task = {
      timezone,
      date: DateTimeHelpers.formatToString({ timezone, date }),
      autoChooseTasker: isAutoChooseTasker,
      taskPlace: {
        country: address?.country,
        city: address?.city,
        district: address?.district,
      },
      homeType: address?.homeType,
      duration: duration,
      // Add payment method and promotion using PostTaskHelpers
      ...PostTaskHelpers.formatDataToParams({ paymentMethod, promotion }),
    };

    if (!isEmpty(forceTasker)) {
      task.forceTasker = forceTasker;
    }
    if (!isEmpty(dateOptions)) {
      task.dateOptions = dateOptions;
    }

    // addOnService
    if (!isEmpty(requirements)) {
      task.requirements = requirements.map((req) => ({ type: req.type })); // send type only
    }
    if (!isEmpty(addons)) {
      task.addons = addons;
    }
    // No get price when no rooms
    if (isEmpty(rooms)) {
      return null;
    }

    let detailHousekeeping = {
      name: homeType?.name,
      text: homeType?.text,
      roomTypes: rooms,
    };

    if (!isEmpty(options)) {
      detailHousekeeping = { ...detailHousekeeping, options };
    }
    const totalRoomNeedToSetUp = options?.find(
      (item: IHouseKeepingOption) => item?.name === 'SETUP_ROOM',
    );

    if (totalRoomNeedToSetUp && totalRoomNeedToSetUp?.quantity) {
      const newOptions = detailHousekeeping?.options || [];
      detailHousekeeping = {
        ...detailHousekeeping,
        options: [...newOptions, totalRoomNeedToSetUp],
      };
    }

    task.detailHousekeeping = detailHousekeeping;

    return { task, service: { _id: service?._id }, isoCode };
  };

  const getPrice = debounce(async () => {
    // refactor data after call get price
    const data = getDataPricing();

    // data is null, no get price
    if (!data) {
      // set price is nul --> hide price button.
      return setPrice(null);
    }

    // call get price API
    getPriceHousekeeping(data, {
      onSuccess: (result) => {
        setPrice(result);
      },
      onError: (error) => {
        handleError(error);
        setPrice(null);
      },
    });
  }, 150);

  const _refactorDataPostTask = () => {
    // Get the latest state directly from the store
    const currentState = usePostTaskStore.getState();
    const {
      address,
      date,
      duration,
      isAutoChooseTasker,
      service,
      paymentMethod,
      requirements,
      isFavouriteTasker,
      isApplyNoteForAllTask,
      note,
      pet,
      schedule,
      isEnabledSchedule,
      rooms,
      options,
      homeType,
      roomNumber,
      listRoomsImages,
      promotion,
    } = currentState;

    const isAddressMaybeWrong = Boolean(address?.isAddressMaybeWrong);
    const isTet = service?.isTet || false;
    const city = address?.city;
    const timezone = DateTimeHelpers.getTimezoneByCity(city);

    // base task info
    const task = {
      address: address?.address,
      contactName: address?.contact || user?.name,
      lat: address?.lat,
      lng: address?.lng,
      phone: address?.phoneNumber || user?.phone,
      countryCode: address?.countryCode || user?.countryCode,
      description: address?.description,
      askerId: user?._id,
      autoChooseTasker: isAutoChooseTasker,
      date: DateTimeHelpers.formatToString({ date: date, timezone }),
      timezone,
      deviceInfo: DeviceHelper.getDeviceInfo(),
      duration: duration,
      homeType: address?.homeType,
      houseNumber: address?.description,
      isSendToFavTaskers: Boolean(isFavouriteTasker),
      isoCode,
      serviceId: service?._id,
      taskPlace: {
        country: address?.country,
        city,
        district: address?.district,
      },
      updateTaskNoteToUser: isApplyNoteForAllTask,
      shortAddress: address?.shortAddress,
      // Add payment method and promotion using PostTaskHelpers
      ...PostTaskHelpers.formatDataToParams({ paymentMethod, promotion }),
    };

    // Refactor phone number - refill 0 at first
    task.phone = getPhoneNumber(task.phone || '', task.countryCode || '');

    // Địa chỉ có thể bị sai => khi book task, send to slack cho team vận hành xử lý
    if (isAddressMaybeWrong) {
      task.taskPlace.isAddressMaybeWrong = isAddressMaybeWrong;
    }

    if (isTet) {
      task.isTetBooking = true;
    }

    if (note && note?.trim()) {
      task.taskNote = note?.trim();
    }

    // pet
    if (pet) {
      task.pet = pet;
    }

    // addOnService
    if (!isEmpty(requirements)) {
      task.requirements = requirements.map((req) => ({ type: req.type })); // send type only
    }
    // add schedule
    if (isEnabledSchedule && !isEmpty(schedule)) {
      task.weekday = schedule;
    }

    // if (!isEmpty(relatedTask?.relatedTaskId)) {
    //   task.source = {
    //     from: relatedTask?.service?.name,
    //     taskId: relatedTask?.relatedTaskId,
    //   };
    // }

    // No get price when no rooms
    if (isEmpty(rooms)) {
      return null;
    }

    let detailHousekeeping = {
      name: homeType?.name,
      text: homeType?.text,
      roomTypes: rooms,
    };

    if (!isEmpty(options)) {
      detailHousekeeping = { ...detailHousekeeping, options };
    }
    const totalRoomNeedToSetUp = options?.find(
      (item: IHouseKeepingOption) => item?.name === 'SETUP_ROOM',
    );

    if (totalRoomNeedToSetUp && totalRoomNeedToSetUp?.quantity) {
      const newOptions = detailHousekeeping?.options || [];
      detailHousekeeping = {
        ...detailHousekeeping,
        options: [...newOptions, totalRoomNeedToSetUp],
      };
    }

    if (roomNumber) {
      detailHousekeeping = {
        ...detailHousekeeping,
        roomNumber: roomNumber,
      };
    }
    if (!isEmpty(listRoomsImages)) {
      const newRooms = [];
      rooms?.map((room) => {
        const imageSet = listRoomsImages?.find(
          (image) => image?.name === room?.name,
        );
        if (imageSet) {
          newRooms.push({ ...room, setupImages: imageSet.images });
          return;
        }
        newRooms.push(room);
        return;
      });
      detailHousekeeping.roomTypes = newRooms;
    }
    task.detailHousekeeping = detailHousekeeping;

    return task;
  };

  /**
   * @description function booking task
   * @param payload {{object}} data of task
   */
  const postTask = async () => {
    const currentState = usePostTaskStore.getState();
    const { date, service, address } = currentState;
    const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

    // Set tracking step for payment completion
    setStepPostTask(TrackingPostTaskStep.STEP_4);

    // check task same time
    checkTaskSameTime(
      {
        taskDate: DateTimeHelpers.formatToString({
          date: date as IDate,
          timezone,
        }),
        serviceId: service?._id || '',
      },
      {
        onSuccess: (data) => {
          _addTask({ isExistTask: !data });
        },
      },
    );
  };

  /**
   * @description function booking task
   * @param payload {{object}} data of task
   */
  const _addTask = debounce(
    async ({ isExistTask }: { isExistTask: boolean }) => {
      const dataTask = _refactorDataPostTask();
      // time ok
      if (isExistTask) {
        // call api book task
        postTaskHousekeeping(dataTask);
        return;
      }

      // same time, alert for user
      return Alert.alert.open({
        title: t('DIALOG_TITLE_INFORMATION'),
        message: t('TASK_SAME_TIME_MESSAGE'),
        actions: [
          { text: t('CLOSE'), style: 'cancel' },
          {
            text: t('OK'),
            onPress: async () => {
              // wait modal close
              setTimeout(async () => {
                postTaskHousekeeping(dataTask);
              }, 300);
            },
          },
        ],
      });
    },
    300,
  );

  const onChangeDateTime = (newDate: IDate) => {
    const currentState = usePostTaskStore.getState();
    const { address, date } = currentState;

    const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);
    const isSame = DateTimeHelpers.checkIsSame({
      firstDate: date,
      secondDate: newDate,
      timezone,
    });
    // check spam, call api with same data
    if (isSame) return null;
    // set new date time
    setDateTime(newDate);
    // get price again
    getPrice();
  };

  return {
    getPrice,
    postTask,
    onCreateHousekeepingLocation,
    onUpdateRoomHousekeepingLocation,
    onDeleteRoomHousekeepingLocation,
    onChangeDateTime,
  };
};
