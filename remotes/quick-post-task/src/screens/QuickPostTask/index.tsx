/**
 * TaskDetail Component for QuickPostTask
 *
 * This component displays and manages the task details including service information,
 * working time, duration, and additional requirements for quick post task rebooking.
 *
 * Migrated from remotes/quick-post-task/src/screens/QuickPostTask/components/TaskDetail
 * to CleaningService structure following super-app architecture patterns.
 */
import React, { useCallback, useEffect } from 'react';
import { AppState, AppStateStatus } from 'react-native';
import {
  BlockView,
  ConditionView,
  isIOS,
  QuickPostTaskRouteName,
  QuickPostTaskStackScreenProps,
  SERVICES,
  TrackingActions,
  TrackingPostTaskStep,
} from '@btaskee/design-system';
import { useIsFocused } from '@react-navigation/native';
import { isEmpty } from 'lodash-es';

import { useAppNavigation, useTracking } from '@hooks';
import { usePostTaskStore } from '@stores';

import { CleaningService } from '../CleaningService';

interface DetailServiceComponents {
  [key: string]: React.ComponentType<any>;
}

const listDetail: DetailServiceComponents = {};
listDetail[SERVICES.CLEANING] = CleaningService;

type QuickPostTaskScreenProps =
  QuickPostTaskStackScreenProps<QuickPostTaskRouteName.QuickPostTask>;
export const QuickPostTaskScreen: React.FC<QuickPostTaskScreenProps> = ({
  route,
}) => {
  const taskDetail = route.params?.taskDetail;
  const DetailServiceComponents =
    listDetail[taskDetail?.serviceName as SERVICES];

  const { setDataTask, setStepPostTask } = usePostTaskStore();
  const {
    trackingQuickPostTaskScreenView,
    trackingBackNextActionChooseService,
    trackingPostTaskAbandoned,
  } = useTracking();
  const isFocused = useIsFocused();
  const navigation = useAppNavigation();

  // Track screen view on component mount
  useEffect(() => {
    trackingQuickPostTaskScreenView();
    setStepPostTask(TrackingPostTaskStep.STEP_2);
  }, [trackingQuickPostTaskScreenView]);

  // Handle tracking for back navigation
  const handleTrackingAction = useCallback(
    (action: TrackingActions) => {
      if (!isFocused) {
        return null;
      }
      trackingBackNextActionChooseService(action);
    },
    [trackingBackNextActionChooseService, isFocused],
  );

  useEffect(() => {
    const unsubscribe = navigation.addListener('beforeRemove', () => {
      handleTrackingAction(TrackingActions.Back);
    });

    return unsubscribe; // Cleanup listener when component unmounts
  }, [navigation, handleTrackingAction]);

  // Handle app state changes for tracking
  const handleAppStateChange = (nextAppState: AppStateStatus) => {
    if (isIOS) {
      if (nextAppState === 'inactive') {
        trackingPostTaskAbandoned(TrackingActions.EXITED_APP);
      }
    } else if (nextAppState === 'background') {
      trackingPostTaskAbandoned(TrackingActions.EXITED_APP);
    }
  };

  useEffect(() => {
    const subscribe = AppState.addEventListener('change', handleAppStateChange);
    return () => {
      trackingPostTaskAbandoned(TrackingActions.TAP_HEADER_BACK);
      subscribe?.remove();
    };
  }, []);

  useEffect(() => {
    if (!isEmpty(taskDetail)) {
      setDataTask(taskDetail);
    }
  }, []);

  return (
    <BlockView flex>
      <ConditionView
        condition={Boolean(DetailServiceComponents)}
        viewTrue={<DetailServiceComponents />}
      />
    </BlockView>
  );
};
