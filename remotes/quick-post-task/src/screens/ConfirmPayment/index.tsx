import React, { useCallback, useEffect, useMemo } from 'react';
import {
  Alert,
  BEFORE_HOUR_POST_TASK_CHOOSE_TASKER,
  BlockView,
  ChooseTaskerBeforeHour,
  ConditionView,
  DatePicker,
  DatePickerTet,
  DateTimeHelpers,
  NotePostTask,
  PostTaskHelpers,
  PriceButton,
  PriceIncrease,
  ScrollView,
  Spacing,
  TaskerFavoriteForRebook,
  TimePicker,
  TrackingActions,
  useSettingsStore,
} from '@btaskee/design-system';
import { useIsFocused } from '@react-navigation/native';
import { get, isEmpty } from 'lodash-es';

import {
  useAppNavigation,
  useI18n,
  usePostTaskCleaning,
  useTracking,
} from '@hooks';
import { usePostTaskStore } from '@stores';

import styles from './styles';

// Import component
export const ConfirmPayment = () => {
  const { t } = useI18n();
  const isFocused = useIsFocused();

  const { onChangeDateTime, postTask } = usePostTaskCleaning();
  const {
    address,
    date,
    isAutoChooseTasker,
    duration,
    note,
    setNote,
    setIsApplyNoteForAllTask,
    isApplyNoteForAllTask,
    price,
    forceTasker,
    service,
    isBookedTask,
  } = usePostTaskStore();
  const navigation = useAppNavigation();

  const { trackingConfirmPaymentScreenView, trackingActionConfirmPayment } =
    useTracking();

  const settings = useSettingsStore().settings;

  const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

  // Track screen view on component mount
  useEffect(() => {
    trackingConfirmPaymentScreenView();
  }, [trackingConfirmPaymentScreenView]);

  // Handle tracking for back navigation
  const handleTrackingAction = useCallback(
    (action: TrackingActions) => {
      if (!isFocused || isBookedTask) {
        return null;
      }
      trackingActionConfirmPayment({ action });
    },
    [trackingActionConfirmPayment, isFocused, isBookedTask],
  );

  useEffect(() => {
    const unsubscribe = navigation.addListener('beforeRemove', () => {
      handleTrackingAction(TrackingActions.Back);
    });

    return unsubscribe; // Cleanup listener when component unmounts
  }, [navigation, handleTrackingAction]);

  const onConfirmed = () => {
    const diff = DateTimeHelpers.diffDate({
      timezone,
      firstDate: date,
      unit: 'hour',
    });
    //Kiểm tra nếu isAutoChooseTasker = false(Asker tự chọn Tasker) và thời gian làm việc nhỏ hơn 3 tiếng thì báo lỗi
    if (!isAutoChooseTasker && diff < BEFORE_HOUR_POST_TASK_CHOOSE_TASKER) {
      return Alert.alert?.open?.({
        title: t('DIALOG_TITLE_INFORMATION'),
        message: <ChooseTaskerBeforeHour />,
        actions: [{ text: t('PT2_POPUP_ERROR_TIME_CLOSE') }],
      });
    }

    // check time before 60min
    if (
      !PostTaskHelpers.validateDateTime(
        timezone,
        date,
        // settings?.settingSystem?.minPostTaskTime,
      )
    ) {
      return Alert.alert?.open?.({
        title: t('DIALOG_TITLE_INFORMATION'),
        message: t('POSTTASK_STEP2_ERROR_TIME', {
          t: settings?.settingSystem?.minPostTaskTime,
        }),
        actions: [{ text: t('PT2_POPUP_ERROR_TIME_CLOSE') }],
      });
    }

    // check posting limit, ex 6AM - 10PM
    const postingLimits = service?.postingLimits;
    if (
      !PostTaskHelpers.checkTimeValidFromService(
        timezone,
        date,
        duration,
        postingLimits,
      )
    ) {
      const postingLimitsFormat = PostTaskHelpers.formatPostingLimits({
        timezone,
        postingLimits,
      });
      return Alert.alert?.open?.({
        title: t('DIALOG_TITLE_INFORMATION'),
        message: t('PT2_POPUP_ERROR_TIME_INVALID_CONTENT', {
          from: postingLimitsFormat.from,
          to: postingLimitsFormat.to,
        }),
        actions: [{ text: t('PT2_POPUP_ERROR_TIME_INVALID_CLOSE') }],
      });
    }

    // data ok
    onGotoConfirm();
  };

  const onGotoConfirm = () => {
    trackingActionConfirmPayment({ action: TrackingActions.Next });
    postTask();
  };

  const shouldRenderChooseDateTime = useMemo(() => {
    return (
      <BlockView>
        {get(service, 'isTet', null) ? (
          // Tet booking need a calendar to choose easily
          <DatePickerTet
            service={service}
            date={date}
            onChangeDateTime={onChangeDateTime}
            settingSystem={settings?.settingSystem}
            timezone={timezone}
          />
        ) : (
          // Normal date picker (supports testID)
          <DatePicker
            testID="date-picker"
            value={date}
            onChange={onChangeDateTime}
            settingSystem={settings?.settingSystem}
            timezone={timezone}
          />
        )}
        <TimePicker
          testID="time-picker"
          value={date}
          onChange={onChangeDateTime}
          settingSystem={settings?.settingSystem}
          timezone={timezone}
        />
      </BlockView>
    );
  }, [date, service?.isTet, isFocused, timezone]);

  return (
    <BlockView
      inset={['bottom']}
      style={styles.container}
    >
      <BlockView style={styles.content}>
        <ScrollView
          testID="scrollChooseDateTime"
          contentContainerStyle={styles.containerScroll}
          showsVerticalScrollIndicator={false}
        >
          <ConditionView
            condition={!isEmpty(forceTasker)}
            viewTrue={
              <BlockView
                padding={{
                  horizontal: Spacing.SPACE_16,
                  top: Spacing.SPACE_16,
                }}
              >
                <TaskerFavoriteForRebook forceTasker={forceTasker} />
              </BlockView>
            }
          />
          {shouldRenderChooseDateTime}
          <PriceIncrease
            price={price}
            address={address}
            service={service}
            increaseReasons={get(price, 'increaseReasons', false)}
            isShow={Boolean(get(price, 'isIncrease', false))}
          />

          <NotePostTask
            title={t('LABEL_NOTE_FOR_TASKER')}
            description={t('TASK_NOTE_DESCRIPTION')}
            setNote={setNote}
            value={note}
            service={service}
            isApplyNoteForAllTask={isApplyNoteForAllTask}
            setNoteForAllTask={setIsApplyNoteForAllTask}
            containerStyle={{ marginTop: 24 }}
          />
        </ScrollView>
      </BlockView>
      <PriceButton
        testID="btnNextStep3"
        onPress={onConfirmed}
        fromScreen={service?.name}
        pricePostTask={price}
      />
    </BlockView>
  );
};
