/**
 * Styles for the ChooseDateTime screen
 */
import { Dimensions, StyleSheet } from 'react-native';
import {
  BorderRadius,
  Colors,
  Shadows,
  Spacing,
} from '@btaskee/design-system';

const { width } = Dimensions.get('window');

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.neutralBackground,
  },
  content: {
    flex: 1,
    backgroundColor: Colors.neutralWhite,
  },
  containerScroll: {
    paddingBottom: width / 3,
  },
  boxCollectionDate: {
    paddingBottom: Spacing.SPACE_16,
  },
  boxChooseDateTime: {},
  wrapConflictTime: {
    marginHorizontal: Spacing.SPACE_16,
    marginTop: Spacing.SPACE_16,
    padding: Spacing.SPACE_16,
    backgroundColor: Colors.orange100,
    borderRadius: BorderRadius.RADIUS_08,
    borderWidth: 1,
    borderColor: Colors.orange500,
  },
  wrapBtnSeeSchedule: {
    position: 'absolute',
    left: 0,
    bottom: 0,
    right: 0,
    padding: Spacing.SPACE_16,
    ...Shadows.SHADOW_1,
    backgroundColor: Colors.neutralWhite,
  },
  btnSeeSchedule: {
    backgroundColor: Colors.green500,
    borderRadius: BorderRadius.RADIUS_08,
    paddingVertical: Spacing.SPACE_16,
    alignItems: 'center',
  },
});
