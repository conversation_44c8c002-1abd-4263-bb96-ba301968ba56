/**
 * @Filename: remotes/quick-post-task/src/screens/ChooseServiceScreen/index.tsx
 * @Description: Choose Service Screen - migrated from legacy PostTaskStep1
 * @CreatedAt: 2024-08-25
 * @Author: Augment Agent
 * @UpdatedBy: Augment Agent
 **/

import React, { useCallback, useEffect } from 'react';
import {
  BlockView,
  getRouteNameOfService,
  NavigationService,
  QuickPostTaskRouteName,
  QuickPostTaskStackScreenProps,
  RouteName,
  TaskManagementRouteName,
  TrackingActions,
} from '@btaskee/design-system';
import { isEmpty } from 'lodash-es';

import { useTracking } from '@hooks';
import { usePostTaskStore } from '@stores';

import { BookingButton } from './components/BookingButton';
import { RecentTaskItem } from './components/RecentTaskItem';
import { styles } from './styles';

type ChooseServiceScreenProps =
  QuickPostTaskStackScreenProps<QuickPostTaskRouteName.ChooseService>;

/**
 * ChooseServiceScreen component for quick post task functionality
 * Purpose: Displays recent task information and provides booking options
 * @param props - Navigation props containing route parameters and navigation functions
 * @returns JSX element representing the choose service screen
 */
export const ChooseServiceScreen: React.FC<ChooseServiceScreenProps> = (
  props,
) => {
  const { route } = props;

  const { setDataTask, dataTask } = usePostTaskStore();

  const { trackingBackNextActionChooseService } = useTracking();
  const initData = () => {
    if (!isEmpty(route?.params?.taskDetail)) {
      return setDataTask(route?.params?.taskDetail);
    }
  };

  // Handle component mount effects
  useEffect(() => {
    initData();
  }, []);

  /**
   * Handles booking button press
   * Purpose: Navigates to next step in booking process
   */
  const handleBookingPress = useCallback(() => {
    trackingBackNextActionChooseService(TrackingActions.Next);
    const routeName = getRouteNameOfService(dataTask?.serviceName);
    if (routeName) {
      NavigationService.navigate(routeName);
    }
  }, [dataTask?.serviceName, trackingBackNextActionChooseService]);

  /**
   * Handles task detail press
   * Purpose: Navigates to task detail screen
   */
  const handleTaskDetailPress = useCallback(() => {
    NavigationService.navigate(RouteName.TaskManagement, {
      screen: TaskManagementRouteName.TaskDetail,
      params: {
        taskId: dataTask?._id,
      },
    });
  }, [dataTask?._id]);

  /**
   * Handles rebooking press
   * Purpose: Navigates to quick post task with existing data
   */
  const onRebookPress = () => {
    if (isEmpty(dataTask)) {
      return;
    }

    trackingBackNextActionChooseService(TrackingActions.Next);
    NavigationService.navigate(RouteName.QuickPostTask, {
      screen: QuickPostTaskRouteName.QuickPostTask,
      params: {
        taskDetail: dataTask,
      },
    });
  };

  const isViewOnly = false; // Remove isTet property as it doesn't exist in the type

  return (
    <BlockView
      inset={'bottom'}
      style={styles.container}
    >
      <RecentTaskItem
        viewOnly={isViewOnly}
        testID="recentTaskItem"
        onTaskDetailPress={handleTaskDetailPress}
        onRebookPress={onRebookPress}
      />
      <BookingButton
        testID="btnNewBooking"
        onPress={handleBookingPress}
      />
    </BlockView>
  );
};
