/**
 * CleaningService Component
 *
 * Displays comprehensive cleaning service task details including working time,
 * workload, requirements, pet information, notes, and premium status.
 * Refactored to use extracted components for better maintainability.
 *
 * @CreatedAt: 4/12/2020
 * @Author: Duc<PERSON><PERSON>, HuuToan
 * @RefactoredAt: Current Date
 * @RefactoredBy: AI Assistant
 */
import React, { useCallback, useEffect, useState } from 'react';
import {
  BlockView,
  BookingButton,
  CText,
  DateTimeHelpers,
  getRouteNameOfService,
  LocationPostTask,
  NavigationService,
  PaymentMethodBlock,
  ScrollView,
  TrackingActions,
  TYPE_OF_PAYMENT,
  useSettingsStore,
} from '@btaskee/design-system';
import { isEmpty } from 'lodash-es';

import { useI18n, usePostTaskCleaning, useTracking } from '@hooks';
import { usePostTaskStore } from '@stores';

import {
  AdvanceOptions,
  PetSection,
  PremiumSection,
  RequirementsSection,
  WorkingTimeQuickPostTask,
  WorkloadSection,
} from './components';
import { styles } from './styles';

export const CleaningService = () => {
  const {
    address,
    price,
    setDateTime,
    setAddress,
    setDuration,
    paymentMethod,
    promotion,
    setPromotion,
    setPaymentMethod,
    setService,
    dataTask,
    duration,
    date,
    service,
  } = usePostTaskStore();
  const { t } = useI18n();

  const { settings } = useSettingsStore();
  const { getPrice, postTask } = usePostTaskCleaning();

  const { trackingBackNextActionQuickPostTask } = useTracking();

  const [isInitialized, setIsInitialized] = useState(false);
  const timezone = DateTimeHelpers.getTimezoneByCity(dataTask?.taskPlace?.city);

  /**
   * Initializes the task data and sets up the form state
   */
  const initializeTaskData = useCallback(async () => {
    if (!dataTask || isInitialized) return;

    try {
      const serviceData = settings?.services?.find(
        (e) => e.name === dataTask?.serviceName,
      );

      // Set service data
      if (!isEmpty(serviceData)) {
        setService(serviceData);
      }

      // Set duration
      if (dataTask?.duration) {
        setDuration(dataTask?.duration);
      }

      // Set address data
      if (dataTask?.address && dataTask?.taskPlace) {
        setAddress({
          address: dataTask?.address,
          shortAddress: dataTask?.shortAddress,
          contact: dataTask?.contactName,
          phoneNumber: dataTask?.phone,
          countryCode: dataTask?.countryCode as any,
          description: dataTask?.description,
          lat: dataTask?.lat,
          lng: dataTask?.lng,
          city: dataTask?.taskPlace.city,
          country: dataTask?.taskPlace.country as any,
          district: dataTask?.taskPlace.district,
        });
      }

      // Set date to tomorrow with same time as original task
      if (dataTask?.date && timezone) {
        const originalDate = new Date(dataTask?.date);
        const tomorrowDate = DateTimeHelpers.toDayTz({ timezone })
          .add(1, 'day')
          .set(
            'hour',
            DateTimeHelpers.getHour({ timezone, date: originalDate }),
          )
          .set(
            'minute',
            DateTimeHelpers.getMinute({ timezone, date: originalDate }),
          )
          .startOf('minute');

        setDateTime(tomorrowDate.toDate());
      }

      // Set payment method with validation
      if (dataTask?.payment?.method) {
        // TODO: Implement payment method validation and card info retrieval
        // This will be handled in the payment method migration
      }

      // Clear any existing promotion
      setPromotion(null);

      setIsInitialized(true);

      // Get initial price
      setTimeout(() => {
        getPrice();
      }, 100);
    } catch (error) {}
  }, [dataTask, isInitialized]);

  /**
   * Handles task data retrieval if incomplete data is provided
   */
  const getTaskDetailData = async () => {
    if (!isEmpty(dataTask)) {
      initializeTaskData();
    }
  };

  /**
   * Handles the booking process
   */
  const handleBooking = useCallback(async () => {
    trackingBackNextActionQuickPostTask(TrackingActions.Next);
    await postTask();
  }, [postTask, trackingBackNextActionQuickPostTask]);

  const handleChangeOptions = async () => {
    const routeName = getRouteNameOfService(dataTask?.serviceName);
    if (routeName) {
      // default go home page and go to step 2
      if (NavigationService.canGoBack()) {
        NavigationService.popToTop();
      }
      NavigationService.navigate(routeName, {
        defaultAddress: {
          address: dataTask?.address,
          shortAddress: dataTask?.shortAddress,
          contact: dataTask?.contactName,
          phoneNumber: dataTask?.phone,
          countryCode: dataTask?.countryCode,
          description: dataTask?.description,
          lat: dataTask?.lat,
          lng: dataTask?.lng,
          city: dataTask?.taskPlace?.city,
          country: dataTask?.taskPlace?.country,
          district: dataTask?.taskPlace?.district,
        },
      });
    }
  };

  // Initialize data on component mount
  useEffect(() => {
    getTaskDetailData();
  }, []);

  // Show loading or empty state if data is not ready
  if (!dataTask || !isInitialized) {
    return (
      <BlockView style={styles.container}>
        {/* TODO: Add loading component */}
      </BlockView>
    );
  }

  return (
    <BlockView
      style={styles.container}
      testID="quick-post-task-screen"
    >
      <ScrollView
        testID="quick-post-task-scroll"
        contentContainerStyle={styles.content}
        showsVerticalScrollIndicator={false}
      >
        <LocationPostTask
          address={address || {}}
          homeNumber={address?.description || ''}
          setAddress={setAddress}
        />

        <BlockView style={styles.panel}>
          <CText
            bold
            style={styles.txtPanel}
          >
            {t('TASK_DETAIL')}
          </CText>

          <BlockView style={styles.card}>
            <WorkingTimeQuickPostTask timezone={timezone} />

            <BlockView style={styles.wrapDetail}>
              <CText
                bold
                style={styles.subPanel}
              >
                {t('TASK_DETAIL')}
              </CText>

              <WorkloadSection duration={duration} />
              <RequirementsSection requirements={dataTask?.requirements} />
              <PetSection pets={dataTask?.pet} />
              <PremiumSection isPremium={dataTask?.isPremium} />
            </BlockView>
          </BlockView>
        </BlockView>

        <PaymentMethodBlock
          serviceName={service?.name}
          type={TYPE_OF_PAYMENT.bookTask}
          promotionOptions={{
            currentPromotion: promotion,
            taskCost: price?.cost,
            taskDate: date,
            taskPlace: {
              country: address?.country,
              city: address?.city,
              district: address?.district,
            },
          }}
          paymentMethodOptions={{
            currentPaymentMethod: paymentMethod,
          }}
          onChangePromotion={setPromotion}
          onChangePaymentMethod={setPaymentMethod}
        />

        <AdvanceOptions handleChangeOptions={handleChangeOptions} />
      </ScrollView>

      <BookingButton
        onPostTask={handleBooking}
        price={price}
        isDisabled={!price}
      />
    </BlockView>
  );
};
