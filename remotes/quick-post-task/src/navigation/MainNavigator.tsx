import '../i18n';

import React, { useCallback } from 'react';
import {
  Colors,
  NavBar,
  QuickPostTaskRouteName,
  QuickPostTaskStackParamList,
} from '@btaskee/design-system';
import {
  createNativeStackNavigator,
  type NativeStackHeaderProps,
  type NativeStackNavigationOptions,
} from '@react-navigation/native-stack';

import { useI18n } from '@hooks';
import {
  ChooseServiceScreen,
  ConfirmPayment,
  QuickPostTaskScreen,
} from '@screens';

const Stack = createNativeStackNavigator<QuickPostTaskStackParamList>();

const QuickPostTaskNavigator = () => {
  const { t } = useI18n();

  const screenOptions = useCallback(
    ({ navigation }: any): NativeStackNavigationOptions => ({
      headerShown: true,
      animation: 'slide_from_right' as const,
      animationDuration: 200,
      contentStyle: { backgroundColor: Colors.neutralWhite },
      // eslint-disable-next-line react/no-unstable-nested-components
      header: (props: NativeStackHeaderProps) => {
        // Get the title from options
        const getTitle = () => {
          if (
            props?.options?.headerTitle &&
            typeof props.options.headerTitle === 'function'
          ) {
            // @ts-ignore - React Navigation headerTitle function compatibility
            return props.options.headerTitle();
          }
          if (
            props?.options?.headerTitle &&
            typeof props.options.headerTitle === 'string'
          ) {
            return props.options.headerTitle;
          }
          if (props?.options?.title) {
            return props.options.title;
          }
          return '';
        };

        return (
          <NavBar
            // @ts-ignore - NavBar title accepts ReactNode but types are strict
            title={getTitle()}
            backgroundColor={Colors.neutralWhite}
            onGoBack={() => navigation.goBack()}
          />
        );
      },
    }),
    [],
  );

  return (
    <Stack.Navigator
      screenOptions={screenOptions}
      initialRouteName={QuickPostTaskRouteName.ChooseService}
    >
      <Stack.Screen
        name={QuickPostTaskRouteName.ChooseService}
        component={ChooseServiceScreen}
        options={{ title: t('QUICK_POST_TASK_TITLE') }}
      />
      <Stack.Screen
        name={QuickPostTaskRouteName.QuickPostTask}
        component={QuickPostTaskScreen}
        options={{ title: t('QUICK_POST_TASK_TITLE') }}
      />
      <Stack.Screen
        name={QuickPostTaskRouteName.ConfirmPayment}
        component={ConfirmPayment}
        options={{ title: t('WORK_TIME_TITLE') }}
      />
    </Stack.Navigator>
  );
};

export default QuickPostTaskNavigator;
