/**
 * Comprehensive tracking hook for quick-post-task service
 * Provides complete tracking functionality with multi-provider architecture
 *
 * Features:
 * - Multi-provider tracking (CleverTap, Appmetrica, Firebase Analytics)
 * - Provider-agnostic interface using TrackingServices
 * - Complete tracking parity with service-elderly-care
 * - Quick-post-task specific adaptations
 * - TypeScript type safety throughout
 * - Performance optimized with useCallback
 */

import { useCallback } from 'react';
import {
  TrackingActions,
  TrackingScreenNames,
  TrackingServices,
} from '@btaskee/design-system';

import { usePostTaskStore } from '@stores';

export const useTracking = () => {
  /**
   * Track back/next actions for choose service screen (Step 1)
   * @param action - The tracking action (Back, Next, etc.)
   */
  const trackingBackNextActionChooseService = useCallback(
    (action: TrackingActions) => {
      const currentState = usePostTaskStore.getState();
      const { service, dataTask } = currentState;

      TrackingServices.trackingServiceClick({
        screenName: TrackingScreenNames.ChooseService,
        serviceName: service?.name,
        serviceType: TrackingScreenNames.QuickPostTask,
        action: action,
        isTetBooking: service?.isTet,
        additionalInfo: {
          quickPostTaskDetails: {
            hasRecentTask: Boolean(dataTask),
            taskId: dataTask?._id,
            serviceName: dataTask?.serviceName,
          },
        },
      });
    },
    [],
  );

  /**
   * Track screen view for choose service screen
   */
  const trackingChooseServiceScreenView = useCallback((entryPoint?: string) => {
    const currentState = usePostTaskStore.getState();
    const { service, dataTask } = currentState;

    TrackingServices.trackingServiceView({
      screenName: TrackingScreenNames.ChooseService,
      serviceName: service?.name,
      serviceType: TrackingScreenNames.QuickPostTask,
      entryPoint: entryPoint || TrackingScreenNames.ListOfPlaces,
      isTetBooking: service?.isTet,
      additionalInfo: {
        quickPostTaskDetails: {
          hasRecentTask: Boolean(dataTask),
          taskId: dataTask?._id,
        },
      },
    });
  }, []);

  /**
   * Track screen view for quick post task detail screen
   */
  const trackingQuickPostTaskScreenView = useCallback(() => {
    const currentState = usePostTaskStore.getState();
    const { service, dataTask, duration, requirements, isPremium } =
      currentState;

    TrackingServices.trackingServiceView({
      screenName: TrackingScreenNames.DetailInformation,
      serviceName: service?.name,
      serviceType: TrackingScreenNames.QuickPostTask,
      isTetBooking: service?.isTet,
      additionalInfo: {
        quickPostTaskDetails: {
          taskId: dataTask?._id,
          duration: duration,
          requirements: requirements,
          isPremium: isPremium,
        },
      },
    });
  }, []);

  /**
   * Track back/next actions for quick post task detail screen (Step 2)
   * @param action - The tracking action (Back, Next, etc.)
   */
  const trackingBackNextActionQuickPostTask = useCallback(
    (action: TrackingActions) => {
      const currentState = usePostTaskStore.getState();
      const { service, dataTask, duration, requirements, isPremium, addons } =
        currentState;

      TrackingServices.trackingServiceClick({
        screenName: TrackingScreenNames.DetailInformation,
        serviceName: service?.name,
        serviceType: TrackingScreenNames.QuickPostTask,
        action: action,
        isTetBooking: service?.isTet,
        additionalInfo: {
          quickPostTaskDetails: {
            taskId: dataTask?._id,
            duration: duration,
            requirements: requirements,
            isPremium: isPremium,
            addons: addons,
          },
        },
      });
    },
    [],
  );

  /**
   * Track post task abandonment (when user exits app or goes back)
   * @param action - The tracking action that caused abandonment
   */
  const trackingPostTaskAbandoned = useCallback((action: TrackingActions) => {
    const currentState = usePostTaskStore.getState();
    const { service, dataTask } = currentState;

    TrackingServices.trackingServiceClick({
      screenName: TrackingScreenNames.DetailInformation,
      serviceName: service?.name,
      serviceType: TrackingScreenNames.QuickPostTask,
      action: action,
      isTetBooking: service?.isTet,
      additionalInfo: {
        quickPostTaskDetails: {
          taskId: dataTask?._id,
          abandonedAt: new Date().toISOString(),
        },
      },
    });
  }, []);

  /**
   * Track successful post task completion
   */
  const trackingPostTaskSuccess = useCallback(() => {
    const currentState = usePostTaskStore.getState();
    const { service, dataTask, duration, price } = currentState;

    TrackingServices.trackingServiceClick({
      screenName: TrackingScreenNames.PostTaskSuccess,
      serviceName: service?.name,
      serviceType: TrackingScreenNames.QuickPostTask,
      action: TrackingActions.Next,
      isTetBooking: service?.isTet,
      additionalInfo: {
        quickPostTaskDetails: {
          taskId: dataTask?._id,
          duration: duration,
          totalPrice: price?.finalCost || price?.totalCost,
          completedAt: new Date().toISOString(),
        },
      },
    });
  }, []);

  /**
   * Track screen view for date/time selection screen
   */
  const trackingConfirmPaymentScreenView = useCallback(() => {
    const currentState = usePostTaskStore.getState();
    const { service, dataTask } = currentState;

    TrackingServices.trackingServiceView({
      screenName: TrackingScreenNames.ConfirmPayment,
      serviceName: service?.name,
      serviceType: TrackingScreenNames.QuickPostTask,
      isTetBooking: service?.isTet,
      additionalInfo: {
        quickPostTaskDetails: {
          taskId: dataTask?._id,
        },
      },
    });
  }, []);

  const trackingActionConfirmPayment = useCallback(
    ({ action }: { action: TrackingActions }) => {
      const currentState = usePostTaskStore.getState();
      const { service, paymentMethod, address, promotion, isBookedTask } =
        currentState;

      if (isBookedTask) {
        return null;
      }

      TrackingServices.trackingServiceClick({
        // campaignName: service?.isTet ? configSpecialPreBooking?.name : null,
        screenName: TrackingScreenNames.ConfirmPayment,
        serviceName: service?.name,
        serviceType: TrackingScreenNames.QuickPostTask,
        action,
        isTetBooking: service?.isTet,
        additionalInfo: {
          phoneNumber: address?.phoneNumber,
          contactName: address?.contact,
          paymentMethod: {
            method: paymentMethod?.name,
            promotion: promotion?.code,
          },
        },
      });
    },
    [],
  );

  return {
    trackingBackNextActionChooseService,
    trackingChooseServiceScreenView,
    trackingQuickPostTaskScreenView,
    trackingBackNextActionQuickPostTask,
    trackingPostTaskAbandoned,
    trackingPostTaskSuccess,
    trackingConfirmPaymentScreenView,
    trackingActionConfirmPayment,
  };
};
