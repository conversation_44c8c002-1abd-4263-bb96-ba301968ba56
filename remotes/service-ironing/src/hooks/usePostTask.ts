import {
  Al<PERSON>,
  DateTimeHel<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Endpoint<PERSON>eys,
  getPhoneNumber,
  handleError,
  IApiError,
  IDate,
  PaymentService,
  PostTaskHelpers,
  useApiMutation,
  useAppLoadingStore,
  useAppStore,
  usePostTaskAction,
  useUserStore,
} from '@btaskee/design-system';
import { debounce, isEmpty } from 'lodash-es';

import { useI18n } from '@hooks';
import { usePostTaskStore } from '@stores';

import { useTracking } from './useTracking';

export const usePostTask = () => {
  const { t } = useI18n();
  const { trackingPostTaskSuccess } = useTracking();

  const { setPrice, setLoadingPrice, resetState, setIsBookedTask } =
    usePostTaskStore();
  const { showLoading, hideLoading } = useAppLoadingStore();
  const { handlePostTaskError } = usePostTaskAction();

  const { user } = useUserStore();
  const { isoCode } = useAppStore();

  const { mutate: getPriceIroning } =
    useApiMutation<EndpointKeys.getPriceIroning>({
      key: EndpointKeys.getPriceIroning,
      options: {
        onMutate: () => {
          setLoadingPrice(true);
          showLoading();
        },
        onSettled: () => {
          setLoadingPrice(false);
          hideLoading();
        },
      },
    });

  const { mutate: checkTaskSameTime } = useApiMutation({
    key: EndpointKeys.checkTaskSameTime,
    options: {
      onMutate: () => {
        showLoading();
      },
      onSettled() {
        hideLoading();
      },
    },
  });

  const { mutate: getOutstandingPayment } = useApiMutation({
    key: EndpointKeys.getOutstandingPayment,
  });

  const { mutate: bookTaskForceTasker } =
    useApiMutation<EndpointKeys.bookTaskForceTasker>({
      key: EndpointKeys.bookTaskForceTasker,
      options: {
        onSuccess: async (data) => {
          // success
          if (data?.bookingId) {
            // Track successful task posting
            trackingPostTaskSuccess();

            resetState();
            setIsBookedTask(true);

            // Payment processing (includes navigation)
            await PaymentService.onPostTaskSuccess({
              bookingId: data.bookingId,
              isPrepayment: data.isPrepayment,
            });
          }
        },
        onError: (error: IApiError) => {
          handlePostTaskError(error);
        },
        onMutate: () => {
          showLoading();
        },
        onSettled() {
          hideLoading();
        },
      },
    });
  const { mutate: postTaskIroning } =
    useApiMutation<EndpointKeys.postTaskIroning>({
      key: EndpointKeys.postTaskIroning,
      options: {
        onSuccess: async (data) => {
          // success
          if (data?.bookingId) {
            // Track successful task posting
            trackingPostTaskSuccess();

            resetState();
            setIsBookedTask(true);

            // Payment processing (includes navigation)
            await PaymentService.onPostTaskSuccess({
              bookingId: data.bookingId,
              isPrepayment: data.isPrepayment,
            });
          }
        },
        onError: (error: IApiError) => {
          handlePostTaskError(error);
        },
        onMutate: () => {
          showLoading();
        },
        onSettled() {
          hideLoading();
        },
      },
    });

  const getDataPricing = () => {
    // Get the latest state directly from the store
    const currentState = usePostTaskStore.getState();
    const {
      address,
      date,
      duration,
      isAutoChooseTasker,
      service,
      forceTasker,
      dateOptions,
      addons,
      paymentMethod,
      requirements,
      isPremium,
      promotion,
    } = currentState;

    if (!address || !date || !duration) {
      return null;
    }

    const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

    // base info
    const task = {
      timezone,
      date: DateTimeHelpers.formatToString({ timezone, date }),
      autoChooseTasker: isAutoChooseTasker,
      taskPlace: {
        country: address?.country,
        city: address?.city,
        district: address?.district,
      },
      homeType: address?.homeType,
      duration: duration,
      // Add payment method and promotion using PostTaskHelpers
      ...PostTaskHelpers.formatDataToParams({ paymentMethod, promotion }),
    };

    if (!isEmpty(forceTasker)) {
      task.forceTasker = forceTasker;
    }
    if (!isEmpty(dateOptions)) {
      task.dateOptions = dateOptions;
    }
    // addOnService
    if (!isEmpty(requirements)) {
      task.requirements = requirements.map((req) => ({ type: req.type })); // send type only
    }
    if (!isEmpty(addons)) {
      task.addons = addons;
    }
    // Check premium service
    if (isPremium) {
      task.isPremium = true;
    }
    return { task, service: { _id: service?._id }, isoCode };
  };

  const getPrice = debounce(async () => {
    // refactor data after call get price
    const data = getDataPricing();

    // data is null, no get price
    if (!data) {
      // set price is nul --> hide price button.
      return setPrice(null);
    }

    // call get price API
    getPriceIroning(data, {
      onSuccess: (result) => {
        setPrice(result);
      },
      onError: (error) => {
        handleError(error);
        setPrice(null);
      },
    });
  }, 150);

  const _refactorDataPostTask = () => {
    // Get the latest state directly from the store
    const currentState = usePostTaskStore.getState();
    const {
      address,
      date,
      duration,
      isAutoChooseTasker,
      service,
      forceTasker,
      dateOptions,
      addons,
      paymentMethod,
      requirements,
      isPremium,
      isFavouriteTasker,
      isApplyNoteForAllTask,
      note,
      pet,
      relatedTask,
      schedule,
      isEnabledSchedule,
      gender,
      promotion,
    } = currentState;

    const isAddressMaybeWrong = Boolean(address?.isAddressMaybeWrong);
    const isTet = service?.isTet || false;
    const city = address?.city;
    const timezone = DateTimeHelpers.getTimezoneByCity(city);

    // base task info
    const task = {
      address: address?.address,
      contactName: address?.contact || user?.name,
      lat: address?.lat,
      lng: address?.lng,
      phone: address?.phoneNumber || user?.phone,
      countryCode: address?.countryCode || user?.countryCode,
      description: address?.description,
      askerId: user?._id,
      autoChooseTasker: isAutoChooseTasker,
      date: DateTimeHelpers.formatToString({ date: date, timezone }),
      timezone,
      deviceInfo: DeviceHelper.getDeviceInfo(),
      duration: duration,
      homeType: address?.homeType,
      houseNumber: address?.description,
      isSendToFavTaskers: Boolean(isFavouriteTasker),
      isoCode,
      serviceId: service?._id,
      taskPlace: {
        country: address?.country,
        city,
        district: address?.district,
      },
      updateTaskNoteToUser: isApplyNoteForAllTask,
      shortAddress: address?.shortAddress,
      // Add payment method and promotion using PostTaskHelpers
      ...PostTaskHelpers.formatDataToParams({ paymentMethod, promotion }),
    };

    // Refactor phone number - refill 0 at first
    task.phone = getPhoneNumber(task.phone || '', task.countryCode || '');

    // Địa chỉ có thể bị sai => khi book task, send to slack cho team vận hành xử lý
    if (isAddressMaybeWrong) {
      task.taskPlace.isAddressMaybeWrong = isAddressMaybeWrong;
    }

    if (isTet) {
      task.isTetBooking = true;
    }

    if (note && note?.trim()) {
      task.taskNote = note?.trim();
    }

    // pet
    if (pet) {
      task.pet = pet;
    }

    // addOnService
    if (!isEmpty(requirements)) {
      task.requirements = requirements.map((req) => ({ type: req.type })); // send type only
    }
    // add schedule
    if (isEnabledSchedule && !isEmpty(schedule)) {
      task.weekday = schedule;
    }

    // premium service
    if (isPremium) {
      task.isPremium = isPremium;
    }

    // Asker tự chọn giới tính của Tasker
    if (gender) {
      task.gender = gender;
    }

    // Asker chọn Tasker
    if (forceTasker?._id) {
      delete task.isSendToFavTaskers;
      task.autoChooseTasker = true;
      task.forceTasker = {
        taskerId: forceTasker?._id,
        isResent: Boolean(forceTasker?.isResent),
      };
    }

    // Asker chọn lịch của Tasker
    if (dateOptions) {
      task.dateOptions = dateOptions;
    }

    // Nếu có addons thì thêm addons vào
    if (!isEmpty(addons)) {
      task.addons = addons;
    }

    if (!isEmpty(relatedTask?.relatedTaskId)) {
      task.source = {
        from: relatedTask?.service?.name,
        taskId: relatedTask?.relatedTaskId,
      };
    }

    return task;
  };

  const _postTaskProvider = async () => {
    const dataTask = _refactorDataPostTask();
    if (isEmpty(dataTask?.dateOptions)) {
      delete dataTask.dateOptions;
    }
    if (!isEmpty(dataTask?.forceTasker)) {
      bookTaskForceTasker(dataTask);
      return;
    }
    postTaskIroning(dataTask);
  };

  /**
   * @description function booking task
   */
  const postTask = async () => {
    const currentState = usePostTaskStore.getState();
    const { date, service, address } = currentState;
    const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

    // check task same time
    checkTaskSameTime(
      {
        taskDate: DateTimeHelpers.formatToString({
          date: date as IDate,
          timezone,
        }),
        serviceId: service?._id || '',
      },
      {
        onSuccess: (data) => {
          _addTask({ isExistTask: !data });
        },
      },
    );
  };

  /**
   * @description function booking task
   * @param payload {{object}} data of task
   */
  const _addTask = debounce(
    async ({ isExistTask }: { isExistTask: boolean }) => {
      // time ok
      if (isExistTask) {
        // call api book task
        _postTaskProvider();
        return;
      }

      // same time, alert for user
      return Alert.alert.open({
        title: t('DIALOG_TITLE_INFORMATION'),
        message: t('TASK_SAME_TIME_MESSAGE'),
        actions: [
          { text: t('CLOSE'), style: 'cancel' },
          {
            text: t('OK'),
            onPress: async () => {
              // wait modal close
              setTimeout(async () => {
                _postTaskProvider();
              }, 300);
            },
          },
        ],
      });
    },
    300,
  );

  return { getPrice, postTask };
};
