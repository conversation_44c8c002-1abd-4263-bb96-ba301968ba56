import '../i18n';

import React, { useCallback, useEffect, useMemo } from 'react';
import {
  Colors,
  ConfigHelpers,
  IroningRouteName,
  IroningStackParamList,
  IService,
  NavBar,
  SERVICES,
  useSettingsStore,
} from '@btaskee/design-system';
import {
  createNativeStackNavigator,
  type NativeStackHeaderProps,
  type NativeStackNavigationOptions,
} from '@react-navigation/native-stack';
import { isEmpty } from 'lodash-es';

import { useI18n } from '@hooks';
import {
  ChooseAddress,
  ChooseDateTime,
  ChooseDuration,
  ConfirmBooking,
  IntroService,
  PostTaskSuccess,
} from '@screens';
import { usePostTaskStore } from '@stores';

const Stack = createNativeStackNavigator<IroningStackParamList>();

const MainNavigator = ({ route }: any) => {
  const promotion = route?.params?.promotion;

  const { t } = useI18n();
  const { setService, setPromotion, isFirstOpenService } = usePostTaskStore();
  const settings = useSettingsStore().settings;

  // Memoize the cleaning service to avoid unnecessary re-computations
  const ironingService = useMemo(() => {
    return settings?.services?.find(
      (service: IService) => service?.name === SERVICES.IRONING,
    );
  }, [settings?.services]);

  // Use useCallback to prevent unnecessary re-renders
  const initData = useCallback(async () => {
    if (ironingService) {
      setService(ironingService);
    }
    if (!isEmpty(promotion)) {
      setPromotion(promotion);
    }
  }, [ironingService, setService, setPromotion]);

  useEffect(() => {
    initData();
  }, [initData]);

  // Screen options following the established pattern across all microservices
  const screenOptions = useCallback(
    ({ navigation }: any): NativeStackNavigationOptions => ({
      headerShown: true,
      animation: 'slide_from_right',
      animationDuration: 200,
      contentStyle: { backgroundColor: Colors.neutralWhite },
      // eslint-disable-next-line react/no-unstable-nested-components
      header: (props: NativeStackHeaderProps) => {
        // Get the title from options
        const getTitle = () => {
          if (
            props?.options?.headerTitle &&
            typeof props.options.headerTitle === 'function'
          ) {
            // @ts-ignore - React Navigation headerTitle function compatibility
            return props.options.headerTitle();
          }
          if (
            props?.options?.headerTitle &&
            typeof props.options.headerTitle === 'string'
          ) {
            return props.options.headerTitle;
          }
          if (props?.options?.title) {
            return props.options.title;
          }
          return '';
        };

        return (
          <NavBar
            // @ts-ignore - NavBar title accepts ReactNode but types are strict
            title={getTitle()}
            backgroundColor={Colors.neutralWhite}
            onGoBack={() => navigation.goBack()}
            isShadow={true}
          />
        );
      },
    }),
    [],
  );

  return (
    <Stack.Navigator
      screenOptions={screenOptions}
      initialRouteName={
        isFirstOpenService && !ConfigHelpers.isE2ETesting
          ? IroningRouteName.IntroService
          : IroningRouteName.ChooseAddress
      }>
      <Stack.Screen
        name={IroningRouteName.IntroService}
        component={IntroService}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={IroningRouteName.ChooseAddress}
        component={ChooseAddress}
        options={{
          title: t('LIST_OF_LOCATIONS'),
        }}
      />
      <Stack.Screen
        name={IroningRouteName.ChooseDuration}
        component={ChooseDuration}
      />
      <Stack.Screen
        name={IroningRouteName.ChooseDateTime}
        component={ChooseDateTime}
        options={{
          title: t('WORK_TIME_TITLE'),
        }}
      />
      <Stack.Screen
        name={IroningRouteName.ConfirmAndPayment}
        component={ConfirmBooking}
        options={{
          title: t('PT2_CONFIRM_HEADER_TITLE'),
        }}
      />
      <Stack.Screen
        name={IroningRouteName.PostTaskSuccess}
        component={PostTaskSuccess}
        options={{
          headerShown: false,
        }}
      />
    </Stack.Navigator>
  );
};

export default MainNavigator;
