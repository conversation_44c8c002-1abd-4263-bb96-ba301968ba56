import {
  <PERSON><PERSON>,
  DateT<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Endpoint<PERSON>eys,
  getPhoneNumber,
  handleError,
  IApiError,
  PaymentService,
  PostTaskHelpers,
  useApiMutation,
  useAppLoadingStore,
  useAppStore,
  usePostTaskAction,
  useUserStore,
} from '@btaskee/design-system';
import { isEmpty } from 'lodash-es';

import { usePatientCareStore } from '@stores';

import { useI18n } from './useI18n';
import { useTracking } from './useTracking';

// Payment data is now handled by PostTaskHelpers.formatDataToParams

export const usePostTask = () => {
  const { setPrice, setLoadingPrice, resetState, setIsBookedTask } =
    usePatientCareStore();
  const { showLoading, hideLoading } = useAppLoadingStore();
  const { trackingPostTaskSuccess } = useTracking();
  const { handlePostTaskError } = usePostTaskAction();
  const { user } = useUserStore();
  const { isoCode } = useAppStore();
  const { t } = useI18n();

  const { mutate: getPricePatientCare } = useApiMutation({
    key: EndpointKeys.getPricePatientCare,
    options: {
      onMutate: () => {
        setLoadingPrice(true);
        showLoading();
      },
      onSettled: () => {
        setLoadingPrice(false);
        hideLoading();
      },
    },
  });

  const { mutate: postTaskPatientCare } = useApiMutation({
    key: EndpointKeys.postTaskPatientCare,
    options: {
      onSuccess: async (data: any) => {
        if (data?.bookingId) {
          trackingPostTaskSuccess();

          resetState();
          setIsBookedTask(true);

          // Payment processing (includes navigation)
          await PaymentService.onPostTaskSuccess({
            bookingId: data.bookingId,
            isPrepayment: data.isPrepayment,
          });
        }
      },
      onError: (error: IApiError) => {
        handlePostTaskError(error);
      },
      onMutate: () => {
        showLoading();
      },
      onSettled() {
        hideLoading();
      },
    },
  });

  const { mutate: checkTaskSameTime } = useApiMutation({
    key: EndpointKeys.checkTaskSameTime,
    options: {
      onMutate: () => {
        showLoading();
      },
      onSettled() {
        hideLoading();
      },
    },
  });

  const { mutate: bookTaskForceTasker } =
    useApiMutation<EndpointKeys.bookTaskForceTasker>({
      key: EndpointKeys.bookTaskForceTasker,
      options: {
        onSuccess: async (data: any) => {
          if (data?.bookingId) {
            resetState();

            // Payment processing (includes navigation)
            await PaymentService.onPostTaskSuccess({
              bookingId: data.bookingId,
              isPrepayment: data.isPrepayment,
            });
          }
        },
        onError: (error: IApiError) => {
          handlePostTaskError(error);
        },
        onMutate: () => {
          showLoading();
        },
        onSettled() {
          hideLoading();
        },
      },
    });

  const { mutate: getPricingTaskDateOptionsAPI } = useApiMutation({
    key: EndpointKeys.getPricingTaskDateOptions,
    options: {
      onMutate: () => {
        setLoadingPrice(true);
        showLoading();
      },
      onSettled: () => {
        setLoadingPrice(false);
        hideLoading();
      },
    },
  });

  const buildPricingData = () => {
    const currentState = usePatientCareStore.getState();
    const {
      address,
      forceTasker,
      dateOptions,
      date,
      duration,
      isAutoChooseTasker,
      service,
      paymentMethod,
      promotion,
    } = currentState;

    if (!address || !date || !duration) {
      return null;
    }

    const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

    const task: any = {
      timezone,
      date: DateTimeHelpers.formatToString({ timezone, date }),
      autoChooseTasker: isAutoChooseTasker,
      taskPlace: {
        country: address?.country,
        city: address?.city,
        district: address?.district,
      },
      homeType: address?.homeType,
      duration,
      ...PostTaskHelpers.formatDataToParams({ paymentMethod, promotion }),
    };

    if (!isEmpty(forceTasker)) {
      task.forceTasker = forceTasker;
    }
    if (!isEmpty(dateOptions)) {
      task.dateOptions = dateOptions;
    }

    return { task, service: { _id: service?._id }, isoCode };
  };

  const getPrice = async (): Promise<void> => {
    const pricingData = buildPricingData();

    if (!pricingData) {
      setPrice(null);
      return;
    }

    if (!isEmpty(pricingData?.task?.dateOptions)) {
      return getPricingTaskDateOptionsAPI(pricingData, {
        onSuccess: (result: any) => {
          setPrice(result);
        },
        onError: (error: IApiError) => {
          handleError(error);
          setPrice(null);
        },
      });
    }

    setLoadingPrice(true);

    await getPricePatientCare(pricingData, {
      onSuccess: (result: any) => {
        setPrice(result);
      },
      onError: (error: IApiError) => {
        handleError(error);
        setPrice(null);
      },
    });

    setLoadingPrice(false);
  };

  const buildTaskData = () => {
    const currentState = usePatientCareStore.getState();
    const {
      address,
      forceTasker,
      dateOptions,
      date,
      duration,
      isAutoChooseTasker,
      service,
      paymentMethod,
      isApplyNoteForAllTask,
      note,
      isFavouriteTasker,
      isEnableSchedule,
      schedule,
      pet,
      relatedTask,
      promotion,
    } = currentState;

    const isAddressMaybeWrong = Boolean(address?.isAddressMaybeWrong);
    const isTet = service?.isTet || false;
    const city = address?.city;
    const timezone = DateTimeHelpers.getTimezoneByCity(city);

    const task: any = {
      address: address?.address,
      contactName: address?.contact || user?.name,
      lat: address?.lat,
      lng: address?.lng,
      phone: getPhoneNumber(
        address?.phoneNumber || user?.phone || '',
        address?.countryCode || user?.countryCode || '',
      ),
      countryCode: address?.countryCode || user?.countryCode,
      description: address?.description,
      askerId: user?._id,
      autoChooseTasker: isAutoChooseTasker,
      date: DateTimeHelpers.formatToString({ date, timezone }),
      timezone,
      deviceInfo: DeviceHelper.getDeviceInfo(),
      duration,
      homeType: address?.homeType,
      houseNumber: address?.description,
      isoCode,
      serviceId: service?._id,

      taskPlace: {
        country: address?.country,
        city,
        district: address?.district,
      },
      isSendToFavTaskers: isFavouriteTasker,
      updateTaskNoteToUser: isApplyNoteForAllTask,
      shortAddress: address?.shortAddress,
      // Add payment method and promotion using PostTaskHelpers
      ...PostTaskHelpers.formatDataToParams({ paymentMethod, promotion }),
    };

    // Add optional fields
    if (isAddressMaybeWrong) {
      task.taskPlace.isAddressMaybeWrong = true;
    }

    if (isTet) {
      task.isTetBooking = true;
    }

    if (note?.trim()) {
      task.taskNote = note.trim();
    }

    if (pet) {
      task.pet = pet;
    }

    if (isEnableSchedule && schedule && schedule.length > 0) {
      task.weekday = schedule;
    }

    if (!isEmpty(relatedTask?.relatedTaskId)) {
      task.source = {
        from: relatedTask?.service?.name,
        taskId: relatedTask?.relatedTaskId,
      };
    }

    // Asker chọn Tasker
    if (forceTasker?._id) {
      delete task.isSendToFavTaskers;
      task.autoChooseTasker = true;
      task.forceTasker = {
        taskerId: forceTasker?._id,
        isResent: Boolean(forceTasker?.isResent),
      };
    }

    // Asker chọn lịch của Tasker
    if (dateOptions) {
      task.dateOptions = dateOptions;
    }

    // Payment and promotion are handled by PostTaskHelpers.formatDataToParams

    return task;
  };

  const executeTaskPosting = async (): Promise<any> => {
    const taskData = buildTaskData();

    // Clean up dateOptions if needed
    if (isEmpty(taskData?.dateOptions)) {
      delete taskData.dateOptions;
    }

    // Check if this is a force tasker booking
    if (!isEmpty(taskData?.forceTasker)) {
      return bookTaskForceTasker(taskData);
    }

    // Normal patient care task posting
    return postTaskPatientCare(taskData);
  };

  const handleSameTimeConflict = async (): Promise<void> => {
    Alert.alert?.open({
      title: t('DIALOG_TITLE_INFORMATION'),
      message: t('TASK_SAME_TIME_MESSAGE'),
      actions: [
        { text: t('CLOSE'), style: 'cancel' },
        {
          text: t('OK'),
          onPress: async () => {
            setTimeout(async () => {
              executeTaskPosting();
            }, 300);
          },
        },
      ],
    });
  };

  const postTask = async (): Promise<any> => {
    const currentState = usePatientCareStore.getState();
    const { date, service, address } = currentState;
    const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

    // Check for conflicting tasks at the same time
    return checkTaskSameTime(
      {
        taskDate: DateTimeHelpers.formatToString({ date: date!, timezone }),
        serviceId: service?._id || '',
      },
      {
        onSuccess: async (result: any) => {
          if (result === true) {
            return handleSameTimeConflict();
          }
          await executeTaskPosting();
        },
      },
    );
  };

  return { getPrice, postTask };
};
