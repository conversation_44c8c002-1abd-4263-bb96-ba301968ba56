import React, { memo, useCallback, useState } from 'react';
import {
  ActivityIndicator,
  ListRenderItem,
  RefreshControl,
} from 'react-native';
import {
  BlockView,
  Colors,
  FlatList,
  FlatListProps,
  Spacing,
  useApiQuery,
  useAppStore,
  useUserStore,
} from '@btaskee/design-system';

import { styles } from './styles';

interface ListItemProps<T> extends FlatListProps<T> {
  readonly data: ReadonlyArray<T>;
  readonly refetch: ReturnType<typeof useApiQuery>['refetch'];
  readonly renderItem: ListRenderItem<T>;
  readonly testID: string;
  readonly onEndReached?: () => void;
  readonly onEndReachedThreshold?: number;
  readonly isLoadingMore?: boolean;
  readonly error?: Error | null;
}

const LoadingFooter = memo(({ isVisible }: { isVisible: boolean }) => {
  if (!isVisible) return null;

  return (
    <BlockView
      padding={{ vertical: Spacing.SPACE_16 }}
      center
      testID="loading-footer"
    >
      <ActivityIndicator
        size="small"
        color={Colors.orange500}
      />
    </BlockView>
  );
});

LoadingFooter.displayName = 'LoadingFooter';

const ItemSeparator = memo(() => <BlockView height={Spacing.SPACE_16} />);

ItemSeparator.displayName = 'ItemSeparator';

function ListItemComponent<T>({
  data,
  refetch,
  renderItem,
  testID,
  ListEmptyComponent,
  onEndReached,
  onEndReachedThreshold = 0.3,
  isLoadingMore = false,
  error,
  ItemSeparatorComponent,
  ...rest
}: ListItemProps<T>) {
  const { user } = useUserStore();
  const { isoCode } = useAppStore();
  const [isRefreshing, setIsRefreshing] = useState(false);

  const onRefresh = useCallback(async () => {
    if (!user?._id || !isoCode) {
      return;
    }

    setIsRefreshing(true);
    try {
      await refetch();
    } catch (err) {
      console.error('Error refreshing task list:', err);
    } finally {
      setIsRefreshing(false);
    }
  }, [refetch, user?._id, isoCode]);

  const keyExtractor = useCallback(
    (item: T, index: number): string => item?._id || index?.toString(),
    [],
  );

  const handleEndReached = useCallback(() => {
    if (onEndReached && !isLoadingMore && !error) {
      onEndReached();
    }
  }, [onEndReached, isLoadingMore, error]);

  const refreshControl = (
    <RefreshControl
      refreshing={isRefreshing}
      onRefresh={onRefresh}
      colors={[Colors.orange50, Colors.orange500]} // Android
      tintColor={Colors.orange500} // iOS
      progressBackgroundColor={Colors.neutralSecondary} // Android
      progressViewOffset={Spacing.SPACE_04}
    />
  );

  return (
    <FlatList
      testID={testID}
      data={data}
      renderItem={renderItem}
      keyExtractor={keyExtractor}
      ItemSeparatorComponent={ItemSeparatorComponent ?? ItemSeparator}
      contentContainerStyle={styles.contentContainer}
      showsVerticalScrollIndicator={false}
      refreshControl={refreshControl}
      ListEmptyComponent={ListEmptyComponent}
      ListFooterComponent={<LoadingFooter isVisible={isLoadingMore} />}
      onEndReached={handleEndReached}
      onEndReachedThreshold={onEndReachedThreshold}
      removeClippedSubviews={true}
      maxToRenderPerBatch={10}
      windowSize={10}
      initialNumToRender={10}
      updateCellsBatchingPeriod={100}
      {...rest}
    />
  );
}

// Export as generic component while maintaining type safety
export const ListItem = memo(ListItemComponent) as typeof ListItemComponent;
