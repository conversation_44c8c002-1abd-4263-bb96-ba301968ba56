import React from 'react';
import { SystemMessageProps } from 'react-native-gifted-chat';
import { Maybe } from '@btaskee/design-system';
import {
  IInfoTaskOfChat,
  IMessage,
  ITaskerInfo,
  TypeMessageFromSystem,
} from '@types';

import { BookTaskFavMessage } from './BookTaskFav';
import { Guarantee } from './Guarantee';
import { MessageWithAction } from './MessageWithAction';
import { TaskReminder } from './TaskReminder';
import { WelcomeFavTasker } from './WelcomeFavTasker';

/**
 * Interface for system message component props
 * Purpose: Extends SystemMessageProps with additional chat-specific properties
 */
export interface IChatMessageSystem extends SystemMessageProps<IMessage> {
  task?: Maybe<IInfoTaskOfChat>;
  chatId?: Maybe<string>;
  isHistory?: Maybe<boolean>;
  taskerInfo?: Maybe<ITaskerInfo>;
}

/**
 * Mapping of system message types to their respective components
 * Purpose: Provides a centralized mapping for rendering different system message types
 */
const listCustomView = {
  [TypeMessageFromSystem.alertTaskStart]: TaskReminder,
  [TypeMessageFromSystem.alertTaskEnd]: TaskReminder,
  [TypeMessageFromSystem.alertTaskDone]: Guarantee,
  [TypeMessageFromSystem.bookWithFav]: BookTaskFavMessage,
  [TypeMessageFromSystem.welcomeFavTasker]: WelcomeFavTasker,
};

/**
 * MessageBySystem component for rendering system-generated messages
 * Purpose: Main component that determines which system message component to render
 * based on the message type, handling various system notifications and actions
 * @param props - System message props including message data and context
 * @returns {JSX.Element | null} React component for the specific system message type or null
 */
export const MessageBySystem = (
  props: IChatMessageSystem,
): React.JSX.Element | null => {
  const { currentMessage } = props;

  const messageBySystem = currentMessage?.messageBySystem;

  // Handle specific system message types
  if (messageBySystem?.key) {
    const CustomViewScreen =
      listCustomView[messageBySystem.key as TypeMessageFromSystem];
    if (CustomViewScreen) {
      return <CustomViewScreen {...props} />;
    }
  }

  // Handle generic system messages with actions
  if (messageBySystem) {
    return <MessageWithAction {...props} />;
  }

  // Return null if no system message is found
  return null;
};
