import React from 'react';
import {
  BlockView,
  Colors,
  ConditionView,
  CText,
  FastImage,
  getTextWithLocale,
  Spacing,
} from '@btaskee/design-system';

import { TimeGiftChat } from '../../TimeGiftChat';
import { RenderAction } from '../components/ButtonAction';
import type { IChatMessageSystem } from '../index';
import { styles } from './styles';

/**
 * WelcomeFavTasker component for displaying welcome messages for favorite taskers
 * Purpose: Renders welcome system messages when users start chatting with favorite taskers
 * @param props - System message props containing welcome message data
 * @returns {JSX.Element} React component displaying welcome message
 */
export const WelcomeFavTasker = (
  props: IChatMessageSystem,
): React.JSX.Element => {
  const { currentMessage } = props;

  const messageBySystem = currentMessage?.messageBySystem;

  return (
    <BlockView>
      <BlockView
        flex
        style={styles.container}
      >
        <BlockView
          center
          margin={{ bottom: Spacing.SPACE_08 }}
        >
          <ConditionView
            condition={Boolean(messageBySystem?.image)}
            viewTrue={
              <FastImage
                source={{ uri: messageBySystem?.image }}
                style={styles.image}
              />
            }
          />
          {messageBySystem?.title && (
            <CText
              bold
              color={Colors.orange500}
              style={styles.title}
            >
              {getTextWithLocale(messageBySystem.title)}
            </CText>
          )}

          {messageBySystem?.text && (
            <CText style={styles.text}>
              {getTextWithLocale(messageBySystem.text)}
            </CText>
          )}
        </BlockView>
        <TimeGiftChat
          {...props}
          containerStyle={{
            right: { alignSelf: 'flex-end' },
          }}
        />
      </BlockView>
      <ConditionView
        condition={Boolean(messageBySystem?.actions)}
        viewTrue={
          <BlockView>
            <RenderAction
              actions={messageBySystem?.actions}
              taskerInfo={props?.taskerInfo}
              chatId={props?.chatId}
              taskId={props?.task?._id}
            />
          </BlockView>
        }
      />
    </BlockView>
  );
};
