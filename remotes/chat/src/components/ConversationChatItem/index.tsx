import React, { useMemo } from 'react';
import {
  Avatar,
  BlockView,
  BorderRadius,
  Colors,
  ConditionView,
  CText,
  DateTimeHelpers,
  FontSizes,
  HitSlop,
  IconAssets,
  IconImage,
  IConversationItemProps,
  Spacing,
  TouchableOpacity,
  TypeFormatDate,
  useSettingsStore,
  useUserStore,
} from '@btaskee/design-system';

import { styles } from './styles';

/**
 * ConversationChatItem component for displaying individual chat conversations
 * Purpose: Renders a single conversation item in the chat list with user avatar,
 * last message, timestamp, and interaction options
 * @param item - Conversation data containing user info, messages, and metadata
 * @param index - Position index in the list for animation and testing
 * @param onPress - Callback function when conversation item is pressed
 * @param onHandleOption - Optional callback for handling options menu press
 * @returns {JSX.Element} React component displaying conversation item
 */
export const ConversationChatItem = ({
  item,
  index,
  onPress,
  onHandleOption,
}: IConversationItemProps): React.JSX.Element => {
  const { settings } = useSettingsStore();
  const { user } = useUserStore();

  /**
   * Extracts tasker information from conversation members
   * Purpose: Finds the other participant in the conversation (not the current user)
   * @returns User object of the tasker/other participant
   */
  const taskerInfo = useMemo(() => {
    const listMember = item?.members?.filter(
      (member) => member._id !== user?._id,
    );
    // Note: Asker doesn't support group chat. If updated later, handle multiple members
    return listMember?.[0];
  }, [item?.members, user?._id]);

  return (
    <BlockView>
      {/* Options button positioned absolutely */}
      {onHandleOption && (
        <TouchableOpacity
          testID="btnOptionsChat"
          activeOpacity={0.7}
          hitSlop={HitSlop.LARGE}
          style={styles.btnOptions}
          onPress={onHandleOption}
        >
          <IconImage
            source={IconAssets.icOptions}
            size={16}
            color={Colors.neutral900}
          />
        </TouchableOpacity>
      )}

      {/* Main conversation item container */}
      <TouchableOpacity
        testID={`boxChat_${index}`}
        activeOpacity={0.7}
        style={styles.containerItem}
        onPress={onPress}
      >
        <BlockView
          row
          horizontal
        >
          <BlockView
            flex
            row
            testID={`btnNotificationItemChat${index}`}
            horizontal
          >
            {/* User avatar */}
            <Avatar
              index={index}
              avatar={taskerInfo?.avatar}
              size={48}
              isPremiumTasker={taskerInfo?.isPremiumTasker}
              avatarFrameSettings={settings?.askerSetting?.avatarFrame}
              containerStyle={
                settings?.askerSetting?.avatarFrame ||
                taskerInfo?.isPremiumTasker
                  ? styles.wrapAvtSpecial
                  : styles.wrapAvtNormal
              }
            />

            {/* Message content */}
            <BlockView
              flex
              style={styles.boxContent}
            >
              {/* Tasker name */}
              <CText
                testID={`taskerName_${index}`}
                color={Colors.neutral900}
                size={FontSizes.SIZE_14}
                style={styles.taskerName}
                bold
                numberOfLines={1}
              >
                {taskerInfo?.name}
              </CText>

              {/* Last message and timestamp row */}
              <BlockView
                row
                horizontal
              >
                {/* Last message text */}
                <ConditionView
                  condition={Boolean(item?.lastChatMessage?.message)}
                  viewTrue={
                    <CText
                      flex
                      numberOfLines={1}
                      style={styles.lastMessage}
                      color={Colors.neutral600}
                      size={FontSizes.SIZE_12}
                    >
                      {item?.lastChatMessage?.message}
                    </CText>
                  }
                />

                {/* Timestamp */}
                <CText
                  flex
                  right
                  color={Colors.neutral500}
                  size={FontSizes.SIZE_12}
                >
                  {DateTimeHelpers.formatToString({
                    date: item?.lastChatMessage?.createdAt || item?.createdAt,
                    typeFormat: TypeFormatDate.HourMinuteDate,
                  })}
                </CText>
              </BlockView>
            </BlockView>
          </BlockView>
        </BlockView>
      </TouchableOpacity>

      {/* Unread badge indicator */}
      <ConditionView
        condition={!item?.isRead}
        viewTrue={
          <BlockView
            testID={`badge_${index}`}
            center
            absolute
            positionTop={0}
            positionBottom={0}
            positionLeft={0}
          >
            <BlockView
              width={Spacing.SPACE_08}
              height={Spacing.SPACE_08}
              radius={BorderRadius.RADIUS_FULL}
              backgroundColor={Colors.red500}
            />
          </BlockView>
        }
      />
    </BlockView>
  );
};
