import { IMessage as IGiftedChatMessage } from 'react-native-gifted-chat';
import {
  ICurrency,
  IDate,
  IObjectText,
  ITaskPlace,
  SERVICES,
} from '@btaskee/design-system';

/**
 * Enum for message origin identification
 * Purpose: Identifies who sent a message in the chat conversation
 */
export enum MessageFrom {
  Asker = 'ASKER',
  Tasker = 'TASKER',
  System = 'SYSTEM',
}

/**
 * Enum for message status tracking
 * Purpose: Tracks the delivery and read status of messages
 */
export enum MessageStatus {
  sending = 'sending',
  sent = 'sent',
  received = 'received',
  pending = 'pending',
}

/**
 * Enum for custom message types
 * Purpose: Defines different types of custom messages that can be rendered
 */
export enum TypeCustomMessage {
  Map = 'MAP',
  UpdateDateTime = 'UPDATE_DATE_TIME',
  IncreaseDuration = 'INCREASE_DURATION',
  UpdateDetail = 'UPDATE_DETAIL',
  TextAction = 'TEXT_ACTION',
  TaskIsCancelled = 'TASK_IS_CANCELLED',
}

/**
 * Enum for system message types
 * Purpose: Defines different types of system-generated messages
 */
export enum TypeMessageFromSystem {
  alertTaskStart = 'ALERT_TASK_START',
  alertTaskEnd = 'ALERT_TASK_DONE',
  alertTaskDone = 'GUARANTEED_TASK',
  bookWithFav = 'BOOK_WITH_FAV',
  welcomeFavTasker = 'WELCOME',
}

/**
 * Enum for action message types
 * Purpose: Defines the visual style of action buttons in messages
 */
export enum TypeActionMessage {
  Primary = 'PRIMARY',
  Secondary = 'SECONDARY',
}

/**
 * Enum for request data status
 * Purpose: Tracks the status of requests made through chat messages
 */
export enum StatusRequestData {
  Waiting = 'WAITING',
  Approved = 'APPROVED',
  Rejected = 'REJECTED',
  Expired = 'EXPIRED',
}

/**
 * Enum for task request data status
 * Purpose: Tracks the status of task requests in chat messages
 */
export enum IStatusTaskRequestData {
  Waiting = 'WAITING',
  SentNoti = 'SENT_NOTI',
  Approved = 'APPROVED',
  Rejected = 'REJECTED',
  Expired = 'EXPIRED',
}

/**
 * Enum for phone call status
 * Purpose: Tracks the status of phone calls in chat messages
 */
export enum StatusPhoneCall {
  Missed = 'MISSED',
  Success = 'SUCCESS',
}

/**
 * Interface for phone call data in messages
 * Purpose: Contains information about phone calls made through the chat
 */
export type IPhoneCallDataMessage = {
  status?: StatusPhoneCall;
  duration?: number;
  createdAt?: IDate;
};

/**
 * Interface for action buttons in messages
 * Purpose: Defines clickable actions that can be performed from chat messages
 */
export type IAction = {
  key?: string;
  title?: IObjectText;
  text?: IObjectText;
  type?: TypeActionMessage | string;
  data?: any;
  isDisabled?: boolean;
};

/**
 * Interface for tasker information in messages
 * Purpose: Contains tasker-specific data for system messages
 */
export type ITaskerInfo = {
  taskerId?: string;
  taskerName?: string;
  taskerAvatar?: string;
  extraMoney?: number;
  currency?: string;
  language?: string;
};

/**
 * Interface for cost detail items in update requests
 * Purpose: Defines the structure of cost information for update detail messages
 */
export interface ICostDetailItem {
  totalCost?: number;
  finalCost: number;
  currency?: ICurrency;
}

/**
 * Interface for request data in custom messages
 * Purpose: Contains data for requests that require approval/rejection
 */
export type IRequestDataMessage = {
  status?: StatusRequestData;
  requestId?: string;
  data?: any;
  createdAt?: IDate;
  expiredAt?: IDate;
  // Date/time update specific fields
  date?: {
    old?: IDate;
    new?: IDate;
  };
  duration?: {
    old?: number;
    new?: number;
  };
  extraMoney?: number;
  costDetail?: {
    old?: ICostDetailItem;
    new?: ICostDetailItem;
  };
  // Update detail specific fields
  updateDetailHTML?: string;
  acceptedTasker?: any;
};

/**
 * Interface for task request data
 * Purpose: Contains task-specific request information
 */
export type ITaskRequestData = {
  taskId?: string;
  serviceName?: SERVICES;
  requestType?: string;
  data?: any;
  status?: IStatusTaskRequestData;
  expiredAt?: IDate;
  taskInfo?: {
    taskId?: string;
    duration?: number;
    serviceText?: IObjectText;
    district?: string;
    dateOptions?: IDateOptionItem[];
  };
};

/**
 * Interface for text action data
 * Purpose: Contains data for text-based action messages
 */
export type ITextActionData = {
  text: string;
  actions: IAction[];
};

/**
 * Interface for date option items
 * Purpose: Represents selectable date/time options in messages
 */
export type IDateOptionItem = {
  _id?: string;
  date?: IDate;
  time?: string;
  isSelected?: boolean;
  isAvailable?: boolean;
};

/**
 * Interface for system messages
 * Purpose: Contains data for messages generated by the system
 */
export type IMessageBySystem = {
  key: string;
  title?: IObjectText;
  text?: IObjectText;
  actions?: Array<IAction>;
  taskerTotalExtraMoney?: ITaskerInfo[];
  image?: string;
  textAction?: {
    key?: string;
  };
  dateOptions?: IDateOptionItem[];
  taskInfo?: any;
};

/**
 * Interface for task information in chat context
 * Purpose: Contains task details relevant to chat conversations
 */
export type IInfoTaskOfChat = {
  _id?: string;
  serviceName?: SERVICES;
  serviceText?: IObjectText;
  serviceIcon?: string;
  taskPlace?: ITaskPlace;
  address?: string;
  date?: IDate;
  duration?: number;
  status?: string;
  askerName?: string;
  taskerName?: string;
};

/**
 * Interface for confirmed task information
 * Purpose: Represents task details for confirmed tasks in chat context
 */
export type ITaskConfirmed = {
  _id?: string;
  date?: IDate;
  duration?: number;
  serviceText?: IObjectText;
  taskPlace?: {
    address?: string;
    district?: string;
    city?: string;
  };
};

/**
 * Extended message interface for chat functionality
 * Purpose: Extends GiftedChat's IMessage with custom properties for bTaskee chat features
 */
export type IMessage = {
  location?: {
    latitude: number;
    longitude: number;
  };
  text: string;
  from?: MessageFrom;
  translatedText?: string;
  preparedMessage?: string;
  requestData?: IRequestDataMessage;
  type?: TypeCustomMessage;
  messageBySystem?: IMessageBySystem;
  message?: string;
  isHistory?: boolean;
  chatId?: string;
  isConverted?: boolean;
  userId?: IGiftedChatMessage['user']['_id'];
  videoSummary?: {
    _id?: string;
    duration?: number;
    link?: string;
    thumbnail?: string;
  };
  callData?: IPhoneCallDataMessage;
  taskRequestData?: ITaskRequestData;
  textActionData?: ITextActionData;
  status?: MessageStatus;
} & IGiftedChatMessage;

/**
 * Interface for message parameters when sending
 * Purpose: Defines the structure for sending messages to the server
 */
export type IMessageParam = {
  chatId?: string;
  messageTo?: string[];
  createdAt?: IMessage['createdAt'];
  version?: 'v2';
  data: {
    from: MessageFrom;
    to?: string;
    message?: string;
    userId?: string;
    preparedMessage?: string;
    image?: string;
    location?: {
      latitude: number;
      longitude: number;
    };
    videoSummary?: {
      duration?: number;
      link?: string;
      thumbnail?: string;
    };
  };
};

/**
 * Interface for conversation properties
 * Purpose: Defines properties for chat conversations
 */
export type GetConversationProps = {
  chatId?: string;
  taskId?: string;
};

/**
 * Enum for chat message actions
 * Purpose: Defines Redux-style actions for chat message updates
 */
export enum ChatMessageAction {
  UPDATE_CHAT_MESSAGE = 'UPDATE_CHAT_MESSAGE',
}
