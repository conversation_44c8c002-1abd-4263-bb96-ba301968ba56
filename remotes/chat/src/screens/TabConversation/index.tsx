import React, { memo, useCallback } from 'react';
import { ListRenderItemInfo } from 'react-native';
import {
  Alert,
  BlockView,
  ChatRouteName,
  Colors,
  ConditionView,
  IConversationChatItem,
  SizedBox,
  useAppStore,
  useUserStore,
} from '@btaskee/design-system';
import { useFocusEffect } from '@react-navigation/native';
import { isEmpty } from 'lodash-es';

import {
  ConversationActions,
  ConversationChatItem,
  ConversationTaskItem,
  ListChatEmpty,
  ListItem,
  SkeletonList,
} from '@components';
import { useAppNavigation, useConversationsPaginated, useI18n } from '@hooks';
import { useConversationChatStore } from '@stores';

const ItemSeparator = memo(() => (
  <SizedBox
    height={1}
    color={Colors.neutral100}
  />
));

/**
 * TabConversation component for displaying chat conversation list
 * Purpose: Main screen component that shows a list of user's chat conversations
 * with both regular chat and task-related conversations, including pagination,
 * pull-to-refresh, and empty states
 * @returns {JSX.Element} React component displaying conversation list
 */
export function TabConversation(): React.JSX.Element {
  const { user } = useUserStore();
  const { isoCode } = useAppStore();
  const navigation = useAppNavigation();
  const { t } = useI18n();
  const { setChatId, setTask } = useConversationChatStore();

  // Use paginated conversation hook following the same pattern as useTaskHistoryPaginated
  // Maintains the same architectural pattern as task-management module
  const { data, isLoading, isLoadingMore, error, loadMore, refetch } =
    useConversationsPaginated();

  // ✅ Focus effect following established pattern from TabUpcoming, TabMonthly, TabSchedule
  // Ensures fresh data when tab comes into focus with single API call per focus event
  useFocusEffect(
    useCallback(() => {
      if (user?._id && isoCode) {
        refetch();
      }
    }, [refetch, user?._id, isoCode]),
  );

  /**
   * Wrapper function for refresh to match ListItem's expected refetch signature
   * Purpose: Adapts the paginated refetch function to work with ListItem component
   */
  const handleRefresh = useCallback(() => {
    refetch();
  }, [refetch]);

  /**
   * Handles navigation to chat screen
   * Purpose: Navigates to the chat message screen with appropriate parameters
   * @param options - Navigation options containing task or chat information
   */
  const onNavigateToChat = useCallback(
    (options: {
      task?: { _id?: string };
      chatId?: string;
      isTaskerFavorite?: boolean;
    }) => {
      if (options?.chatId) {
        setChatId(options.chatId);
      }
      if (options?.task?._id) {
        setTask(options.task);
      }
      // Navigate to chat message screen
      navigation.navigate(ChatRouteName.ChatMessage, {
        task: options.task,
        chatId: options.chatId,
      });
    },
    [navigation, setChatId, setTask],
  );

  /**
   * Handles conversation options menu
   * Purpose: Shows options menu for conversation actions (archive, delete, etc.)
   * Migrated from unicorn-btaskee-version-v3 Chat component with modern design system
   * @param chatId - ID of the conversation to show options for
   */
  const onHandleOption = useCallback(
    (chatId?: string) => {
      if (!chatId) return;

      Alert.alert.open({
        title: t('CONVERSATION.OPTION'),
        message: (
          <ConversationActions
            chatId={chatId}
            refetch={refetch}
          />
        ),
        backgroundContainer: Colors.neutral50,
      });
    },
    [t, refetch],
  );

  /**
   * Renders individual conversation items
   * Purpose: Determines whether to render a task conversation or regular chat conversation
   * @param item - Conversation data
   * @param index - Item index in the list
   * @returns Appropriate conversation component
   */
  const renderItem = useCallback(
    ({ item, index }: ListRenderItemInfo<IConversationChatItem>) => {
      if (item?.taskId) {
        return (
          <ConversationTaskItem
            item={item}
            index={index}
            onPress={() => onNavigateToChat({ task: { _id: item?.taskId } })}
          />
        );
      }
      return (
        <ConversationChatItem
          item={item}
          index={index}
          onPress={() =>
            onNavigateToChat({ chatId: item?.chatId, isTaskerFavorite: true })
          }
          onHandleOption={() => onHandleOption(item?.chatId)}
        />
      );
    },
    [onNavigateToChat, onHandleOption],
  );

  return (
    <BlockView
      testID="container-conversations"
      flex
      backgroundColor={Colors.neutralBackground}
    >
      {isLoading && isEmpty(data) ? (
        <SkeletonList count={15} />
      ) : (
        <ListItem
          testID={'scroll-conversations'}
          data={data} // Type assertion for compatibility with ListItem
          renderItem={renderItem}
          refetch={handleRefresh} // Use wrapper function for pull-to-refresh
          ListEmptyComponent={<ListChatEmpty />}
          onEndReached={loadMore} // Enable pagination
          onEndReachedThreshold={0.3} // Trigger load more when 30% from bottom
          isLoadingMore={isLoadingMore} // Show loading footer
          error={error as Error | null} // Type assertion for compatibility
          ItemSeparatorComponent={ItemSeparator}
          ListFooterComponent={
            <ConditionView
              condition={!isEmpty(data) && data?.length < 5}
              viewTrue={
                <>
                  <ItemSeparator />
                  <ListChatEmpty />
                </>
              }
            />
          }
        />
      )}
    </BlockView>
  );
}
