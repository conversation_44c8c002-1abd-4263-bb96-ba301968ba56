// import locale for dayjs
import 'dayjs/locale/es';
import 'dayjs/locale/id';
import 'dayjs/locale/ko';
import 'dayjs/locale/ms';
import 'dayjs/locale/th';
import 'dayjs/locale/vi';

import React, { memo, useCallback, useEffect, useMemo, useState } from 'react';
import {
  Bubble,
  BubbleProps,
  DayProps,
  GiftedChat,
  Message,
  MessageProps,
  MessageText,
  MessageTextProps,
  SystemMessageProps,
  TimeProps,
} from 'react-native-gifted-chat';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import {
  BlockView,
  ChatRouteName,
  ChatStackScreenProps,
  Colors,
  CText,
  DateTimeHelpers,
  EndpointKeys,
  FastImage,
  FontFamily,
  FontSizes,
  handleError,
  IconAssets,
  IconImage,
  ITaskDetail,
  Maybe,
  MessageFrom,
  Spacing,
  TypeFormatDate,
  useApiMutation,
  useAppStore,
  useUserStore,
} from '@btaskee/design-system';
import { type IMessage, ITaskerInfo, TypeCustomMessage } from '@types';
import { isEmpty } from 'lodash-es';

import {
  ChatHistoryHeader,
  ChatMessageSkeleton,
  MessageBySystem,
  MessageCustomView,
  MessageImage,
  MessageVideo,
  TaskNotFound,
  TimeGiftChat,
  TranslatedMessage,
} from '@components';
import { useChatMessage } from '@hooks';

import { styles } from './styles.ts';

type ChatMessageProps = ChatStackScreenProps<ChatRouteName.ChatMessage>;

/**
 * ChatMessage component for displaying chat conversation
 * Purpose: Main chat screen that handles real-time messaging, media sharing,
 * custom message types, and system messages using GiftedChat integration
 * @returns {JSX.Element} React component displaying chat conversation
 */
const ChatHistoryMessageComponent = ({
  route,
}: ChatMessageProps): React.JSX.Element => {
  const { user } = useUserStore();
  const { locale } = useAppStore();
  const { convertMessagesToGiftChat } = useChatMessage();

  const insets = useSafeAreaInsets();

  const [isLoading, setIsLoading] = useState(true);
  const [messages, setMessages] = useState<IMessage[]>([]);
  const [chatId, setChatId] = useState<Maybe<string>>(null);
  const [task, setTask] = useState<Maybe<ITaskDetail>>(null);
  const [taskerInfo, setTaskerInfo] = useState<Maybe<ITaskerInfo>>(null);

  const { mutate: getListChatHistoryMassage } = useApiMutation({
    key: EndpointKeys.getListChatHistoryMassage,
    options: {
      onMutate: () => {
        setIsLoading(true);
      },
      onSettled: () => {
        setIsLoading(false);
      },
      onError: (error) => {
        handleError(error);
      },
      onSuccess: (data) => {
        setChatId(data?._id);
        setTask(data?.task);
        setTaskerInfo(data?.taskerInfo);
        if (data?.messages) {
          const newMessages = data?.messages?.map((mess) => {
            return convertMessagesToGiftChat({
              conversation: data,
              newMessage: mess,
            });
          });

          setMessages(newMessages);
        }
      },
    },
  });

  // Memoize user data to prevent re-renders when user object reference changes
  const memoizedUser = useMemo(
    () => ({
      _id: user?._id || '',
      avatar: user?.avatar,
      name: user?.name || '',
    }),
    [user?._id, user?.avatar, user?.name],
  );

  // Memoize route params to prevent unnecessary effect triggers
  const routeParams = useMemo(() => route.params, [route.params]);

  useEffect(() => {
    getListChatHistoryMassage({
      taskId: routeParams?.task?._id,
      chatId: routeParams?.chatId,
      version: 'v2',
    });
  }, [
    getListChatHistoryMassage,
    routeParams.chatId,
    routeParams.isTaskerFavorite,
    routeParams.memberIds,
    routeParams.task,
    routeParams.taskerInfo,
  ]);

  /**
   * Renders the message input composer
   * Purpose: Custom input field for typing messages with proper styling
   */
  const renderComposer = useCallback(() => {
    return null;
  }, []);

  /**
   * Renders the send button
   * Purpose: Custom send button with icon
   */
  const renderSend = useCallback(() => {
    return null;
  }, []);

  /**
   * Renders message bubbles
   * Purpose: Custom styling for message bubbles
   */
  const renderBubble = useCallback((props: BubbleProps<IMessage>) => {
    return (
      <Bubble
        {...props}
        wrapperStyle={styles.bubble}
        textStyle={styles.bubbleText}
      />
    );
  }, []);

  // Memoize link styles to prevent recreation
  const linkStyles = useMemo(
    () => ({
      left: {
        color: Colors.neutral800,
        fontSize: FontSizes.SIZE_14,
        fontFamily: FontFamily.semiBold,
      },
      right: {
        color: Colors.neutral800,
        fontSize: FontSizes.SIZE_14,
        fontFamily: FontFamily.semiBold,
      },
    }),
    [],
  );

  /**
   * Renders message text
   * Purpose: Custom text rendering for messages with link styling
   */
  const renderMessageText = useCallback(
    (mess: MessageTextProps<IMessage>) => {
      const { currentMessage } = mess;
      return (
        <BlockView>
          <MessageText
            {...mess}
            linkStyle={linkStyles}
          />
          {currentMessage?.translatedText &&
          currentMessage?.from === MessageFrom.Tasker ? (
            <TranslatedMessage translatedText={currentMessage.translatedText} />
          ) : null}
        </BlockView>
      );
    },
    [linkStyles],
  );

  /**
   * Renders video messages
   * Purpose: Custom video message rendering with play controls
   */
  const renderMessageVideo = useCallback(
    ({ currentMessage }: { currentMessage: IMessage }) => {
      return <MessageVideo data={currentMessage} />;
    },
    [],
  );

  /**
   * Renders image messages
   * Purpose: Custom image message rendering with full screen capability
   */
  const renderMessageImage = useCallback(
    ({ currentMessage }: { currentMessage: IMessage }) => {
      return <MessageImage data={currentMessage} />;
    },
    [],
  );

  /**
   * Renders day separator
   * Purpose: Custom day separator styling
   */
  const renderDay = useCallback(
    (props: DayProps & { previousMessage: IMessage }) => {
      const { createdAt, previousMessage } = props;
      if (
        createdAt == null ||
        DateTimeHelpers.checkIsSame({
          firstDate: createdAt,
          secondDate: previousMessage?.createdAt,
          unit: 'day',
        })
      ) {
        return null;
      }
      return (
        <CText
          center
          size={FontSizes.SIZE_12}
          color={Colors.neutral400}
          margin={{ vertical: Spacing.SPACE_08 }}
        >
          {DateTimeHelpers.formatToString({
            date: createdAt,
            typeFormat: TypeFormatDate.DateMonthYearFull,
          })}
        </CText>
      );
      // return null;
    },
    [],
  );

  /**
   * Renders user avatar
   * Purpose: Custom avatar rendering with FastImage for optimal performance
   */
  const renderAvatar = useCallback((props: any) => {
    if (!props.currentMessage?.user?.avatar) return null;

    return (
      <BlockView style={styles.avatarContainer}>
        <FastImage
          source={{ uri: props.currentMessage.user.avatar }}
          style={styles.avatar}
          resizeMode="cover"
        />
      </BlockView>
    );
  }, []);

  /**
   * Renders custom message view
   * Purpose: Handles custom message types like location, phone calls, etc.
   */
  const renderCustomView = useCallback(
    (props: BubbleProps<IMessage>) => {
      return (
        <MessageCustomView
          {...props}
          chatId={chatId}
          taskId={task?._id}
          serviceName={task?.serviceName}
          task={task}
          taskerInfo={taskerInfo}
        />
      );
    },
    [chatId, task, taskerInfo],
  );

  /**
   * Renders system messages
   * Purpose: Handles system-generated messages
   */
  const renderSystemMessage = useCallback(
    (props: SystemMessageProps<IMessage>) => {
      return (
        <MessageBySystem
          {...props}
          chatId={chatId}
        />
      );
    },
    [chatId],
  );

  /**
   * Renders scroll to bottom button
   * Purpose: Custom scroll to bottom button
   */
  const renderScrollToBottomComponent = useCallback(
    () => (
      <BlockView style={styles.scrollToBottomButton}>
        <IconImage
          source={IconAssets.icArrowDown}
          color={Colors.orange500}
        />
      </BlockView>
    ),
    [],
  );

  /**
   * Renders time for messages
   * Purpose: Custom time rendering with proper styling
   */
  const renderTime = (
    props: TimeProps<IMessage> & { nextMessage: IMessage },
  ) => {
    const { currentMessage, nextMessage } = props;
    // Chi hien thi voi tin nhan cuoi cung cung nguoi gui va khac ngay
    let isSameSender = true;
    if (
      nextMessage?.user?._id === currentMessage?.user?._id &&
      DateTimeHelpers.checkIsSame({
        firstDate: nextMessage?.createdAt,
        secondDate: currentMessage?.createdAt,
        unit: 'day',
      })
    ) {
      isSameSender = false;
    }

    // Chỉ render với thời gian ở tin nhắn cuối cùng mà thôi (Ngoại trừ các loại tin nhắn từ Hệ thống và tin nhắn đã đc custom)
    if (
      isEmpty(props?.currentMessage?.messageBySystem) &&
      !props?.currentMessage?.image &&
      !props?.currentMessage?.video &&
      ![
        TypeCustomMessage.IncreaseDuration,
        TypeCustomMessage.UpdateDetail,
        TypeCustomMessage.UpdateDateTime,
        TypeCustomMessage.TextAction,
      ].includes(props?.currentMessage?.type as TypeCustomMessage) &&
      isSameSender
    ) {
      return (
        <TimeGiftChat
          {...props}
          timeTextStyle={{
            left: {
              marginBottom: Spacing.SPACE_04,
              fontSize: FontSizes.SIZE_10,
              color: Colors.neutral400,
              fontFamily: FontFamily.medium,
            },
            right: {
              marginBottom: Spacing.SPACE_04,
              fontSize: FontSizes.SIZE_10,
              color: Colors.neutral400,
              fontFamily: FontFamily.medium,
            },
          }}
        />
      );
    }
    return null;
  };

  /**
   * Enhanced message rendering with translation support
   * Purpose: Wraps default message with translation functionality
   */
  const renderMessage = useCallback((mess: MessageProps<IMessage>) => {
    const { currentMessage } = mess;
    return (
      <BlockView testID={currentMessage?.preparedMessage}>
        <Message {...mess} />
      </BlockView>
    );
  }, []);

  /**
   * Renders input toolbar with additional features
   * Purpose: Custom input toolbar with image, location, and suggestion buttons
   */
  const renderInputToolbar = useCallback(() => {
    return null;
  }, []);

  // Show loading state
  if (isLoading && isEmpty(messages)) {
    return <ChatMessageSkeleton />;
  }

  // Show error state if no chat ID
  if (!chatId && !task) {
    return <TaskNotFound />;
  }

  return (
    <BlockView
      inset={['bottom']}
      style={styles.container}
    >
      {/* Restored Original Header */}
      <ChatHistoryHeader
        taskerInfo={taskerInfo}
        task={task}
      />

      {/* Chat Messages */}
      <BlockView flex>
        <GiftedChat
          keyboardShouldPersistTaps="never"
          isScrollToBottomEnabled
          scrollToBottomComponent={renderScrollToBottomComponent}
          renderSend={renderSend}
          renderComposer={renderComposer}
          renderInputToolbar={renderInputToolbar}
          messages={messages || []}
          renderBubble={renderBubble}
          renderMessage={renderMessage}
          renderMessageVideo={renderMessageVideo}
          renderMessageImage={renderMessageImage}
          renderMessageText={renderMessageText}
          renderDay={renderDay}
          dateFormat={TypeFormatDate.DateFullWithDay}
          timeFormat={TypeFormatDate.DateFullWithDay}
          user={memoizedUser}
          renderAvatar={renderAvatar}
          renderCustomView={renderCustomView}
          locale={locale}
          bottomOffset={-insets.bottom + Spacing.SPACE_08}
          renderAvatarOnTop={true}
          showUserAvatar={false}
          renderTime={renderTime}
          renderSystemMessage={renderSystemMessage}
          infiniteScroll
          messagesContainerStyle={{
            paddingTop: isEmpty(task) ? insets?.top : Spacing.SPACE_04,
          }}
          invertibleScrollViewProps={{
            contentContainerStyle: {
              paddingVertical: 100,
            },
          }}
        />
      </BlockView>
    </BlockView>
  );
};

/**
 * Memoized ChatMessage component to prevent unnecessary re-renders
 * Purpose: Optimizes performance by preventing re-renders when props haven't changed
 */
export const ChatHistoryMessage = memo(ChatHistoryMessageComponent);
