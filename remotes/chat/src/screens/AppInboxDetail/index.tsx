/**
 * AppInboxDetail screen component
 *
 * Purpose: Displays comprehensive CleverTap app inbox notification details including
 * promotional content, reward information, action buttons, and navigation handling.
 * Features modern design system components, proper TypeScript typing, and optimized performance.
 *
 * Key Features:
 * - CleverTap app inbox notification display
 * - Reward content with markdown support
 * - Promotion code copy functionality
 * - Dynamic action button handling
 * - Header animation with background images
 * - Expiration date validation
 * - Multi-language support
 *
 * Migrated from: unicorn-btaskee-version-v3/src/screens/app-inbox-detail/layout/index.js
 */
import React, { useCallback, useMemo } from 'react';
import {
  BlockView,
  ChatRouteName,
  ChatStackParamList,
  Colors,
  DeviceHelper,
  HeaderAnimated,
  NavigationService,
  openUrl,
  PrimaryButton,
  SizedBox,
  Spacing,
  useReward,
} from '@btaskee/design-system';
import { RouteProp, useRoute } from '@react-navigation/native';
import dayjs from 'dayjs';
import { get } from 'lodash-es';

import { useAppNavigation, useI18n } from '@hooks';
import { backgroundAlertSub } from '@images';

import { RewardContent } from './components/RewardContent';
import { RewardHeader } from './components/RewardHeader';
import { styles } from './styles';

/**
 * Type definition for AppInboxDetail route parameters
 * Purpose: Provides type safety for navigation route parameters
 */
type AppInboxDetailRouteProp = RouteProp<
  ChatStackParamList,
  ChatRouteName.AppInboxDetail
>;

/**
 * Interface for CleverTap reward data structure
 * Purpose: Comprehensive type definition for app inbox notification data
 *
 * @interface IRewardData
 * @property {string} _id - Unique identifier for the reward
 * @property {string} [title] - Display title of the reward/notification
 * @property {string | object} [message] - Main content message (can be localized object)
 * @property {string} [note] - Additional notes or terms of use
 * @property {string} [image] - URL for reward header image
 * @property {string} [url] - External URL to open when action is triggered
 * @property {string} [action] - Text for the action button
 * @property {string} [promotionCode] - Promotion code for copying
 * @property {string} [screenName] - Target screen name for navigation
 * @property {string} [serviceId] - Service ID for service selection
 * @property {string} [incentiveId] - Incentive identifier
 * @property {string} [surveyId] - Survey identifier
 * @property {string} [taskId] - Task identifier
 * @property {string} [giftId] - Gift identifier
 * @property {string} [comboVoucherId] - Combo voucher identifier
 * @property {string} [campaignId] - Campaign identifier
 * @property {number} [expired] - Expiration timestamp (Unix timestamp)
 * @property {boolean} [hasUrl] - Whether the reward has an external URL
 * @property {boolean} [hasAction] - Whether the reward has an action button
 * @property {string | Date} createdAt - Creation timestamp
 * @property {string} [routeName] - Target route name for navigation
 * @property {string} [miniAppScreenName] - Target mini app screen name
 * @property {Record<string, any>} [params] - Additional navigation parameters
 */
interface IRewardData {
  _id: string;
  title?: string;
  message?: string | object;
  note?: string;
  image?: string;
  url?: string;
  action?: string;
  promotionCode?: string;
  screenName?: string;
  serviceId?: string;
  incentiveId?: string;
  surveyId?: string;
  taskId?: string;
  giftId?: string;
  comboVoucherId?: string;
  campaignId?: string;
  expired?: number;
  hasUrl?: boolean;
  hasAction?: boolean;
  createdAt: string | Date;
  screenNameOfMiniApp?: string;
  params?: Record<string, any>;
}

/**
 * AppInboxDetail screen component
 *
 * Purpose: Comprehensive CleverTap app inbox notification detail screen that displays
 * reward information, promotional content, and handles various user actions including
 * navigation, URL opening, and service selection.
 *
 * Features:
 * - Animated header with background image support
 * - Markdown content rendering for reward details
 * - Promotion code copy functionality
 * - Dynamic action button with expiration validation
 * - Multi-language content support
 * - Service selection integration
 * - External URL handling
 *
 * @returns {React.ReactElement} The AppInboxDetail screen component
 */
export const AppInboxDetail: React.FC = () => {
  const route = useRoute<AppInboxDetailRouteProp>();
  const navigation = useAppNavigation();
  const { t } = useI18n();
  const { onChooseService } = useReward();

  // Extract reward data from route parameters with type safety
  const rewardData = get(route, 'params.data', null) as IRewardData | null;
  const action = rewardData?.action;

  /**
   * Handles the main action button press for reward interactions
   *
   * Purpose: Processes different types of reward actions including service selection,
   * navigation to specific screens, and parameter passing for various reward types.
   *
   * Action Types:
   * - Service Selection: Navigates to post task with promotion code
   * - Screen Navigation: Routes to specific screens with parameters
   * - Parameter Passing: Includes all relevant IDs and campaign data
   *
   * @function handleUseCode
   * @returns {void}
   */
  const handleUseCode = useCallback(() => {
    if (!rewardData) {
      return;
    }

    // Extract navigation and reward parameters
    const {
      promotionCode,
      screenName,
      serviceId,
      incentiveId,
      surveyId,
      taskId,
      giftId,
      comboVoucherId,
      campaignId,
      screenNameOfMiniApp,
      params,
    } = rewardData;

    // Track CleverTap usage for analytics
    // TODO: Implement tracking function when tracking system is available
    // trackingCleverTapUsedAppInbox(user?.taskDone, rewardData);

    // Handle service selection flow
    if (serviceId) {
      return onChooseService({
        promotionCode,
        serviceId,
      });
    }

    // Handle general navigation with parameters
    NavigationService.navigateToScreen({
      screenName,
      params: {
        incentiveId,
        surveyId,
        taskId,
        giftId,
        comboVoucherId,
        campaignId,
        screenNameOfMiniApp,
        ...params,
      },
    });
  }, [onChooseService, rewardData]);

  /**
   * Renders the reward content sections with markdown support
   *
   * Purpose: Displays the main reward content and terms of use sections
   * with proper spacing and markdown rendering capabilities.
   *
   * Content Sections:
   * - Main Content: Reward description and details (title disabled)
   * - Terms of Use: Legal terms and conditions for the reward
   *
   * @function renderRewardContent
   * @returns {React.ReactElement} Memoized reward content sections
   */
  const renderRewardContent = useMemo(() => {
    return (
      <BlockView>
        <RewardContent
          disabledTitle={true}
          testID="rewardContent"
          title={t('REWARD_DETAIL_CONTENT_TITLE')}
          content={rewardData?.message}
        />
        <SizedBox height={Spacing.SPACE_24} />
        <RewardContent
          testID="rewardNote"
          title={t('TERM_OF_USE_REWARDS')}
          content={rewardData?.note}
        />
      </BlockView>
    );
  }, [rewardData?.message, rewardData?.note, t]);

  /**
   * Renders the appropriate action button based on reward configuration
   *
   * Purpose: Dynamically displays action buttons for different reward types
   * with proper validation and state management.
   *
   * Button Types:
   * - URL Button: Opens external URLs when hasUrl is true
   * - Action Button: Triggers internal actions with expiration validation
   * - No Button: Returns null when no action is available
   *
   * Features:
   * - Expiration date validation
   * - Disabled state for expired rewards
   * - External URL handling
   * - Fallback button text
   *
   * @function renderButtonSubmit
   * @returns {React.ReactElement | null} Memoized action button or null
   */
  const renderButtonSubmit = useMemo(() => {
    if (!action) {
      return null;
    }

    // Handle external URL actions (configured in CleverTap dashboard)
    if (rewardData?.url && rewardData?.hasUrl) {
      return (
        <PrimaryButton
          testID="btnUsedGift"
          title={rewardData.action || t('VIEW')}
          onPress={() => {
            if (rewardData.url) {
              openUrl(rewardData.url);
            }
          }}
        />
      );
    }

    // Return null if no action is configured
    if (!rewardData?.hasAction) {
      return null;
    }

    // Validate expiration date
    const isExpired = rewardData?.expired
      ? dayjs(rewardData.expired * 1000).isSameOrBefore(dayjs())
      : false;

    return (
      <PrimaryButton
        testID="btnUsedGift"
        disabled={isExpired}
        title={action}
        onPress={handleUseCode}
      />
    );
  }, [
    action,
    handleUseCode,
    rewardData?.action,
    rewardData?.expired,
    rewardData?.hasAction,
    rewardData?.hasUrl,
    rewardData?.url,
    t,
  ]);

  // Early return with null safety check
  if (!rewardData) {
    return null;
  }

  return (
    <BlockView
      inset={['bottom']}
      style={styles.container}
    >
      <HeaderAnimated
        height={DeviceHelper.WINDOW.HEIGHT * 0.25}
        backgroundImage={
          rewardData?.image ? { uri: rewardData.image } : backgroundAlertSub
        }
        onPressBack={() => navigation.goBack()}
        footer={
          renderButtonSubmit ? (
            <BlockView style={styles.submitButtonContainer}>
              {renderButtonSubmit}
            </BlockView>
          ) : null
        }
      >
        <BlockView
          flex
          backgroundColor={Colors.neutralWhite}
          padding={{ bottom: Spacing.SPACE_64 }}
        >
          <BlockView style={styles.contentWrapper}>
            <RewardHeader rewardData={rewardData} />
            {renderRewardContent}
          </BlockView>
        </BlockView>
      </HeaderAnimated>
    </BlockView>
  );
};
