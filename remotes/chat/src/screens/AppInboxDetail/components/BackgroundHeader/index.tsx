/**
 * BackgroundHeader component for AppInboxDetail
 *
 * Purpose: Intelligent header background component that displays reward-specific
 * images or fallback backgrounds for app inbox notifications. Features conditional
 * rendering, proper image sizing, and responsive design.
 *
 * Key Features:
 * - Conditional image rendering based on reward data
 * - Reward-specific image display with proper aspect ratio
 * - Fallback background for rewards without images
 * - Responsive image sizing and cover mode
 * - Optimized performance with FastImage
 * - Consistent styling across different states
 *
 * Migrated from: unicorn-btaskee-version-v3/src/screens/app-inbox-detail/layout/background-header.js
 */
import React from 'react';
import { BlockView, ConditionView, FastImage } from '@btaskee/design-system';

import { backgroundAlertSub } from '@images';

import { styles } from './styles';

/**
 * Interface for reward data structure
 *
 * Purpose: Defines the expected reward data properties for background display
 *
 * @interface IRewardData
 * @property {string} [image] - URL of the reward image to display
 * @property {string} [title] - Reward title (for potential future use)
 */
interface IRewardData {
  image?: string;
  title?: string;
}

/**
 * Props interface for BackgroundHeader component
 *
 * Purpose: Defines the component props structure with proper typing
 *
 * @interface BackgroundHeaderProps
 * @property {IRewardData | null} [rewardData] - Reward data object or null
 */
interface BackgroundHeaderProps {
  rewardData?: IRewardData | null;
}

/**
 * BackgroundHeader component
 *
 * Purpose: Renders conditional header background with reward image or fallback.
 * Uses ConditionView for efficient conditional rendering and FastImage for
 * optimized image loading and display.
 *
 * @param {BackgroundHeaderProps} props - Component props
 * @param {IRewardData | null} [props.rewardData] - Reward data containing image info
 * @returns {React.ReactElement} Rendered background header component
 */
export const BackgroundHeader: React.FC<BackgroundHeaderProps> = ({
  rewardData,
}) => {
  return (
    <ConditionView
      condition={Boolean(rewardData?.image)}
      viewTrue={
        <BlockView style={styles.header}>
          <FastImage
            style={styles.boxHeaderImage}
            resizeMode="cover"
            source={{ uri: rewardData?.image }}
          />
        </BlockView>
      }
      viewFalse={
        <BlockView style={styles.header}>
          <FastImage
            style={styles.boxHeaderImageEmpty}
            resizeMode="cover"
            source={backgroundAlertSub}
          />
        </BlockView>
      }
    />
  );
};
