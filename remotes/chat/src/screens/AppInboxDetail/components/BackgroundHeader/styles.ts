/**
 * Styles for BackgroundHeader component
 *
 * This file contains styling for the background header component
 * including image dimensions and layout using design system tokens.
 */
import { StyleSheet } from 'react-native';
import { DeviceHelper } from '@btaskee/design-system';

export const styles = StyleSheet.create({
  /**
   * Header container styling
   * Purpose: Container for the header background
   */
  header: {
    // No specific styling needed, handled by parent
  },

  /**
   * Header image styling for reward images
   * Purpose: Displays reward header image with proper aspect ratio (16:9)
   */
  boxHeaderImage: {
    width: DeviceHelper.WINDOW.WIDTH,
    height: (DeviceHelper.WINDOW.WIDTH * 9) / 16,
  },

  /**
   * Empty header image styling for fallback background
   * Purpose: Displays fallback background when no reward image is available
   */
  boxHeaderImageEmpty: {
    width: DeviceHelper.WINDOW.WIDTH,
    height: DeviceHelper.WINDOW.HEIGHT * 0.25, // Consistent with HeaderAnimated height
  },
});
