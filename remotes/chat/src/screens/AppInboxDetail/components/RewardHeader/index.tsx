/**
 * RewardHeader component for AppInboxDetail
 *
 * Purpose: Comprehensive reward header component that displays reward information
 * including title, creation date, and promotion code with copy-to-clipboard functionality.
 * Features proper date formatting, toast notifications, and accessibility support.
 *
 * Key Features:
 * - Reward title display with text truncation
 * - Formatted creation date with localization
 * - Promotion code with copy functionality
 * - Toast notification on successful copy
 * - Responsive layout and spacing
 * - Accessibility support with testIDs
 *
 * Migrated from: unicorn-btaskee-version-v3/src/screens/app-inbox-detail/layout/reward-header.js
 */
import React, { useCallback, useMemo } from 'react';
import {
  BlockView,
  Colors,
  CText,
  DateTimeHelpers,
  IconImage,
  SizedBox,
  Spacing,
  ToastHelpers,
  TouchableOpacity,
  TypeFormatDate,
} from '@btaskee/design-system';
import Clipboard from '@react-native-clipboard/clipboard';

import { useI18n } from '@hooks';
import { icCopy } from '@images';

import { styles } from './styles';

/**
 * Interface for reward data structure
 *
 * Purpose: Defines the expected reward data properties for the header component
 *
 * @interface IRewardData
 * @property {string} [title] - The reward title to display
 * @property {string | Date} [createdAt] - Creation timestamp for date formatting
 * @property {string} [promotionCode] - Promotion code for copy functionality
 */
interface IRewardData {
  title?: string;
  createdAt?: string | Date;
  promotionCode?: string;
}

/**
 * Props interface for RewardHeader component
 *
 * Purpose: Defines the component props structure with proper typing
 *
 * @interface RewardHeaderProps
 * @property {IRewardData | null} [rewardData] - Reward data object or null
 */
interface RewardHeaderProps {
  rewardData?: IRewardData | null;
}

/**
 * RewardHeader component
 *
 * Purpose: Renders comprehensive reward header with title, formatted date,
 * and interactive promotion code with copy functionality.
 *
 * Features:
 * - Title display with proper truncation
 * - Localized date formatting
 * - Copy-to-clipboard promotion code
 * - Toast notification feedback
 * - Accessibility support
 *
 * @param {RewardHeaderProps} props - Component props
 * @param {IRewardData | null} props.rewardData - Reward data object
 * @returns {React.ReactElement} Rendered reward header component
 */
export const RewardHeader: React.FC<RewardHeaderProps> = ({ rewardData }) => {
  const { t } = useI18n();

  /**
   * Handles copying promotion code to clipboard with user feedback
   *
   * Purpose: Memoized callback that copies text to clipboard and shows toast.
   * Optimized to prevent unnecessary re-renders when dependencies don't change.
   *
   * @function handleCopyToClipboard
   * @param {string} text - The text to copy to clipboard
   * @returns {void}
   */
  const handleCopyToClipboard = useCallback(
    (text: string): void => {
      ToastHelpers.showSuccess({
        message: t('LABEL_COPY'),
        position: 'top',
      });
      Clipboard.setString(text);
    },
    [t],
  );

  /**
   * Formats the creation date for display with localization
   *
   * Purpose: Memoized date formatting to prevent unnecessary recalculations.
   * Only recalculates when the creation date changes.
   *
   * @constant formattedDate
   * @type {string}
   */
  const formattedDate = useMemo(() => {
    return rewardData?.createdAt
      ? DateTimeHelpers.formatToString({
          date: rewardData.createdAt,
          typeFormat: TypeFormatDate.DateFullWithDay,
        })
      : '';
  }, [rewardData?.createdAt]);

  return (
    <BlockView>
      {/* Header Content */}
      <BlockView style={styles.headerContent}>
        <BlockView margin={{ top: Spacing.SPACE_16 }}>
          <CText
            numberOfLines={2}
            style={styles.txtNameReward}
            testID="txtRewardTitle"
          >
            {rewardData?.title}
          </CText>
          <SizedBox height={Spacing.SPACE_08} />
          <CText
            color={Colors.neutral600}
            testID="txtRewardDate"
          >
            {formattedDate}
          </CText>
        </BlockView>

        {/* Promotion Code Section */}
        {rewardData?.promotionCode ? (
          <TouchableOpacity
            onPress={() => handleCopyToClipboard(rewardData.promotionCode!)}
            style={styles.wrapCode}
            testID="btnCopyPromotionCode"
          >
            <BlockView style={styles.promotionCodeContainer}>
              <CText
                testID="txtPromotionCodeDetailIncentive"
                style={styles.txtPromotionCode}
              >
                {rewardData.promotionCode}
              </CText>
              <IconImage
                source={icCopy}
                size={20}
                color={Colors.green500}
              />
            </BlockView>
          </TouchableOpacity>
        ) : null}
      </BlockView>
    </BlockView>
  );
};
