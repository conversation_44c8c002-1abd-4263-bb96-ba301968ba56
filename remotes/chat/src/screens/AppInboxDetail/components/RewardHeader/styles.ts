/**
 * Styles for RewardHeader component
 *
 * This file contains styling for the reward header component
 * including text styling, promotion code container, and spacing using design system tokens.
 */
import { StyleSheet } from 'react-native';
import {
  BorderRadius,
  Colors,
  FontSizes,
  Spacing,
} from '@btaskee/design-system';

export const styles = StyleSheet.create({
  /**
   * Header content container
   * Purpose: Provides horizontal padding for header content
   */
  headerContent: {
    paddingHorizontal: Spacing.SPACE_16, // constant.PADDING_CONTENT equivalent
  },

  /**
   * Reward name text styling
   * Purpose: Displays the main reward title with emphasis
   */
  txtNameReward: {
    fontSize: FontSizes.SIZE_20, // constant.FONT_SIZE.large2 equivalent
    fontWeight: 'bold',
    color: Colors.neutral900, // constant.COLOR.black2 equivalent
  },

  /**
   * Promotion code wrapper styling
   * Purpose: Container for promotion code display with copy functionality
   */
  wrapCode: {
    marginTop: Spacing.SPACE_16, // constant.MARGIN.medium equivalent
    marginBottom: Spacing.SPACE_04, // constant.MARGIN.tiny equivalent
  },

  /**
   * Promotion code container styling
   * Purpose: Background and padding for promotion code display
   */
  promotionCodeContainer: {
    backgroundColor: Colors.neutralBackground, // constant.UNDERLAY_COLOR equivalent
    borderRadius: BorderRadius.RADIUS_08, // constant.BORDER_RADIUS equivalent
    padding: Spacing.SPACE_08, // constant.MARGIN.small equivalent
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },

  /**
   * Promotion code text styling
   * Purpose: Styling for the promotion code text
   */
  txtPromotionCode: {
    fontSize: FontSizes.SIZE_16,
    fontWeight: 'bold',
    color: Colors.orange500, // constant.PRIMARY_COLOR equivalent
  },
});
