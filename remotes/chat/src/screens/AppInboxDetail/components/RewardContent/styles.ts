/**
 * Styles for RewardContent component
 *
 * This file contains styling for the reward content component
 * including text styling, content layout, and markdown image styling using design system tokens.
 */
import { StyleSheet } from 'react-native';
import {
  BorderRadius,
  Colors,
  FontSizes,
  Spacing,
} from '@btaskee/design-system';

export const styles = StyleSheet.create({
  /**
   * Content container for reward information
   * Purpose: Provides spacing for content sections
   */
  containerContent: {
    paddingHorizontal: Spacing.SPACE_16, // constant.PADDING_CONTENT equivalent
  },

  /**
   * Reward info section styling
   * Purpose: Container for reward information content
   */
  rewardInfo: {
    // No specific styling needed
  },

  /**
   * Title content text styling
   * Purpose: Styling for section titles
   */
  txtTitleContent: {
    fontSize: FontSizes.SIZE_16,
    fontWeight: 'bold',
    color: Colors.neutral900,
    marginBottom: Spacing.SPACE_08,
  },

  /**
   * Content text styling for markdown content
   * Purpose: Provides base styling for reward content text
   */
  txtContent: {
    marginTop: Spacing.SPACE_08, // constant.MARGIN.small equivalent
    fontSize: FontSizes.SIZE_14,
    color: Colors.neutral800,
    lineHeight: FontSizes.SIZE_20,
  },

  /**
   * Markdown image styling
   * Purpose: Styling for images within markdown content
   */
  imgMarkdown: {
    borderRadius: BorderRadius.RADIUS_08, // constant.BORDER_RADIUS equivalent
    overflow: 'hidden',
  },
});
