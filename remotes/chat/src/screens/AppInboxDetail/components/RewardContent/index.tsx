/**
 * RewardContent component for AppInboxDetail
 *
 * Purpose: Versatile content display component that renders reward information
 * with markdown support, localization handling, and flexible title configuration.
 * Supports both string and localized object content types.
 *
 * Key Features:
 * - Markdown content rendering with custom styling
 * - Localized content support (string or object)
 * - Optional title display with toggle
 * - Image styling for markdown images
 * - Null safety and early returns
 * - Accessibility support with testIDs
 * - Responsive layout and spacing
 *
 * Migrated from: unicorn-btaskee-version-v3/src/screens/app-inbox-detail/layout/reward-content.js
 */
import React, { useMemo } from 'react';
import {
  BlockView,
  CText,
  getTextWithLocale,
  Markdown,
} from '@btaskee/design-system';

import { styles } from './styles';

/**
 * Props interface for RewardContent component
 *
 * Purpose: Comprehensive type definition for component props with detailed descriptions
 *
 * @interface RewardContentProps
 * @property {string} [title] - Optional section title to display above content
 * @property {string | object} [content] - Content to display (string or localized object)
 * @property {string} [testID] - Test identifier for automated testing
 * @property {boolean} [disabledTitle] - Flag to hide the title section
 */
interface RewardContentProps {
  /** The title of the content section */
  title?: string;
  /** The content to display - can be string or localized object */
  content?: string | object;
  /** Test ID for testing purposes */
  testID?: string;
  /** Whether to hide the title */
  disabledTitle?: boolean;
}

/**
 * RewardContent component
 *
 * Purpose: Renders reward content with optional title and comprehensive markdown support.
 * Handles content localization, null safety, and provides flexible display options.
 *
 * Features:
 * - Content type detection and processing
 * - Localization support for object content
 * - Optional title display
 * - Markdown rendering with custom styling
 * - Early returns for performance
 * - Null safety throughout
 *
 * @param {RewardContentProps} props - Component props
 * @param {string} [props.title] - Optional section title
 * @param {string | object} [props.content] - Content to display
 * @param {string} [props.testID] - Test identifier for testing
 * @param {boolean} [props.disabledTitle] - Whether to hide the title
 * @returns {React.ReactElement | null} Rendered content component or null
 */
export const RewardContent: React.FC<RewardContentProps> = ({
  title,
  content,
  testID,
  disabledTitle,
}) => {
  // Memoized content processing for performance optimization
  const processedContent = useMemo(() => {
    if (!content) return null;

    if (typeof content === 'object' && content !== null) {
      return getTextWithLocale(content as any); // Type assertion needed for getTextWithLocale
    }
    return content as string;
  }, [content]);

  // Early return if no content or processed content
  if (!content || !processedContent) return null;

  return (
    <BlockView style={styles.containerContent}>
      <BlockView style={styles.rewardInfo}>
        {/* Conditional title rendering */}
        {!disabledTitle && title ? (
          <CText style={styles.txtTitleContent}>{title}</CText>
        ) : null}

        {/* Content with markdown support */}
        <BlockView testID={testID}>
          <Markdown
            text={processedContent}
            textStyle={styles.txtContent}
            imageStyle={styles.imgMarkdown}
          />
        </BlockView>
      </BlockView>
    </BlockView>
  );
};
