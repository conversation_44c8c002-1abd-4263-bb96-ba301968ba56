/**
 * Styles for the AppInboxDetail screen
 *
 * This file contains all styling for the app inbox detail screen
 * including layout, spacing, colors, and typography using design system tokens.
 * Migrated from legacy styles with modern design system components.
 */
import { StyleSheet } from 'react-native';
import { Colors, DeviceHelper, Spacing } from '@btaskee/design-system';

export const styles = StyleSheet.create({
  /**
   * Main container for the app inbox detail screen
   * Purpose: Provides full screen layout with proper background color
   */
  container: {
    flex: 1,
    backgroundColor: Colors.neutralWhite,
  },

  /**
   * Content wrapper for the scrollable area
   * Purpose: Provides proper spacing and background for the main content
   */
  contentWrapper: {
    flex: 1,
    paddingHorizontal: Spacing.SPACE_16, // constant.PADDING_CONTENT equivalent
  },

  /**
   * Header section styling
   * Purpose: Container for the header background image
   */
  header: {
    // No specific styling needed, handled by HeaderAnimated
  },

  /**
   * Header image styling for reward images
   * Purpose: Displays reward header image with proper aspect ratio (16:9)
   */
  boxHeaderImage: {
    width: DeviceHelper.WINDOW.WIDTH,
    height: (DeviceHelper.WINDOW.WIDTH * 9) / 16,
  },

  /**
   * Empty header image styling for fallback background
   * Purpose: Displays fallback background when no reward image is available
   */
  boxHeaderImageEmpty: {
    width: DeviceHelper.WINDOW.WIDTH,
    height: DeviceHelper.WINDOW.HEIGHT * 0.25, // Consistent with HeaderAnimated height
  },

  // Note: Component-specific styles moved to respective component style files
  // This reduces duplication and improves maintainability

  /**
   * Submit button container positioning
   * Purpose: Positions the action button at the bottom of the screen
   */
  submitButtonContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: Colors.neutralWhite,
    paddingHorizontal: Spacing.SPACE_16,
    paddingVertical: Spacing.SPACE_12,
    borderTopWidth: 1,
    borderTopColor: Colors.neutral100,
  },

  // Note: Promotion code styles moved to RewardHeader component styles
  // This eliminates duplication and improves component encapsulation
});
