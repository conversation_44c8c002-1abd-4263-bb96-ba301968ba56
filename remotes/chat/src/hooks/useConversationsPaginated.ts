import { useCallback, useMemo } from 'react';
import {
  EndpointKeys,
  IConversationChatItem,
  queryKeys,
  undefinedValue,
  useApiInfiniteQuery,
  useAppStore,
  useUserStore,
} from '@btaskee/design-system';
import { PAGINATION_DEFAULT } from '@constants';
import { flatten, isArray, isNil, size } from 'lodash-es';

interface UseConversationsPagination {
  readonly data: ReadonlyArray<IConversationChatItem>;
  readonly isLoading: boolean;
  readonly isFetching: boolean;
  readonly isLoadingMore: boolean;
  readonly hasMore: boolean;
  readonly error: Error | null;
  readonly loadMore: () => void;
  readonly refetch: () => void;
}

/**
 * Custom hook for paginated notification queries with focus-based API integration
 * Purpose: Provides optimized pagination functionality for notification list with
 * focus-triggered API calls, following established architectural patterns
 *
 * Features:
 * - Focus-triggered API calls (single API call per focus event)
 * - Intelligent pagination with sufficient items check
 * - Data accumulation with deduplication
 * - Proper loading states for initial load and pagination
 * - Error handling and retry logic
 * - Performance optimizations following established patterns
 *
 * @example
 * ```tsx
 * const {
 *   data,
 *   page,
 *   isLoading,
 *   isLoadingMore,
 *   hasMore,
 *   loadMore,
 *   refetch
 * } = useNotificationsPaginated();
 * ```
 */
export const useConversationsPaginated = (): UseConversationsPagination => {
  const { isoCode } = useAppStore();
  const { user } = useUserStore();

  const queryKey = useMemo(
    () => [...queryKeys.conversations.list(user?._id!)],
    [user?._id],
  );

  const isQueryEnabled = useMemo(
    () => Boolean(user?._id && isoCode),
    [user?._id, isoCode],
  );

  const queryParams = useMemo(
    () => ({
      isoCode: isoCode!,
      limit: PAGINATION_DEFAULT.limit,
    }),
    [isoCode],
  );

  const {
    data,
    error,
    fetchNextPage,
    isFetching,
    isLoading,
    hasNextPage,
    isFetchingNextPage,
    refetch,
  } = useApiInfiniteQuery({
    key: EndpointKeys.getConversations,
    queryKey,
    params: queryParams,
    options: {
      enabled: isQueryEnabled,
      refetchOnWindowFocus: true, // Enable automatic refetch on focus
      refetchOnMount: true, // Refetch when component mounts
      getNextPageParam: (lastPage, allPages) => {
        if (isArray(lastPage) && size(lastPage) < PAGINATION_DEFAULT.limit) {
          return undefinedValue;
        }
        return allPages.length + 1;
      },
    },
  });

  const flattenedData = useMemo(() => {
    return flatten(data?.pages).filter((item) => !isNil(item));
  }, [data]);

  const onRefresh = useCallback(() => {
    // Don't remove queries - this preserves cached data and prevents unnecessary loading states
    // React Query will show existing data while refetching in the background
    refetch().catch();
  }, [refetch]);

  const handleEndReached = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage && !isFetching) {
      fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, isFetching, fetchNextPage]);

  return {
    data: flattenedData,
    isLoading,
    isFetching,
    isLoadingMore: isFetchingNextPage,
    hasMore: hasNextPage,
    error: error as unknown as Error,
    loadMore: handleEndReached,
    refetch: onRefresh,
  };
};
