import { useCallback, useEffect, useMemo } from 'react';
import {
  EndpointKeys,
  queryKeys,
  undefinedValue,
  useApiInfiniteQuery,
  useAppStore,
  useQueryClient,
  useUserStore,
} from '@btaskee/design-system';
import { PAGINATION_DEFAULT } from '@constants';
import { getAllInboxCleverTap } from '@helper';
import type { INotificationUnion } from '@types';
import dayjs from 'dayjs';
import { flatten, isArray, isNil, orderBy, size } from 'lodash-es';

import { useNotificationStore } from '@stores';

const CleverTap = require('clevertap-react-native');

interface UsePaginatedNotificationFocusResult {
  readonly data: ReadonlyArray<INotificationUnion>;
  readonly isLoading: boolean;
  readonly isLoadingMore: boolean;
  readonly isLoadingCleverTap?: boolean;
  readonly hasMore: boolean;
  readonly error: Error | null;
  readonly loadMore: () => void;
  readonly refetch: () => void;
}

/**
 * Custom hook for paginated notification queries with focus-based API integration
 * Purpose: Provides optimized pagination functionality for notification list with
 * focus-triggered API calls, following established architectural patterns
 *
 * Features:
 * - Focus-triggered API calls (single API call per focus event)
 * - Intelligent pagination with sufficient items check
 * - Data accumulation with deduplication
 * - Proper loading states for initial load and pagination
 * - Error handling and retry logic
 * - Performance optimizations following established patterns
 *
 * @example
 * ```tsx
 * const {
 *   data,
 *   page,
 *   isLoading,
 *   isLoadingMore,
 *   hasMore,
 *   loadMore,
 *   refetch
 * } = usePaginatedNotificationFocus();
 * ```
 */
export const useNotificationsPaginated =
  (): UsePaginatedNotificationFocusResult => {
    const { user } = useUserStore();
    const { isoCode } = useAppStore();
    const queryClient = useQueryClient();
    const { setNotifications, setCleverTapNotifications } =
      useNotificationStore();

    const queryKey = useMemo(
      () => [...queryKeys.notifications.list(user?._id!)],
      [user?._id],
    );

    const isQueryEnabled = useMemo(
      () => Boolean(user?._id && isoCode),
      [user?._id, isoCode],
    );

    const queryParams = useMemo(
      () => ({
        isoCode: isoCode!,
        limit: PAGINATION_DEFAULT.limit,
      }),
      [isoCode],
    );

    const {
      data,
      error,
      fetchNextPage,
      isFetching,
      isLoading,
      hasNextPage,
      isFetchingNextPage,
      refetch,
    } = useApiInfiniteQuery({
      key: EndpointKeys.getNotifications,
      queryKey,
      params: queryParams,
      options: {
        enabled: isQueryEnabled,
        refetchOnWindowFocus: true, // Enable automatic refetch on focus
        refetchOnMount: true, // Refetch when component mounts
        getNextPageParam: (lastPage, allPages) => {
          if (isArray(lastPage) && size(lastPage) < PAGINATION_DEFAULT.limit) {
            return undefinedValue;
          }
          return allPages.length + 1;
        },
      },
    });

    const getAppInboxCleverTap = () => {
      // Remove inboxes after 2 months
      const currentState = useNotificationStore.getState();
      const { cleverTapNotifications } = currentState;
      const cloneAppInbox = cleverTapNotifications.filter((item) => {
        return dayjs(item?.createdAt).isAfter(dayjs().subtract(2, 'month'));
      });

      let allInboxCleverTapCustom = [];
      CleverTap.getAllInboxMessages(async (_err, res) => {
        if (res) {
          allInboxCleverTapCustom = await getAllInboxCleverTap(res);
          // If the inbox is not saved in the device, it will be saved
          // Inbox is saved for 2 months, after 2 months will be deleted
          for (let index = 0; index < allInboxCleverTapCustom.length; index++) {
            const element = allInboxCleverTapCustom[index];
            const found = cloneAppInbox.findIndex(
              (item) => item?._id === element?._id,
            );
            if (found === -1) {
              cloneAppInbox.push(element);
            } else {
              cloneAppInbox[found] = element;
            }
          }
        }
      });
      setCleverTapNotifications(cloneAppInbox);
    };

    const flattenedData = useMemo(() => {
      const currentState = useNotificationStore.getState();
      const { cleverTapNotifications } = currentState;
      const dataNotifications = flatten(data?.pages).filter(
        (item) => !isNil(item),
      ) as any;
      const listNotifications = dataNotifications.concat(
        cleverTapNotifications,
      );
      const resultSorted = orderBy(
        listNotifications,
        function (e) {
          return dayjs(e.createdAt).toDate().getTime();
        },
        'desc',
      );
      return resultSorted;
    }, [data]);

    // Update notifications store when data changes

    useEffect(() => {
      getAppInboxCleverTap();
    }, []);

    useEffect(() => {
      setNotifications(flattenedData);
    }, [flattenedData, setNotifications]);

    const onRefresh = useCallback(() => {
      queryClient.removeQueries({ queryKey: [EndpointKeys.getNotifications] });
      refetch().catch();
    }, [queryClient, refetch]);

    const handleEndReached = useCallback(() => {
      if (hasNextPage && !isFetchingNextPage && !isFetching) {
        fetchNextPage();
      }
    }, [hasNextPage, isFetchingNextPage, isFetching, fetchNextPage]);

    return {
      data: flattenedData,
      isLoading,
      isLoadingMore: isFetchingNextPage,
      hasMore: hasNextPage,
      error: error as unknown as Error,
      loadMore: handleEndReached,
      refetch: onRefresh,
    };
  };
