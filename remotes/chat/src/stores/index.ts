import {
  App<PERSON><PERSON><PERSON>,
  ChatRouteName,
  create<PERSON>ustand,
  IConversationChat,
  IConversationChatItem,
  ITaskDetail,
  IUser,
  Maybe,
} from '@btaskee/design-system';
import { createJSONStorage, persist } from 'zustand/middleware';

import type {
  ICleverTapNotificationItem,
  IMessage,
  INotificationStore,
  INotificationUnion,
  ITaskerInfo,
  MessageStatus,
} from '../types';

/**
 * Interface for conversation chat store state
 * Purpose: Manages conversation list data, pagination, loading states, and user interactions
 */
interface ConversationChatState {
  tabFocused: string;
  conversations: IConversationChatItem[];
  isLoading: boolean;
  isRefreshing: boolean;
  currentPage: number;
  isLoadMore: boolean;
  badgeCount: number;
  chatId: Maybe<string>;
  task: Maybe<ITaskDetail>;
  memberIds: Maybe<string[]>;
  isTaskerFavorite: boolean;
  taskerInfo: Maybe<ITaskerInfo>;
  conversation: Maybe<IConversationChat>;
  messages: Maybe<IMessage[]>;
  setTabFocused: (tabFocused: string) => void;
  setConversations: (conversations: IConversationChatItem[]) => void;
  appendConversations: (conversations: IConversationChatItem[]) => void;
  setLoading: (isLoading: boolean) => void;
  setRefreshing: (isRefreshing: boolean) => void;
  setCurrentPage: (page: number) => void;
  setIsLoadMore: (isLoadMore: boolean) => void;
  setBadgeCount: (count: number) => void;
  setChatId: (chatId: Maybe<string>) => void;
  setTask: (task: Maybe<ITaskDetail>) => void;
  setMemberIds: (memberIds: Maybe<string[]>) => void;
  setIsTaskerFavorite: (isTaskerFavorite: boolean) => void;
  setTaskerInfo: (taskerInfo: Maybe<ITaskerInfo>) => void;
  setConversation: (conversation: Maybe<IConversationChat>) => void;
  setMessages: (
    messages:
      | Maybe<IMessage[]>
      | ((current: Maybe<IMessage[]>) => Maybe<IMessage[]>),
  ) => void;
  updateMessageStatus: (messageId: string, status?: MessageStatus) => void;
  resetConversationState: () => void;
  resetMessageState: () => void;
}

/**
 * Zustand store for managing conversation chat state
 * Purpose: Provides centralized state management for conversation list functionality
 * including data fetching, pagination, loading states, and badge notifications
 */
export const useConversationChatStore = createZustand<ConversationChatState>()(
  (set, get) => ({
    tabFocused: ChatRouteName.TabConversation,
    conversations: [],
    isLoading: false,
    isRefreshing: false,
    currentPage: 1,
    isLoadMore: true,
    badgeCount: 0,
    // Chat message
    chatId: null,
    task: null,
    memberIds: null,
    isTaskerFavorite: false,
    taskerInfo: null,
    conversation: null,
    messages: [],
    setTabFocused: (tabFocused: string) => set({ tabFocused }),

    /**
     * Sets the complete conversation list
     * Purpose: Updates the entire conversation array (used for initial load and refresh)
     * @param conversations - Array of conversation items to set
     */
    setConversations: (conversations: IConversationChatItem[]) =>
      set({ conversations }),

    /**
     * Appends conversations to the existing list
     * Purpose: Adds new conversations to the end of the list (used for pagination)
     * @param conversations - Array of conversation items to append
     */
    appendConversations: (conversations: IConversationChatItem[]) =>
      set((state) => ({
        conversations: [...state.conversations, ...conversations],
      })),

    /**
     * Sets the loading state
     * Purpose: Controls the main loading indicator display
     * @param isLoading - Boolean indicating if data is being loaded
     */
    setLoading: (isLoading: boolean) => set({ isLoading }),

    /**
     * Sets the refreshing state
     * Purpose: Controls the pull-to-refresh indicator display
     * @param isRefreshing - Boolean indicating if data is being refreshed
     */
    setRefreshing: (isRefreshing: boolean) => set({ isRefreshing }),

    /**
     * Sets the current page number
     * Purpose: Tracks pagination state for load more functionality
     * @param page - Current page number for pagination
     */
    setCurrentPage: (page: number) => set({ currentPage: page }),

    /**
     * Sets whether more data can be loaded
     * Purpose: Controls whether load more functionality should be available
     * @param isLoadMore - Boolean indicating if more data is available
     */
    setIsLoadMore: (isLoadMore: boolean) => set({ isLoadMore }),

    /**
     * Sets the unread message badge count
     * Purpose: Updates the notification badge count for unread conversations
     * @param count - Number of unread conversations
     */
    setBadgeCount: (count: number) => set({ badgeCount: count }),

    setChatId: (chatId: Maybe<string>) => set({ chatId }),
    setTask: (task: Maybe<ITaskDetail>) => set({ task }),
    setMemberIds: (memberIds: Maybe<string[]>) => set({ memberIds }),
    setIsTaskerFavorite: (isTaskerFavorite: boolean) =>
      set({ isTaskerFavorite }),
    setTaskerInfo: (taskerInfo: Maybe<ITaskerInfo>) => set({ taskerInfo }),
    setConversation: (conversation: Maybe<IConversationChat>) =>
      set({ conversation }),
    setMessages: (
      messages:
        | Maybe<IMessage[]>
        | ((current: Maybe<IMessage[]>) => Maybe<IMessage[]>),
    ) =>
      set((state) => ({
        messages:
          typeof messages === 'function' ? messages(state.messages) : messages,
      })),

    /**
     * Updates the status of a specific message
     * Purpose: Optimizes message status updates without full re-fetch
     * @param messageId - ID of the message to update
     * @param status - New status for the message
     */
    updateMessageStatus: (messageId: string, status?: MessageStatus) =>
      set((state) => ({
        messages:
          state.messages?.map((msg) =>
            (msg._id === messageId ? { ...msg, status } : msg),
          ) || [],
      })),

    /**
     * Resets all conversation state to initial values
     * Purpose: Clears all conversation data and resets pagination state
     */
    resetConversationState: () =>
      set({
        conversations: [],
        isLoading: false,
        isRefreshing: false,
        currentPage: 1,
        isLoadMore: true,
        badgeCount: 0,
      }),
    resetMessageState: () => set({ messages: [] }),
  }),
);

/**
 * Enhanced notification store interface with alert permission management
 * Purpose: Consolidates all notification-related state including list management and permission alerts
 */
interface IEnhancedNotificationStore extends INotificationStore {
  // Alert permission state
  isShowAlertPermission: boolean;
  isTurnOffPermissionNotification: boolean;

  // Alert permission actions
  setIsShowAlertPermission: (isShow: boolean) => void;
  setIsTurnOffPermissionNotification: (isTurnOff: boolean) => void;
  resetAlertPermissionState: () => void;
}

/**
 * Consolidated Zustand store for managing all notification-related state
 * Purpose: Provides centralized state management for notification list functionality,
 * alert permissions, data fetching, pagination, loading states, and CleverTap integration
 * Uses selective persistence to save only user preferences while keeping transient data in memory
 * Replaces Redux notification reducer with modern Zustand implementation
 */
export const useNotificationStore = createZustand<IEnhancedNotificationStore>()(
  persist(
    (set, get) => ({
      // Notification list state (not persisted)
      notifications: [],
      cleverTapNotifications: [],
      isLoading: true,
      isRefreshing: false,
      isLoadingMore: false,
      currentPage: 1,
      hasMore: true,
      isGetFullNotify: false,
      error: null,

      // Alert permission state (persisted)
      isShowAlertPermission: true, // Default to true for first-time users
      isTurnOffPermissionNotification: false, // Always check fresh, not persisted

      /**
       * Sets the complete notification list
       * Purpose: Updates the entire notification array (used for initial load and refresh)
       * @param notifications - Array of notification items to set
       */
      setNotifications: (notifications: INotificationUnion[]) =>
        set({ notifications }),

      /**
       * Appends notifications to the existing list
       * Purpose: Adds new notifications to the end of the list (used for pagination)
       * @param notifications - Array of notification items to append
       */
      appendNotifications: (notifications: INotificationUnion[]) =>
        set((state) => ({
          notifications: [...state.notifications, ...notifications],
        })),

      /**
       * Sets CleverTap notifications separately
       * Purpose: Manages CleverTap notifications independently from regular notifications
       * @param notifications - Array of CleverTap notification items
       */
      setCleverTapNotifications: (
        notifications: ICleverTapNotificationItem[],
      ) => set({ cleverTapNotifications: notifications }),

      /**
       * Sets the loading state
       * Purpose: Controls the main loading indicator display
       * @param isLoading - Boolean indicating if data is being loaded
       */
      setLoading: (isLoading: boolean) => set({ isLoading }),

      /**
       * Sets the refreshing state
       * Purpose: Controls the pull-to-refresh indicator display
       * @param isRefreshing - Boolean indicating if data is being refreshed
       */
      setRefreshing: (isRefreshing: boolean) => set({ isRefreshing }),

      /**
       * Sets the loading more state
       * Purpose: Controls the pagination loading indicator display
       * @param isLoadingMore - Boolean indicating if more data is being loaded
       */
      setLoadingMore: (isLoadingMore: boolean) => set({ isLoadingMore }),

      /**
       * Sets the current page number
       * Purpose: Tracks pagination state for load more functionality
       * @param page - Current page number for pagination
       */
      setCurrentPage: (page: number) => set({ currentPage: page }),

      /**
       * Sets whether more data can be loaded
       * Purpose: Controls whether load more functionality should be available
       * @param hasMore - Boolean indicating if more data is available
       */
      setHasMore: (hasMore: boolean) => set({ hasMore }),

      /**
       * Sets whether all notifications have been fetched
       * Purpose: Indicates if the complete notification list has been loaded
       * @param isGetFullNotify - Boolean indicating if all notifications are loaded
       */
      setIsGetFullNotify: (isGetFullNotify: boolean) =>
        set({ isGetFullNotify }),

      /**
       * Sets the error state
       * Purpose: Manages error states for notification operations
       * @param error - Error object or null if no error
       */
      setError: (error: Error | null) => set({ error }),

      /**
       * Marks a specific notification as read
       * Purpose: Updates the isRead status of a notification and updates the list
       * @param notificationId - ID of the notification to mark as read
       */
      markAsRead: (notificationId: string) =>
        set((state) => ({
          notifications: state.notifications.map((notification) =>
            (notification._id === notificationId
              ? { ...notification, isRead: true }
              : notification),
          ),
        })),

      /**
       * Sets whether to show the alert permission component
       * Purpose: Controls the visibility of the notification permission alert
       * Automatically persisted to storage when changed
       * @param isShow - Boolean indicating if alert should be shown
       */
      setIsShowAlertPermission: (isShow: boolean) =>
        set({ isShowAlertPermission: isShow }),

      /**
       * Sets whether notification permissions are turned off
       * Purpose: Tracks the current notification permission status
       * This is not persisted as it should be checked fresh each time
       * @param isTurnOff - Boolean indicating if notifications are disabled
       */
      setIsTurnOffPermissionNotification: (isTurnOff: boolean) =>
        set({ isTurnOffPermissionNotification: isTurnOff }),

      /**
       * Resets all notification state to initial values
       * Purpose: Clears all notification data and resets pagination state
       */
      resetNotificationState: () =>
        set({
          notifications: [],
          isLoading: true,
          isRefreshing: false,
          isLoadingMore: false,
          currentPage: 1,
          hasMore: true,
          isGetFullNotify: false,
          error: null,
        }),

      /**
       * Resets alert permission state to initial values
       * Purpose: Clears alert permission state when needed
       */
      resetAlertPermissionState: () =>
        set({
          isShowAlertPermission: true, // Reset to show alert again
          isTurnOffPermissionNotification: false,
        }),
    }),
    {
      name: 'notification-storage',
      storage: createJSONStorage(() => AppStorage.zustandStorage),
      version: 1,
      // Only persist the user's choice about showing the alert
      // Don't persist notification list data or permission status as they should be fresh
      partialize: (state) => ({
        isShowAlertPermission: state.isShowAlertPermission,
        cleverTapNotifications: state.cleverTapNotifications,
      }),
    },
  ),
);
