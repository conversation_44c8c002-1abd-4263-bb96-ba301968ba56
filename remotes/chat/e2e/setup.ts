import { device } from 'detox';

import { USER } from './user';

beforeAll(async () => {
  await device.launchApp({
    newInstance: true,
    permissions: {
      notifications: 'YES',
      userTracking: 'YES',
      location: 'always',
      camera: 'YES',
      medialibrary: 'YES',
      photos: 'YES',
    },
    launchArgs: {
      isE2ETesting: true,
      initialRouteName: 'ChatManagement',
      detoxDebugSynchronization: 1000,
      isoCode: 'VN', // Default country code, // Default country code
      requireUser: JSON.stringify(USER),
    },
  });
}, 1800000); // 300 second timeout for app launch

afterAll(async () => {
  await device.terminateApp();
});
