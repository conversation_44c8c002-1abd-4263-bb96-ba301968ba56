import {
  DateTimeHelpers,
  IEventTaskAbandoned,
  IEventTaskPostSuccess,
  SERVICES,
  TrackingActions,
  TrackingPostTaskStep,
  TrackingScreenNames,
  TrackingServices,
  TypeFormatDate,
} from '@btaskee/design-system';

import { usePostTaskStore } from '@stores';

/**
 * Office Carpet Cleaning Service Tracking Hook
 *
 * Provides comprehensive tracking functionality for office-carpet-cleaning service following
 * the multi-provider tracking architecture with provider-agnostic interface.
 *
 * Features:
 * - Screen view tracking
 * - User action tracking (back, next, changes)
 * - Task abandonment tracking
 * - Task success tracking
 * - Office carpet cleaning specific data tracking (area, space, customArea, duration)
 * - App state change tracking
 * - Navigation tracking patterns
 */
export const useTracking = () => {
  const { setStepPostTask, setIsBookedTask } = usePostTaskStore();

  /**
   * Track back action from ChooseService screen
   * Includes office carpet cleaning specific data
   */
  const trackingBackChooseService = () => {
    const currentState = usePostTaskStore.getState();
    const { duration, service, area, space, customArea } = currentState;

    TrackingServices.trackingServiceClick({
      screenName: TrackingScreenNames.DetailInformation,
      serviceName: SERVICES.OFFICE_CARPET_CLEANING,
      action: TrackingActions.Back,
      isTetBooking: service?.isTet,
      additionalInfo: {
        duration,
        area: area?.name || area?.text,
        space: space?.name || space?.text,
        customArea,
      },
    });
  };

  /**
   * Track duration change in ChooseService screen
   */
  const trackingChangeDuration = ({
    oldDuration,
    newDuration,
  }: {
    oldDuration?: number;
    newDuration: number;
  }) => {
    const currentState = usePostTaskStore.getState();
    const { service } = currentState;

    TrackingServices.trackingServiceClick({
      screenName: TrackingScreenNames.DetailInformation,
      serviceName: SERVICES.OFFICE_CARPET_CLEANING,
      action: TrackingActions.ChangeDuration,
      isTetBooking: service?.isTet,
      additionalInfo: {
        oldDuration,
        newDuration,
      },
    });
  };

  /**
   * Track area/space changes in ChooseService screen
   */
  const trackingChangeArea = (newArea: any) => {
    const currentState = usePostTaskStore.getState();
    const { service } = currentState;

    TrackingServices.trackingServiceClick({
      screenName: TrackingScreenNames.DetailInformation,
      serviceName: SERVICES.OFFICE_CARPET_CLEANING,
      action: TrackingActions.ChangeOptions,
      isTetBooking: service?.isTet,
      additionalInfo: {
        area: newArea?.name || newArea?.text,
      },
    });
  };

  /**
   * Track space changes in ChooseService screen
   */
  const trackingChangeSpace = (newSpace: any) => {
    const currentState = usePostTaskStore.getState();
    const { service } = currentState;

    TrackingServices.trackingServiceClick({
      screenName: TrackingScreenNames.DetailInformation,
      serviceName: SERVICES.OFFICE_CARPET_CLEANING,
      action: TrackingActions.ChangeOptions,
      isTetBooking: service?.isTet,
      additionalInfo: {
        space: newSpace?.name || newSpace?.text,
      },
    });
  };

  /**
   * Track next step from ChooseService screen
   */
  const trackingChooseServiceNextStep = () => {
    const currentState = usePostTaskStore.getState();
    const { service, duration, area, space, customArea } = currentState;

    TrackingServices.trackingServiceClick({
      screenName: TrackingScreenNames.DetailInformation,
      serviceName: SERVICES.OFFICE_CARPET_CLEANING,
      action: TrackingActions.Next,
      isTetBooking: service?.isTet,
      additionalInfo: {
        duration,
        area: area?.name || area?.text,
        space: space?.name || space?.text,
        customArea,
      },
    });
  };

  /**
   * Track back/next actions from ChooseDateTime screen
   */
  const trackingNextBackActionChooseDateTime = ({
    action,
  }: {
    action: TrackingActions;
  }) => {
    const currentState = usePostTaskStore.getState();
    const { service, date, address, note } = currentState;
    const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

    TrackingServices.trackingServiceClick({
      screenName: TrackingScreenNames.ChooseWorkingTime,
      serviceName: SERVICES.OFFICE_CARPET_CLEANING,
      action,
      isTetBooking: service?.isTet,
      additionalInfo: {
        workingTime: {
          date: date
            ? DateTimeHelpers.formatToString({
                timezone,
                date,
                typeFormat: TypeFormatDate.DateShort,
              })
            : null,
          time: date
            ? DateTimeHelpers.formatToString({
                timezone,
                date,
                typeFormat: TypeFormatDate.TimeHourMinute,
              })
            : null,
        },
        note,
      },
    });
  };

  /**
   * Track ChooseService screen view
   */
  const trackingChooseServiceScreenView = (entryPoint?: string) => {
    TrackingServices.trackingServiceView({
      serviceName: SERVICES.OFFICE_CARPET_CLEANING,
      screenName: TrackingScreenNames.DetailInformation,
      entryPoint: entryPoint || TrackingScreenNames.ChooseAddress,
    });
  };

  /**
   * Track ChooseDateTime screen view
   */
  const trackingChooseDateTimeScreenView = (entryPoint?: string) => {
    TrackingServices.trackingServiceView({
      screenName: TrackingScreenNames.ChooseWorkingTime,
      serviceName: SERVICES.OFFICE_CARPET_CLEANING,
      entryPoint: entryPoint || TrackingScreenNames.DetailInformation,
    });
  };

  /**
   * Track ConfirmAndPayment screen view
   */
  const trackingConfirmPaymentScreenView = () => {
    TrackingServices.trackingServiceView({
      screenName: TrackingScreenNames.ConfirmPayment,
      serviceName: SERVICES.OFFICE_CARPET_CLEANING,
      entryPoint: TrackingScreenNames.ChooseWorkingTime,
    });
  };

  /**
   * Track back/next actions from ConfirmAndPayment screen
   */
  const trackingNextBackActionConfirmPayment = ({
    action,
  }: {
    action: TrackingActions;
  }) => {
    const currentState = usePostTaskStore.getState();
    const { service, paymentMethod, address, promotion, isBookedTask } =
      currentState;

    if (isBookedTask) {
      return null;
    }

    TrackingServices.trackingServiceClick({
      screenName: TrackingScreenNames.ConfirmPayment,
      serviceName: SERVICES.OFFICE_CARPET_CLEANING,
      action,
      isTetBooking: service?.isTet,
      additionalInfo: {
        phoneNumber: address?.phoneNumber,
        contactName: address?.contact,
        paymentMethod: {
          method: paymentMethod?.name,
          promotion: promotion?.code,
        },
      },
    });
  };

  /**
   * Track task abandonment
   * Called when user exits app or navigates away
   */
  const trackingPostTaskAbandoned = async (action: TrackingActions) => {
    const currentState = usePostTaskStore.getState();
    const {
      service,
      isBookedTask,
      price,
      address,
      duration,
      date,
      promotion,
      stepPostTask,
    } = currentState;

    setStepPostTask(TrackingPostTaskStep.STEP_2);

    // If the task has been booked, the event will not be recorded
    if (isBookedTask) {
      return setIsBookedTask(false);
    }

    const params: IEventTaskAbandoned = {
      action: action,
      step: stepPostTask,
      serviceId: service?._id,
      serviceName: SERVICES.OFFICE_CARPET_CLEANING,
      price: price?.finalCost,
      duration: duration,
      address: address?.address,
      district: address?.district,
      city: address?.city,
      country: address?.country,
      date: date,
      promotionCode: promotion?.code,
      isTetBooking: service?.isTet,
    };

    TrackingServices.trackingTaskAbandoned(params);
  };

  /**
   * Track successful task posting
   */
  const trackingPostTaskSuccess = () => {
    const currentState = usePostTaskStore.getState();
    const {
      service,
      duration,
      date,
      address,
      promotion,
      price,
      area,
      space,
      customArea,
    } = currentState;

    const params: IEventTaskPostSuccess = {
      serviceId: service?._id,
      serviceName: SERVICES.OFFICE_CARPET_CLEANING,
      duration: duration,
      address: address?.address,
      district: address?.district,
      city: address?.city,
      country: address?.country,
      date: date,
      promotionCode: promotion?.code,
      taskValue: price?.finalCost,
      // Office carpet cleaning specific data
      additionalInfo: {
        area: area?.name || area?.text,
        space: space?.name || space?.text,
        customArea,
      },
    };

    TrackingServices.trackingTaskPostSuccess(params);
  };

  return {
    trackingBackChooseService,
    trackingChangeDuration,
    trackingChangeArea,
    trackingChangeSpace,
    trackingChooseServiceNextStep,
    trackingNextBackActionChooseDateTime,
    trackingChooseServiceScreenView,
    trackingChooseDateTimeScreenView,
    trackingConfirmPaymentScreenView,
    trackingNextBackActionConfirmPayment,
    trackingPostTaskAbandoned,
    trackingPostTaskSuccess,
  };
};
