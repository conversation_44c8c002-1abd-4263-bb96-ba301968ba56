import React, { memo, useEffect, useMemo, useRef } from 'react';
import { useFormContext } from 'react-hook-form';
import {
  COUNTRIES,
  EndpointKeys,
  FormTextInput,
  useApiMutation,
  useAppStore,
} from '@btaskee/design-system';
import RightIconLoading from '@src/components/RightIconLoading';
import { useI18n } from '@src/hooks';
import { debounce } from 'lodash-es';

import { RegisterFormValues } from '../../hook';

interface EmailInputInputProps {}

const EmailInputInput: React.FC<EmailInputInputProps> = () => {
  const { t } = useI18n();
  const { isoCode } = useAppStore();
  const currentCountry = useMemo(
    () => COUNTRIES.find((item) => item.isoCode === isoCode) || COUNTRIES[0],
    [isoCode],
  );
  const {
    setFocus,
    control,
    watch,
    setError,
    clearErrors,
    formState: { errors, validatingFields },
  } = useFormContext<RegisterFormValues>();
  const { mutate: validateEmailMutate, isPending } = useApiMutation({
    key: EndpointKeys.validateEmail,
    options: {
      onSuccess: () => {
        clearErrors('email');
      },
      onError: (error) => {
        let message = t('MESSAGE_EMAIL_INVALID');
        if (error?.data?.code === 'EMAIL_USED') {
          message = t('NUMBER_PHONE_EXITS');
        }
        setError('email', {
          type: 'manual',
          message,
        });
      },
      retry: false,
    },
  });

  const email = watch('email');
  const handler = useRef(
    debounce((value: string, error?: string) => {
      if (error || !value) return;
      validateEmailMutate({
        email: value,
        countryCode: currentCountry.countryCode,
        isoCode,
      });
    }, 300),
  ).current;

  useEffect(() => {
    handler(email, errors.email?.message);
  }, [errors.email?.message, handler, email]);
  return (
    <FormTextInput
      control={control}
      name="email"
      label={t('EMAIL')}
      returnKeyType="next"
      enableClear
      variant="secondary"
      placeholder={t('PLACEHOLDER_EMAIL')}
      maxLength={40}
      onSubmitEditing={() => setFocus('password')}
      rightIcon={
        email ? (
          <RightIconLoading
            isLoading={isPending || !!validatingFields.email}
            isError={
              !!errors.email?.message &&
              !(isPending || !!validatingFields.email)
            }
          />
        ) : null
      }
    />
  );
};

export default memo(EmailInputInput);
