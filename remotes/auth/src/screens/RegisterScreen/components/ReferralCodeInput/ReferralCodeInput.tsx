import React, { memo, useEffect, useRef } from 'react';
import { useFormContext } from 'react-hook-form';
import {
  EndpointKeys,
  FormTextInput,
  useApiMutation,
} from '@btaskee/design-system';
import RightIconLoading from '@src/components/RightIconLoading';
import { useI18n } from '@src/hooks';
import { debounce } from 'lodash-es';

import { RegisterFormValues } from '../../hook';

interface ReferralCodeInputProps {}

const ReferralCodeInput: React.FC<ReferralCodeInputProps> = () => {
  const { t } = useI18n();
  const {
    control,
    watch,
    setError,
    clearErrors,
    formState: { errors, validatingFields },
  } = useFormContext<RegisterFormValues>();
  const { mutate: validateFriendCodeMutate, isPending } = useApiMutation({
    key: EndpointKeys.validateFriendCode,
    options: {
      onSuccess: () => {
        clearErrors('referralCode');
      },
      onError: () => {
        setError('referralCode', {
          type: 'manual',
          message: t('REFERRAL_CODE_INVALID'),
        });
      },
      retry: false,
    },
  });

  const referralCode = watch('referralCode');

  const handler = useRef(
    debounce((friendCode: string, error?: string) => {
      if (error || !friendCode) return;
      validateFriendCodeMutate({
        friendCode,
      });
    }, 300),
  ).current;

  useEffect(() => {
    handler(referralCode, errors.phoneNumber?.message);
  }, [errors.phoneNumber?.message, handler, referralCode]);

  return (
    <FormTextInput
      control={control}
      name="referralCode"
      label={t('REFERRAL_CODE')}
      enableClear
      variant="secondary"
      placeholder={t('PLACEHOLDER_REFERRAL_CODE')}
      maxLength={20}
      rightIcon={
        referralCode ? (
          <RightIconLoading
            isLoading={isPending || !!validatingFields.referralCode}
            isError={!!errors.referralCode?.message}
          />
        ) : null
      }
    />
  );
};

export default memo(ReferralCodeInput);
