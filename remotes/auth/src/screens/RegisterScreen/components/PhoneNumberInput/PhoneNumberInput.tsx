import React, { memo, useCallback, useEffect, useMemo, useRef } from 'react';
import { useFormContext } from 'react-hook-form';
import {
  AuthRouteName,
  COUNTRIES,
  EndpointKeys,
  FormTextInput,
  getPhoneNumber,
  useApiMutation,
  useAppStore,
  useAuth,
} from '@btaskee/design-system';
import CountryButton from '@src/components/CountryButton';
import RightIconLoading from '@src/components/RightIconLoading';
import { useI18n } from '@src/hooks';
import { debounce } from 'lodash-es';

import { RegisterFormValues } from '../../hook';

interface PhoneNumberInputProps {}

const PhoneNumberInput: React.FC<PhoneNumberInputProps> = () => {
  const { t } = useI18n();
  const { isoCode } = useAppStore();
  const { setTempAuth } = useAuth();
  const currentCountry = useMemo(
    () => COUNTRIES.find((item) => item.isoCode === isoCode) || COUNTRIES[0],
    [isoCode],
  );
  const {
    setFocus,
    control,
    watch,
    setError,
    clearErrors,
    getValues,
    formState: { errors, validatingFields },
  } = useFormContext<RegisterFormValues>();
  const { mutate: validatePhoneMutate, isPending } = useApiMutation({
    key: EndpointKeys.validatePhone,

    options: {
      onSuccess: () => {
        clearErrors('phoneNumber');
      },
      onError: (error) => {
        let message = t('PHONE_NUMBER_SYNTAX_IS_INCORRECT');
        if (error?.data?.code === 'PHONE_USED') {
          message = t('NUMBER_PHONE_EXITS');
        }
        setError('phoneNumber', {
          type: 'manual',
          message,
        });
      },
      retry: false,
    },
  });

  const phoneNumber = watch('phoneNumber');

  const handler = useRef(
    debounce((phone: string, error?: string) => {
      if (error || !phone) return;
      validatePhoneMutate({
        phone: getPhoneNumber(
          getPhoneNumber(phone, currentCountry.countryCode),
          currentCountry.countryCode,
        ),
        countryCode: currentCountry.countryCode,
        isoCode,
      });
    }, 300),
  ).current;

  useEffect(() => {
    handler(phoneNumber, errors.phoneNumber?.message);
  }, [errors.phoneNumber?.message, handler, phoneNumber]);

  const onChangeCountrySuccess = useCallback(() => {
    setTempAuth({
      routeName: AuthRouteName.Register,
      params: getValues(),
    });
  }, [getValues, setTempAuth]);

  return (
    <FormTextInput
      control={control}
      name="phoneNumber"
      label={t('PHONE_NUMBER')}
      placeholder={t('INPUT_PHONE_NUMBER')}
      returnKeyType="next"
      keyboardType="phone-pad"
      maxLength={12}
      variant="secondary"
      onSubmitEditing={() => setFocus('email')}
      leftIcon={
        <CountryButton onChangeCountrySuccess={onChangeCountrySuccess} />
      }
      rightIcon={
        phoneNumber ? (
          <RightIconLoading
            isLoading={isPending || !!validatingFields.phoneNumber}
            isError={!!errors.phoneNumber?.message}
          />
        ) : null
      }
    />
  );
};

export default memo(PhoneNumberInput);
