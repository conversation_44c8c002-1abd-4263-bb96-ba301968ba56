import { useCallback, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import {
  AuthRouteName,
  AuthStackScreenProps,
  COUNTRIES,
  OryKratosApi,
  OryUtils,
  refactorPhoneNumber,
  useAppLoading,
  useAppStore,
  useMutation,
  useQuery,
  validEmail,
  validPhoneNumber,
} from '@btaskee/design-system';
import { yupResolver } from '@hookform/resolvers/yup';
import { useAppNavigation, useI18n } from '@src/hooks';
import { find } from 'lodash-es';
import * as yup from 'yup';

export type RegisterScreenProps = AuthStackScreenProps<AuthRouteName.Register>;

export type RegisterFormValues = {
  phoneNumber: string;
  name: string;
  email: string;
  referralCode: string;
  password: string;
};

const useRegisterScreen = ({ route }: RegisterScreenProps) => {
  const { t } = useI18n();
  const navigation = useAppNavigation();
  const { isoCode } = useAppStore();
  const [secureEntry, setSecureEntry] = useState(true);
  const { showAppLoading, hideAppLoading } = useAppLoading();
  const {
    email = '',
    name = '',
    password = '',
    phoneNumber = '',
  } = route.params || {};

  const currentCountry = useMemo(
    () => COUNTRIES.find((item) => item.isoCode === isoCode) || COUNTRIES[0],
    [isoCode],
  );
  const registerSchema = useMemo(
    () =>
      yup.object().shape({
        phoneNumber: yup
          .string()
          .trim()
          .required(t('THIS_FIELD_IS_REQUIRED'))
          .test(
            'valid-phone',
            t('PHONE_NUMBER_SYNTAX_IS_INCORRECT'),
            function (value) {
              const context = this.options.context;
              const countryCode = context?.countryCode;
              if (!value) return false;
              const valid = validPhoneNumber(value, countryCode);
              if (valid) return true;
              return false;
            },
          ),
        name: yup.string().trim().required(t('THIS_FIELD_IS_REQUIRED')),
        email: yup
          .string()
          .trim()
          .required(t('THIS_FIELD_IS_REQUIRED'))
          .test('valid-email', t('MESSAGE_EMAIL_INVALID'), function (value) {
            if (!value) return false;
            return validEmail(value);
          }),
        referralCode: yup.string().trim().default(''),
        password: yup
          .string()
          .trim()
          .required(t('THIS_FIELD_IS_REQUIRED'))
          .min(8, t('PASSWORD_MUST_BE_AT_LEAST_8_CHARACTERS')),
      }),
    [t],
  );
  const methods = useForm({
    mode: 'onChange',
    defaultValues: {
      phoneNumber: phoneNumber,
      name: name,
      email: email,
      password: password,
      referralCode: route.params?.referralCode || '',
    },
    resolver: yupResolver(registerSchema),
    context: {
      countryCode: currentCountry.countryCode,
    },
  });
  const { control, handleSubmit, setFocus } = methods;

  const { data: flowData } = useQuery({
    queryKey: ['getFlowRegister'],
    queryFn: OryKratosApi.getFlowRegister,
    staleTime: 1 * 60 * 1000,
  });

  const { mutate: register, isPending } = useMutation({
    mutationFn: OryKratosApi.register,
    onMutate: showAppLoading,
    onSettled: hideAppLoading,
    onSuccess: (data, variables) => {
      const verification = find(data.continue_with, {
        action: 'show_verification_ui',
      });
      navigation.navigate(AuthRouteName.OTP, {
        flowId: verification?.flow?.id!,
        phoneNumber: variables.phoneNumber,
        session:
          data.session_token || OryUtils.getSessionToken(data.continue_with)!,
        userName: verification?.flow?.verifiable_address!,
        verifyFor: 'register',
      });
    },
    onError: OryUtils.onError,
  });

  const onSubmit = useCallback(() => {
    handleSubmit((data) => {
      register({
        flowId: flowData?.id!,
        ...data,
        phoneNumber: refactorPhoneNumber(
          data.phoneNumber,
          currentCountry.countryCode,
        ),
      });
    })();
  }, [currentCountry.countryCode, flowData?.id, handleSubmit, register]);

  return {
    t,
    control,
    methods,
    navigation,
    secureEntry,
    disabled: isPending,
    onSubmit,
    setFocus,
    setSecureEntry,
  };
};

export default useRegisterScreen;
