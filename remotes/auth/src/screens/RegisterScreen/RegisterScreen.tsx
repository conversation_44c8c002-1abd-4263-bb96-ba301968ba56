import React, { memo } from 'react';
import { FormProvider } from 'react-hook-form';
import {
  BlockView,
  CKeyboardStickyView,
  Colors,
  CText,
  FontSizes,
  FormTextInput,
  IconAssets,
  IconImage,
  KeyboardAware,
  PrimaryButton,
  Spacing,
  TouchableOpacity,
} from '@btaskee/design-system';
import { debounce } from 'lodash-es';

import EmailInput from './components/EmailInput';
import PhoneNumberInput from './components/PhoneNumberInput';
import ReferralCodeInput from './components/ReferralCodeInput';
import useRegisterScreen, { RegisterScreenProps } from './hook';
import styles from './styles';

const RegisterScreen: React.FC<RegisterScreenProps> = (props) => {
  const {
    t,
    control,
    disabled,
    methods,
    secureEntry,
    onSubmit,
    setFocus,
    setSecureEntry,
  } = useRegisterScreen(props);

  return (
    <BlockView
      flex
      jBetween
    >
      <KeyboardAware
        showsVerticalScrollIndicator={false}
        bottomOffset={100}
      >
        <FormProvider {...methods}>
          <BlockView padding={Spacing.SPACE_16}>
            <CText
              bold
              size={FontSizes.SIZE_18}
            >
              {t('CREATE_ACCOUNT_TITLE')}
            </CText>
            <CText style={styles.subtitle}>{t('CREATE_ACCOUNT_DES')}</CText>
            <FormTextInput
              control={control}
              name="name"
              label={t('NAME')}
              variant="secondary"
              placeholder={t('PLACEHOLDER_NAME')}
              returnKeyType="next"
              enableClear
              maxLength={40}
              onSubmitEditing={() => setFocus('phoneNumber')}
            />
            <PhoneNumberInput />
            <EmailInput />
            <FormTextInput
              control={control}
              name="password"
              label={t('PASSWORD')}
              variant="secondary"
              enableClear
              returnKeyType="next"
              placeholder={t('INPUT_PASSWORD')}
              secureTextEntry={secureEntry}
              onSubmitEditing={() => setFocus('referralCode')}
              rightIcon={
                <TouchableOpacity
                  onPress={() => setSecureEntry((prev) => !prev)}
                >
                  <IconImage
                    source={
                      secureEntry ? IconAssets.icEye : IconAssets.icEyeSlash
                    }
                    size={24}
                    color={Colors.neutral200}
                  />
                </TouchableOpacity>
              }
            />
            <ReferralCodeInput />
          </BlockView>
        </FormProvider>
      </KeyboardAware>

      <CKeyboardStickyView>
        <PrimaryButton
          title={t('REGISTER')}
          onPress={debounce(onSubmit, 300)}
          disabled={disabled}
        />
      </CKeyboardStickyView>
    </BlockView>
  );
};

export default memo(RegisterScreen);
