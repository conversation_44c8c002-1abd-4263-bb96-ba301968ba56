import React, { memo } from 'react';
import { OtpInput } from 'react-native-otp-entry';
import {
  BlockView,
  Colors,
  ConditionView,
  CountDown,
  CText,
  KeyboardAware,
  Spacing,
  TouchableOpacity,
} from '@btaskee/design-system';
import { debounce } from 'lodash-es';

import useOTPScreen, { OTPScreenProps } from './hook';
import styles from './styles';

const OTPScreen: React.FC<OTPScreenProps> = (props) => {
  const {
    t,
    phoneNumber,
    diffSecond,
    otpRef,
    errorMessage,
    disabledResend,
    onResendOTP,
    submitOTP,
    setDiffSecond,
    setErrorMessage,
  } = useOTPScreen(props);

  return (
    <KeyboardAware>
      <BlockView
        padding={Spacing.SPACE_16}
        margin={{ top: Spacing.SPACE_60 }}
        gap={Spacing.SPACE_20}
      >
        <BlockView>
          <CText color={Colors.neutral200}>{t('OTP_SCREEN_DES')}</CText>
          <CText
            color={Colors.green500}
            semiBold
          >
            {phoneNumber}
          </CText>
        </BlockView>
        <OtpInput
          numberOfDigits={6}
          onTextChange={() => setErrorMessage('')}
          ref={otpRef}
          onFilled={debounce(submitOTP, 300)}
          theme={{
            pinCodeContainerStyle: {
              ...styles.otpBox,
              borderColor: errorMessage ? Colors.red500 : Colors.neutral100,
            },
            focusedPinCodeContainerStyle: {
              borderColor: errorMessage ? Colors.red500 : Colors.green500,
            },
            focusStickStyle: {
              backgroundColor: errorMessage ? Colors.red500 : Colors.green500,
            },
            pinCodeTextStyle: styles.otpText,
          }}
        />
        <ConditionView
          condition={!!errorMessage}
          viewTrue={
            <CText
              color={Colors.red500}
              center
            >
              {errorMessage}
            </CText>
          }
        />
        <BlockView center>
          {!diffSecond ? (
            <BlockView
              center
              gap={Spacing.SPACE_12}
            >
              <CText color={Colors.neutral400}>{t('NOT_RECEIVED_OTP')}</CText>
              <TouchableOpacity
                disabled={disabledResend}
                onPress={debounce(onResendOTP, 300)}
              >
                <CText
                  color={Colors.green500}
                  semiBold
                >
                  {t('RESEND_OTP')}
                </CText>
              </TouchableOpacity>
            </BlockView>
          ) : (
            <CountDown
              until={diffSecond}
              digitStyle={styles.digit}
              digitTxtStyle={styles.digitTxt}
              onFinish={() => {
                setTimeout(() => {
                  setDiffSecond(0);
                }, 1000);
              }}
              timeToShow={['M', 'S']}
              timeLabels={{
                d: undefined,
                h: undefined,
                m: undefined,
                s: undefined,
              }}
              showSeparator
              separatorStyle={{ color: Colors.neutral800 }}
              timeToHiddenWhenEqualToZero={['D', 'H']}
            />
          )}
        </BlockView>
      </BlockView>
    </KeyboardAware>
  );
};

export default memo(OTPScreen);
