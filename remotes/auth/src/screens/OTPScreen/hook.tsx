import React, { useCallback, useMemo, useRef, useState } from 'react';
import { StyleSheet } from 'react-native';
import Markdown from 'react-native-markdown-display';
import { OtpInputRef } from 'react-native-otp-entry';
import {
  Alert,
  AuthRouteName,
  AuthStackScreenProps,
  Colors,
  FontFamily,
  FontSizes,
  OryKratosApi,
  OryUtils,
  ToastHelpers,
  useAppLoading,
  useAuth,
  useMutation,
} from '@btaskee/design-system';
import { useI18n } from '@src/hooks';
import { find } from 'lodash-es';

export type OTPScreenProps = AuthStackScreenProps<AuthRouteName.OTP>;

const TIME_TO_RESEND = 30;

const useOTPScreen = ({ navigation, route }: OTPScreenProps) => {
  const { t } = useI18n();
  // session only have when verifyFor is 'register'
  const { flowId, phoneNumber, session, userName, verifyFor } =
    route.params || {};
  const { hideAppLoading, showAppLoading } = useAppLoading();
  const otpRef = useRef<OtpInputRef>(null);
  const { login } = useAuth();
  const [diffSecond, setDiffSecond] = useState(TIME_TO_RESEND);

  const [errorMessage, setErrorMessage] = useState<string>('');

  const onError = useCallback(
    (error: any) => {
      otpRef.current?.clear();
      if (error.code === 'NHAP SAI QUA NHIEU LAN') {
        Alert.alert.open({
          title: t('DIALOG_TITLE_INFORMATION'),
          message: (
            <Markdown style={markdownStyles}>
              {OryUtils.getMessageError(error)}
            </Markdown>
          ),
          actions: [{ text: t('CLOSE') }],
        });
      } else {
        setErrorMessage(OryUtils.getMessageError(error));
      }
    },
    [t],
  );

  // START ACTION FOR TYPE 'register'
  const { mutate: getWhoAmIMutate } = useMutation({
    mutationFn: OryKratosApi.getWhoAmI,
    onSuccess: async (response, variables) => {
      await login({
        token: response?.tokenized!,
        session_token: variables.token,
      });
    },
    onSettled: hideAppLoading,
  });

  const { mutate: verificationMutate } = useMutation({
    mutationFn: OryKratosApi.verification,
    onMutate: showAppLoading,
    onSuccess: async () => {
      getWhoAmIMutate({ token: session! });
    },
    onError: (error) => {
      onError(error);
      hideAppLoading();
    },
  });

  const {
    mutate: getFlowResendVerificationCodeMutate,
    isPending: isResendOtpRegisterFlowPending,
  } = useMutation({
    mutationFn: OryKratosApi.getFlowResendVerificationCode,
    onSettled: hideAppLoading,
    onSuccess: (response) => {
      navigation.setParams({ flowId: response.id! });
      resendVerificationCodeMutate({ flowId: response.id!, userName });
    },
    onError: OryUtils.onError,
  });

  const {
    mutate: resendVerificationCodeMutate,
    isPending: isResendOtpRegiserPending,
  } = useMutation({
    mutationFn: OryKratosApi.resendVerificationCode,
    onMutate: showAppLoading,
    onSettled: hideAppLoading,
    onError: (error) => {
      Alert.alert.open({
        title: t('DIALOG_TITLE_INFORMATION'),
        message: (
          <Markdown style={markdownStyles}>
            {OryUtils.getMessageError(error)}
          </Markdown>
        ),
        actions: [{ text: t('CLOSE') }],
      });
    },
    onSuccess: () => {
      setDiffSecond(TIME_TO_RESEND);
      ToastHelpers.showSuccess({
        position: 'top',
        message: t('OTP_SENT_SUCCESSFULLY'),
      });
    },
  });

  // END ACTION FOR TYPE 'register'

  //START ACTION FOR TYPE 'forgot_password'
  const { mutate: verifyRecoveryCodeMutate } = useMutation({
    mutationFn: OryKratosApi.verifyRecoveryCode,
    onMutate: showAppLoading,
    onSuccess: async (response) => {
      const setting_ui = find(response.continue_with, {
        action: 'show_settings_ui',
      });
      navigation.replace(AuthRouteName.InputPassword, {
        flowId: setting_ui?.flow?.id!,
        session:
          response.session_token ||
          OryUtils.getSessionToken(response.continue_with)!,
      });
    },
    onSettled: hideAppLoading,
    onError,
  });
  const {
    mutate: getFlowForgotPasswordMutate,
    isPending: isResendOtpForgotPasswordFlowPending,
  } = useMutation({
    mutationFn: OryKratosApi.getFlowForgotPassword,
    onSettled: hideAppLoading,
    onSuccess: (response) => {
      sendRecoveryCodeMutate({ flowId: response.id!, userName });
    },
    onError: OryUtils.onError,
  });

  const {
    mutate: sendRecoveryCodeMutate,
    isPending: isResendOtpForgotPasswordPending,
  } = useMutation({
    mutationFn: OryKratosApi.sendRecoveryCode,
    onMutate: showAppLoading,
    onSuccess: () => {
      ToastHelpers.showSuccess({
        position: 'top',
        message: t('OTP_SENT_SUCCESSFULLY'),
      });
      setDiffSecond(TIME_TO_RESEND);
    },
    onError: (error) => {
      Alert.alert.open({
        title: t('DIALOG_TITLE_INFORMATION'),
        message: (
          <Markdown style={markdownStyles}>
            {OryUtils.getMessageError(error)}
          </Markdown>
        ),
        actions: [{ text: t('CLOSE') }],
      });
    },
  });
  // END ACTION FOR TYPE 'forgot_password'

  const submitOTP = useCallback(
    (otp: string) => {
      if (!otp || otp.length < 6) return;
      if (verifyFor === 'forgot_password') {
        verifyRecoveryCodeMutate({ code: otp, flowId });
      } else if (verifyFor === 'register') {
        verificationMutate({ code: otp, flowId });
      }
    },
    [verifyFor, verifyRecoveryCodeMutate, flowId, verificationMutate],
  );

  const onResendOTP = useCallback(() => {
    if (verifyFor === 'forgot_password') {
      getFlowForgotPasswordMutate();
    } else if (verifyFor === 'register') {
      getFlowResendVerificationCodeMutate();
    }
  }, [
    getFlowForgotPasswordMutate,
    getFlowResendVerificationCodeMutate,
    verifyFor,
  ]);

  const disabledResend = useMemo(() => {
    return (
      isResendOtpForgotPasswordFlowPending ||
      isResendOtpRegisterFlowPending ||
      isResendOtpForgotPasswordPending ||
      isResendOtpRegiserPending
    );
  }, [
    isResendOtpForgotPasswordFlowPending,
    isResendOtpForgotPasswordPending,
    isResendOtpRegiserPending,
    isResendOtpRegisterFlowPending,
  ]);

  return {
    t,
    otpRef,
    navigation,
    diffSecond,
    phoneNumber,
    errorMessage,
    disabledResend,
    submitOTP,
    onResendOTP,
    setDiffSecond,
    setErrorMessage,
  };
};

const markdownStyles = StyleSheet.create({
  body: {
    color: Colors.neutral900,
    fontSize: FontSizes.SIZE_14,
    fontFamily: FontFamily.medium,
    lineHeight: 20,
    textAlign: 'center',
  },
  link: {
    color: Colors.orange500,
    textDecorationLine: 'none',
  },
});

export default useOTPScreen;
