import { useCallback, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import {
  AuthRouteName,
  AuthStackScreenProps,
  COUNTRIES,
  OryKratosApi,
  OryUtils,
  useAppLoading,
  useAppStore,
  useAuth,
  useMutation,
  useQuery,
  validPhoneNumber,
} from '@btaskee/design-system';
import { yupResolver } from '@hookform/resolvers/yup';
import { useI18n } from '@src/hooks';
import * as yup from 'yup';

export type SocialUpdateProfileScreenProps =
  AuthStackScreenProps<AuthRouteName.SocialUpdateProfile>;

const useSocialUpdateProfileScreen = ({
  navigation,
  route,
}: SocialUpdateProfileScreenProps) => {
  const { t } = useI18n();
  const { isoCode } = useAppStore();
  const { showAppLoading, hideAppLoading } = useAppLoading();
  const { login } = useAuth();
  const { session_token, name } = route.params;

  const currentCountry = useMemo(
    () => COUNTRIES.find((item) => item.isoCode === isoCode) || COUNTRIES[0],
    [isoCode],
  );

  const schema = useMemo(
    () =>
      yup.object().shape({
        phoneNumber: yup
          .string()
          .trim()
          .required(t('THIS_FIELD_IS_REQUIRED'))
          .test('valid-phone', t('INVALID_PHONE'), function (value) {
            const context = this.options.context;
            const countryCode = context?.countryCode;
            if (!value) return false;

            const valid = validPhoneNumber(value, countryCode);
            if (valid) return true;
            return this.createError({
              path: 'phoneNumber',
              message: t('INVALID_PHONE_FOR_COUNTRY'),
            });
          }),
        referralCode: yup.string().trim().default(''),
      }),
    [t],
  );

  const { control, handleSubmit, setFocus } = useForm({
    mode: 'onSubmit',
    defaultValues: {
      phoneNumber: '',
      referralCode: '',
    },
    resolver: yupResolver(schema),
    context: {
      countryCode: currentCountry.countryCode,
    },
  });

  const { mutate: getWhoAmIMutate } = useMutation({
    mutationFn: OryKratosApi.getWhoAmI,
    onSettled: hideAppLoading,
    onSuccess: async (response, variables) => {
      await login({
        token: response?.tokenized!,
        session_token: variables.token,
      });
    },
  });

  const { data: updateProfileFlow } = useQuery({
    queryKey: ['getUpdateProfileFlow', session_token],
    queryFn: () => OryKratosApi.getUpdateProfileFlow(session_token),
  });

  const { mutate: updateProfileMutate } = useMutation({
    mutationFn: OryKratosApi.updateProfile,
    onSuccess: async () => {
      getWhoAmIMutate({ token: session_token });
    },
    onMutate: showAppLoading,
    onError: OryUtils.onError,
  });

  const onSubmit = useCallback(() => {
    handleSubmit((data) => {
      updateProfileMutate({ ...data, name, flowId: updateProfileFlow!.id! });
    })();
  }, [handleSubmit, name, updateProfileFlow, updateProfileMutate]);

  return { navigation, t, onSubmit, setFocus, control, name };
};

export default useSocialUpdateProfileScreen;
