import React, { memo } from 'react';
import {
  Avatar,
  BlockView,
  CKeyboardStickyView,
  CText,
  <PERSON>ceHelper,
  FormTextInput,
  KeyboardAware,
  PrimaryButton,
  Spacing,
} from '@btaskee/design-system';
import CountryButton from '@src/components/CountryButton';
import { debounce } from 'lodash-es';

import useSocialUpdateProfileScreen, {
  SocialUpdateProfileScreenProps,
} from './hook';

const SocialUpdateProfileScreen: React.FC<SocialUpdateProfileScreenProps> = (
  props,
) => {
  const { t, control, name, setFocus, onSubmit } =
    useSocialUpdateProfileScreen(props);

  return (
    <BlockView
      flex
      jBetween
    >
      <KeyboardAware
        showsVerticalScrollIndicator={false}
        bottomOffset={100}
      >
        <BlockView
          padding={Spacing.SPACE_16}
          gap={Spacing.SPACE_24}
        >
          <BlockView center>
            <Avatar size={120 * DeviceHelper.WIDTH_RATIO} />
            <CText bold>{name}</CText>
          </BlockView>

          <BlockView>
            <FormTextInput
              control={control}
              name="phoneNumber"
              label={t('PHONE_NUMBER_DESCRIPTION')}
              placeholder={t('INPUT_PHONE_NUMBER')}
              returnKeyType="next"
              keyboardType="phone-pad"
              maxLength={12}
              variant="secondary"
              onSubmitEditing={() => setFocus('referralCode')}
              leftIcon={<CountryButton />}
            />
            <FormTextInput
              control={control}
              name="referralCode"
              label={t('REFERRAL_CODE')}
              enableClear
              variant="secondary"
              placeholder={t('PLACEHOLDER_REFERRAL_CODE')}
              maxLength={20}
            />
          </BlockView>
        </BlockView>
      </KeyboardAware>

      <CKeyboardStickyView>
        <PrimaryButton
          title={t('NEXT')}
          onPress={debounce(onSubmit, 300)}
        />
      </CKeyboardStickyView>
    </BlockView>
  );
};

export default memo(SocialUpdateProfileScreen);
