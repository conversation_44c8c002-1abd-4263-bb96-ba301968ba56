import { useCallback, useRef, useState } from 'react';
import { Keyboard } from 'react-native';
import { BottomModalHandle, COUNTRIES } from '@btaskee/design-system';
import { useI18n } from '@src/hooks';

export interface UseSelectCountryProps {
  country: (typeof COUNTRIES)[0];
  onChangeCountry: (country: (typeof COUNTRIES)[0]) => void;
}

const useSelectCountry = ({
  onChangeCountry,
  country,
}: UseSelectCountryProps) => {
  const { t } = useI18n();
  const bottomSheetRef = useRef<BottomModalHandle>(null);

  const [currentCountry, setCurrentCountry] =
    useState<(typeof COUNTRIES)[0]>(country);

  const onOpenBottomSheet = useCallback(() => {
    bottomSheetRef.current?.present?.();
    Keyboard.dismiss();
  }, []);

  const onApply = useCallback(() => {
    bottomSheetRef.current?.dismiss?.();
    onChangeCountry(currentCountry);
  }, [currentCountry, onChangeCountry]);

  const onClose = useCallback(() => {
    bottomSheetRef.current?.dismiss?.();
  }, []);

  return {
    t,
    currentCountry,
    bottomSheetRef,
    onApply,
    onClose,
    onOpenBottomSheet,
    setCurrentCountry,
  };
};

export default useSelectCountry;
