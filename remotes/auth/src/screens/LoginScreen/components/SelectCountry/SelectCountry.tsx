import React, { memo } from 'react';
import {
  BlockView,
  BorderRadius,
  BottomModal,
  Colors,
  COUNTRIES,
  CText,
  FastImage,
  FontSizes,
  IconAssets,
  IconImage,
  PrimaryButton,
  Radio,
  Spacing,
  TouchableOpacity,
} from '@btaskee/design-system';

import useSelectCountry, { UseSelectCountryProps } from './hook';
import styles from './styles';

interface SelectCountryProps extends UseSelectCountryProps {}

const SelectCountry = memo(
  ({ country, onChangeCountry }: SelectCountryProps) => {
    const {
      t,
      bottomSheetRef,
      currentCountry,
      setCurrentCountry,
      onApply,
      onClose,
      onOpenBottomSheet,
    } = useSelectCountry({ onChangeCountry, country });

    return (
      <BlockView>
        <TouchableOpacity
          row
          horizontal
          gap={Spacing.SPACE_04}
          margin={{ right: Spacing.SPACE_16 }}
          onPress={onOpenBottomSheet}
        >
          <FastImage
            source={country.flag}
            style={{
              width: Spacing.SPACE_24,
              height: Spacing.SPACE_16,
              borderRadius: BorderRadius.RADIUS_04,
            }}
          />
          <CText>{country.countryCode}</CText>
          <IconImage
            source={IconAssets.icChevronCompactDown}
            size={16}
            color={Colors.neutral200}
          />
        </TouchableOpacity>

        <BottomModal
          modalRef={bottomSheetRef}
          backgroundContainer={Colors.neutralBackground}
        >
          <BlockView
            padding={{
              horizontal: Spacing.SPACE_16,
              bottom: Spacing.SPACE_16,
            }}
          >
            <CText
              size={FontSizes.SIZE_20}
              bold
              color={Colors.neutral700}
              center
            >
              {t('COUNTRY_TERRITORY')}
            </CText>
            <CText
              color={Colors.neutral500}
              margin={{ top: Spacing.SPACE_08 }}
              center
            >
              {t('SELECT_COUNTRY_REGION')}
            </CText>
            <BlockView
              center
              gap={Spacing.SPACE_16}
              padding={Spacing.SPACE_16}
              backgroundColor={Colors.neutralWhite}
              radius={BorderRadius.RADIUS_08}
              margin={{ top: Spacing.SPACE_16 }}
            >
              {COUNTRIES.map((item, index) => (
                <TouchableOpacity
                  onPress={() => setCurrentCountry(item)}
                  key={item.isoCode}
                  row
                  horizontal
                  jBetween
                  border={{
                    top: {
                      width: index > 0 ? 1 : 0,
                      color: Colors.neutral50,
                    },
                  }}
                  padding={{ top: index > 0 ? Spacing.SPACE_16 : 0 }}
                >
                  <BlockView
                    row
                    horizontal
                    gap={Spacing.SPACE_12}
                    flex
                  >
                    <FastImage
                      source={item.flag}
                      style={styles.flagIconModal}
                    />
                    <CText
                      flex
                      color={Colors.neutral500}
                    >
                      {t(item.name)}
                    </CText>
                  </BlockView>
                  <Radio
                    checked={item.countryCode === currentCountry.countryCode}
                  />
                </TouchableOpacity>
              ))}
            </BlockView>
            <BlockView
              row
              horizontal
              jBetween
              margin={{ top: Spacing.SPACE_24 }}
              gap={Spacing.SPACE_16}
            >
              <PrimaryButton
                style={styles.button}
                type="secondary"
                title={t('CLOSE')}
                onPress={onClose}
              />
              <PrimaryButton
                style={styles.button}
                title={t('CONFIRM')}
                onPress={onApply}
              />
            </BlockView>
          </BlockView>
        </BottomModal>
      </BlockView>
    );
  },
);
export default SelectCountry;
