import { useCallback, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import {
  Alert,
  AuthRouteName,
  AuthStackScreenProps,
  COUNTRIES,
  OryKratosApi,
  OryUtils,
  refactorPhoneNumber,
  useAppLoading,
  useAppStore,
  useAuth,
  useMutation,
  useQuery,
  validPhoneNumber,
} from '@btaskee/design-system';
import { yupResolver } from '@hookform/resolvers/yup';
import { useI18n } from '@src/hooks';
import * as yup from 'yup';

export type LoginScreenProps = AuthStackScreenProps<AuthRouteName.Login>;

const useLoginScreen = ({ route, navigation }: LoginScreenProps) => {
  const { t } = useI18n();
  const [secureEntry, setSecureEntry] = useState(true);
  const { login } = useAuth();
  const { showAppLoading, hideAppLoading } = useAppLoading();

  const { isoCode } = useAppStore();
  const { phoneNumber = '', password = '' } = route.params || {};
  const [currentCountry, setCurrentCountry] = useState(
    COUNTRIES.find((item) => item.isoCode === isoCode) || COUNTRIES[0],
  );

  const loginSchema = useMemo(
    () =>
      yup.object().shape({
        phoneNumber: yup
          .string()
          .trim()
          .required(t('THIS_FIELD_IS_REQUIRED'))
          .test('valid-phone', t('INVALID_PHONE'), function (value) {
            const context = this.options.context;
            const countryCode = context?.countryCode;
            if (!value) return false;

            const valid = validPhoneNumber(value, countryCode);
            if (valid) return true;
            return this.createError({
              path: 'phoneNumber',
              message: t('INVALID_PHONE_FOR_COUNTRY'),
            });
          }),
        password: yup.string().trim().required(t('THIS_FIELD_IS_REQUIRED')),
      }),
    [t],
  );

  const { data: loginFlow } = useQuery({
    queryKey: ['getLoginFlow'],
    queryFn: OryKratosApi.getLoginFlow,
    staleTime: 1 * 60 * 1000,
  });

  const { control, handleSubmit, setFocus } = useForm({
    mode: 'onSubmit',
    defaultValues: {
      phoneNumber: phoneNumber,
      password: password,
    },
    resolver: yupResolver(loginSchema),
    context: {
      countryCode: currentCountry.countryCode,
    },
  });

  const { mutate: getWhoAmIMutate } = useMutation({
    mutationFn: OryKratosApi.getWhoAmI,
    onSettled: hideAppLoading,
    onSuccess: async (response, variables) => {
      await login({
        token: response?.tokenized!,
        session_token: variables.token,
      });
    },
  });

  const { isPending, mutate } = useMutation({
    mutationFn: OryKratosApi.submitLoginFlow,
    onMutate: showAppLoading,
    onSuccess: async (response) => {
      getWhoAmIMutate({
        token:
          OryUtils.getSessionToken(response?.continue_with) ||
          response.session_token,
      });
    },
    onError: (error: any) => {
      if (error?.status === 'INACTIVE') {
        Alert.alert.open({
          title: t('DIALOG_TITLE_INFORMATION'),
          message: t('LOGIN_MESSAGE_ACCOUNT_ACTIVE'),
          actions: [
            { text: t('ACCOUNT_SUPPORT_ACTION'), style: 'cancel' },
            {
              text: t('ACCOUNT_ACTIVATION_BUTTON_ACTIVATION'),
            },
          ],
        });
      } else {
        OryUtils.onError(error);
      }
    },
  });
  const onSubmit = useCallback(() => {
    handleSubmit((params) =>
      mutate({
        identifier: refactorPhoneNumber(
          params.phoneNumber,
          currentCountry.countryCode,
        ),
        password: params.password,
        flowId: loginFlow?.id!,
      }),
    )();
  }, [currentCountry.countryCode, handleSubmit, loginFlow?.id, mutate]);

  const onForgetPassword = useCallback(() => {
    navigation.navigate(AuthRouteName.ForgotPassword);
  }, [navigation]);

  const onRegister = useCallback(() => {
    navigation.navigate(AuthRouteName.Register);
  }, [navigation]);

  return {
    t,
    control,
    navigation,
    secureEntry,
    currentCountry,
    disabled: isPending,
    onSubmit,
    setFocus,
    onRegister,
    handleSubmit,
    setSecureEntry,
    onForgetPassword,
    setCurrentCountry,
  };
};

export default useLoginScreen;
