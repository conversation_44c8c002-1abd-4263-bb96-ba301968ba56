import React, { memo } from 'react';
import {
  BlockView,
  Colors,
  CText,
  FontSizes,
  FormTextInput,
  IconAssets,
  IconImage,
  KeyboardAware,
  PrimaryButton,
  Spacing,
  TouchableOpacity,
} from '@btaskee/design-system';
import SocialLogin from '@src/components/SocialLogin';
import { debounce } from 'lodash-es';

import SelectCountry from './components/SelectCountry';
import useLoginScreen, { LoginScreenProps } from './hook';
import styles from './styles';

const LoginScreen = (props: LoginScreenProps) => {
  const {
    t,
    control,
    secureEntry,
    disabled,
    currentCountry,
    onSubmit,
    setFocus,
    onRegister,
    setSecureEntry,
    onForgetPassword,
    setCurrentCountry,
  } = useLoginScreen(props);

  return (
    <KeyboardAware showsVerticalScrollIndicator={false}>
      <BlockView
        padding={Spacing.SPACE_16}
        gap={Spacing.SPACE_24}
      >
        <BlockView>
          <CText
            bold
            size={FontSizes.SIZE_18}
          >
            {t('WELCOME_BACK')}
          </CText>
          <CText style={styles.subtitle}>{t('WELCOME_BACK_DES')}</CText>
          <FormTextInput
            control={control}
            name="phoneNumber"
            testID="phoneNumber"
            label={t('PHONE_NUMBER')}
            placeholder={t('INPUT_PHONE_NUMBER')}
            keyboardType="phone-pad"
            variant="secondary"
            maxLength={12}
            onEndEditing={() => {
              setFocus('password');
            }}
            leftIcon={
              <SelectCountry
                onChangeCountry={setCurrentCountry}
                country={currentCountry}
              />
            }
          />
          <FormTextInput
            control={control}
            name="password"
            label={t('PASSWORD')}
            variant="secondary"
            enableClear
            placeholder={t('INPUT_PASSWORD')}
            secureTextEntry={secureEntry}
            rightIcon={
              <TouchableOpacity onPress={() => setSecureEntry((prev) => !prev)}>
                <IconImage
                  source={
                    secureEntry ? IconAssets.icEye : IconAssets.icEyeSlash
                  }
                  size={24}
                  color={Colors.neutral200}
                />
              </TouchableOpacity>
            }
          />
        </BlockView>

        <PrimaryButton
          title={t('LOGIN')}
          onPress={debounce(onSubmit, 300)}
          disabled={disabled}
        />

        <BlockView
          center
          gap={Spacing.SPACE_04}
          margin={{ vertical: Spacing.SPACE_16 }}
        >
          <TouchableOpacity
            onPress={onRegister}
            gap={Spacing.SPACE_04}
            row
          >
            <CText>{t('DO_NOT_HAVE_AN_ACCOUNT')}</CText>
            <CText
              color={Colors.orange500}
              bold
            >
              {t('CREATE_ACCOUNT')}
            </CText>
          </TouchableOpacity>
          <TouchableOpacity onPress={onForgetPassword}>
            <CText
              color={Colors.orange500}
              bold
            >
              {t('FORGOT_PASSWORD')}
            </CText>
          </TouchableOpacity>
        </BlockView>
      </BlockView>
      <SocialLogin />
    </KeyboardAware>
  );
};

export default memo(LoginScreen);
