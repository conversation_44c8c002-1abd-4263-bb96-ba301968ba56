import { useCallback, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import {
  AuthRouteName,
  COUNTRIES,
  OryKratosApi,
  OryUtils,
  refactorPhoneNumber,
  useAppLoading,
  useAppStore,
  useMutation,
  useQuery,
  validPhoneNumber,
} from '@btaskee/design-system';
import { yupResolver } from '@hookform/resolvers/yup';
import { useAppNavigation, useI18n } from '@src/hooks';
import * as yup from 'yup';

const useForgotPasswordScreen = () => {
  const { t } = useI18n();
  const navigation = useAppNavigation();
  const { isoCode } = useAppStore();
  const { showAppLoading, hideAppLoading } = useAppLoading();

  const currentCountry = useMemo(
    () => COUNTRIES.find((item) => item.isoCode === isoCode) || COUNTRIES[0],
    [isoCode],
  );

  const schema = useMemo(
    () =>
      yup.object().shape({
        phoneNumber: yup
          .string()
          .trim()
          .required(t('THIS_FIELD_IS_REQUIRED'))
          .test('valid-phone', t('INVALID_PHONE'), function (value) {
            const context = this.options.context;
            const countryCode = context?.countryCode;
            if (!value) return false;

            const valid = validPhoneNumber(value, countryCode);
            if (valid) return true;
            return this.createError({
              path: 'phoneNumber',
              message: t('INVALID_PHONE_FOR_COUNTRY'),
            });
          }),
      }),
    [t],
  );

  const { data: flowData } = useQuery({
    queryKey: ['getFlowForgotPassword'],
    queryFn: OryKratosApi.getFlowForgotPassword,
  });

  const { mutate: sendRecoveryCodeMutate } = useMutation({
    mutationFn: OryKratosApi.sendRecoveryCode,
    onMutate: showAppLoading,
    onSettled: hideAppLoading,
    onSuccess: (data, variables) => {
      navigation.replace(AuthRouteName.OTP, {
        phoneNumber: variables.userName,
        flowId: flowData?.id!,
        userName: variables.userName,
        verifyFor: 'forgot_password',
      });
    },
    onError: OryUtils.onError,
  });

  const { control, handleSubmit } = useForm({
    mode: 'onSubmit',
    defaultValues: {
      phoneNumber: '',
    },
    resolver: yupResolver(schema),
    context: {
      countryCode: currentCountry.countryCode,
    },
  });

  const onSubmit = useCallback(() => {
    handleSubmit((data) => {
      sendRecoveryCodeMutate({
        flowId: flowData?.id!,
        userName: refactorPhoneNumber(
          data.phoneNumber,
          currentCountry.countryCode,
        ),
      });
    })();
  }, [
    handleSubmit,
    sendRecoveryCodeMutate,
    flowData?.id,
    currentCountry.countryCode,
  ]);

  return { navigation, t, control, onSubmit };
};

export default useForgotPasswordScreen;
