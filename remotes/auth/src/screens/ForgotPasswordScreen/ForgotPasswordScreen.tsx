import React, { memo } from 'react';
import {
  BlockView,
  CKeyboardStickyView,
  Colors,
  CText,
  FontSizes,
  FormTextInput,
  KeyboardAware,
  PrimaryButton,
  Spacing,
} from '@btaskee/design-system';
import CountryButton from '@src/components/CountryButton';

import useForgotPasswordScreen from './hook';
import styles from './styles';

const ForgotPasswordScreen = () => {
  const { t, control, onSubmit } = useForgotPasswordScreen();

  return (
    <BlockView
      flex
      jBetween
    >
      <KeyboardAware
        showsVerticalScrollIndicator={false}
        bottomOffset={100}
      >
        <BlockView
          padding={Spacing.SPACE_16}
          gap={Spacing.SPACE_24}
        >
          <BlockView>
            <CText
              bold
              size={FontSizes.SIZE_18}
            >
              {t('FORGOT_PASSWORD')}
            </CText>
            <CText style={styles.subtitle}>{t('FORGOT_PASSWORD_DES')}</CText>
            <FormTextInput
              control={control}
              name="phoneNumber"
              label={t('PHONE_NUMBER')}
              placeholder={t('INPUT_PHONE_NUMBER')}
              variant="secondary"
              maxLength={12}
              keyboardType="phone-pad"
              leftIcon={<CountryButton />}
            />
            <CText
              margin={{ top: Spacing.SPACE_04 }}
              color={Colors.neutral300}
            >
              {t('VERIFICATION_WILL_SENT')}
            </CText>
          </BlockView>
        </BlockView>
      </KeyboardAware>
      <CKeyboardStickyView>
        <PrimaryButton
          title={t('NEXT')}
          onPress={onSubmit}
        />
      </CKeyboardStickyView>
    </BlockView>
  );
};

export default memo(ForgotPasswordScreen);
