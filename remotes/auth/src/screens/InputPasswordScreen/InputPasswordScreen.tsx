import React, { memo } from 'react';
import {
  BlockView,
  CKeyboardStickyView,
  Colors,
  CText,
  FontSizes,
  FormTextInput,
  IconAssets,
  IconImage,
  KeyboardAware,
  PrimaryButton,
  Spacing,
  TouchableOpacity,
} from '@btaskee/design-system';
import { debounce } from 'lodash-es';

import useInputPasswordScreen, { InputPasswordScreenProps } from './hook';

const InputPasswordScreen: React.FC<InputPasswordScreenProps> = (props) => {
  const { t, control, onSubmit, secureEntry, setSecureEntry } =
    useInputPasswordScreen(props);

  return (
    <BlockView
      flex
      jBetween
    >
      <KeyboardAware
        showsVerticalScrollIndicator={false}
        bottomOffset={100}
      >
        <BlockView
          padding={Spacing.SPACE_16}
          gap={Spacing.SPACE_24}
        >
          <BlockView gap={Spacing.SPACE_40}>
            <BlockView gap={Spacing.SPACE_08}>
              <CText
                bold
                size={FontSizes.SIZE_18}
                color={Colors.orange500}
              >
                {t('NEW_PASSWORD')}
              </CText>
              <CText>{t('ENTER_YOUR_PASSWORD_TO_CONTINUE')}</CText>
            </BlockView>
            <BlockView>
              <FormTextInput
                control={control}
                name="password"
                label={t('PASSWORD')}
                variant="secondary"
                enableClear
                placeholder={t('INPUT_PASSWORD')}
                secureTextEntry={secureEntry}
                rightIcon={
                  <TouchableOpacity
                    onPress={() => setSecureEntry((prev) => !prev)}
                  >
                    <IconImage
                      source={
                        secureEntry ? IconAssets.icEye : IconAssets.icEyeSlash
                      }
                      size={24}
                      color={Colors.neutral200}
                    />
                  </TouchableOpacity>
                }
              />
              <CText
                margin={{ top: Spacing.SPACE_04 }}
                color={Colors.neutral300}
              >
                {t('VERIFICATION_WILL_SENT')}
              </CText>
            </BlockView>
          </BlockView>
        </BlockView>
      </KeyboardAware>
      <CKeyboardStickyView>
        <PrimaryButton
          title={t('NEXT')}
          onPress={debounce(onSubmit, 300)}
        />
      </CKeyboardStickyView>
    </BlockView>
  );
};

export default memo(InputPasswordScreen);
