import { useCallback, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import {
  AuthRouteName,
  AuthStackScreenProps,
  OryKratosApi,
  OryUtils,
  useAppLoading,
  useAuth,
  useMutation,
} from '@btaskee/design-system';
import { yupResolver } from '@hookform/resolvers/yup';
import { useI18n } from '@src/hooks';
import * as yup from 'yup';

export type InputPasswordScreenProps =
  AuthStackScreenProps<AuthRouteName.InputPassword>;

const useInputPasswordScreen = ({
  navigation,
  route,
}: InputPasswordScreenProps) => {
  const { t } = useI18n();
  const { flowId, session } = route.params;

  const { hideAppLoading, showAppLoading } = useAppLoading();
  const [secureEntry, setSecureEntry] = useState(true);
  const { login } = useAuth();
  const schema = useMemo(
    () =>
      yup.object().shape({
        password: yup
          .string()
          .trim()
          .required(t('THIS_FIELD_IS_REQUIRED'))
          .min(8, t('PASSWORD_MUST_BE_AT_LEAST_8_CHARACTERS')),
      }),
    [t],
  );

  const { control, handleSubmit } = useForm({
    mode: 'onSubmit',
    defaultValues: {
      password: '',
    },
    resolver: yupResolver(schema),
  });

  const { mutate } = useMutation({
    mutationFn: OryKratosApi.updatePasswordRecovery,
    onMutate: showAppLoading,
    onSuccess: () => {
      getWhoAmIMutate({
        token: session,
      });
    },
    onError: OryUtils.onError,
  });

  const { mutate: getWhoAmIMutate } = useMutation({
    mutationFn: OryKratosApi.getWhoAmI,
    onSuccess: async (response, variables) => {
      await login({
        token: response?.tokenized!,
        session_token: variables.token,
      });
    },
    onSettled: hideAppLoading,
  });

  const onSubmit = useCallback(() => {
    handleSubmit((data) => {
      mutate({ flowId, password: data.password, session });
    })();
  }, [flowId, handleSubmit, mutate, session]);

  return { navigation, t, control, onSubmit, secureEntry, setSecureEntry };
};

export default useInputPasswordScreen;
