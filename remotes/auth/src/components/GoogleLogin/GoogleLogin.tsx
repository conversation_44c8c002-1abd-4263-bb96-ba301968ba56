import React, { memo } from 'react';
import {
  BorderRadius,
  Colors,
  DeviceHelper,
  IconImage,
  Spacing,
  TouchableOpacity,
} from '@btaskee/design-system';
import { debounce } from 'lodash-es';

import { google } from '@images';

import useGoogleLogin from './hook';

const GoogleLogin = () => {
  const { onLogin } = useGoogleLogin();

  return (
    <TouchableOpacity
      onPress={debounce(onLogin, 300)}
      border={{
        width: 1,
        color: Colors.neutral100,
      }}
      padding={Spacing.SPACE_12}
      radius={BorderRadius.RADIUS_16}
    >
      <IconImage
        source={google}
        size={28 * DeviceHelper.WIDTH_RATIO}
      />
    </TouchableOpacity>
  );
};

export default memo(GoogleLogin);
