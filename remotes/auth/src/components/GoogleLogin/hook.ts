import { useCallback } from 'react';
import { Alert, AppConfig } from '@btaskee/design-system';
import { GoogleSignin } from '@react-native-google-signin/google-signin';
import { jwtDecode } from 'jwt-decode';
import { includes } from 'lodash-es';

import { useAppNavigation, useI18n, useLoginSocial } from '@hooks';

function getNonceFromGoogleIdToken(idToken: string) {
  try {
    const decoded: any = jwtDecode(idToken);
    return decoded?.nonce;
  } catch (e) {
    return undefined;
  }
}

const useGoogleLogin = () => {
  const { t } = useI18n();
  const navigation = useAppNavigation();
  const { LOGIN_GOOGLE } = AppConfig.get();
  const { onLoginSocial } = useLoginSocial();

  const onLogin = useCallback(async () => {
    try {
      await GoogleSignin.hasPlayServices({
        showPlayServicesUpdateDialog: true,
      });
      // play services are available. can now configure library
      GoogleSignin.configure({
        // iosClientId: LOGIN_GOOGLE?.signInKeyIOS, // only for iOS
        iosClientId:
          '372331106274-77tip0ra87qjj3blkuqjr70950mh6i38.apps.googleusercontent.com',
        // webClientId: LOGIN_GOOGLE?.signInKeyAndroid, // client ID of type WEB for your server (needed to verify user ID and offline access)
        offlineAccess: false,
      });

      // call login
      const response = await GoogleSignin.signIn();
      // sign in error
      if (!response || !response.data?.user) {
        // error
        throw response;
      }
      const id_token_nonce = getNonceFromGoogleIdToken(response.data.idToken!);
      onLoginSocial({
        provider: 'google',
        id_token: response.data.idToken!,
        userSocial: {
          email: response.data.user.email,
          name: response.data.user.name ?? '',
        },
        id_token_nonce,
      });
    } catch (error: any) {
      const errorCode = error?.code;
      //user cancel, not show alert
      if (includes([12501, -5, 16], errorCode) || error?.type === 'cancelled') {
        return;
      }

      const defaultObj = {
        title: t('DIALOG_TITLE_ERROR'),
        message: t('ERROR_TRY_AGAIN'),
        actions: [{ text: t('CLOSE') }],
      };

      // user locked
      if (errorCode === 'USER_LOCKED') {
        defaultObj.message = t('LOGIN_MESSAGE_ACCOUNT_LOCK');
      }

      return Alert.alert.open(defaultObj);
    }
  }, [onLoginSocial, t]);

  return { navigation, t, onLogin };
};

export default useGoogleLogin;
