import React, { memo } from 'react';
import {
  BlockView,
  BorderRadius,
  Colors,
  CText,
  FastImage,
  IconAssets,
  IconImage,
  SelectCountry,
  Spacing,
} from '@btaskee/design-system';

interface CountryButtonProps {
  onChangeCountrySuccess?: () => void;
}

const CountryButton = ({ onChangeCountrySuccess }: CountryButtonProps) => {
  return (
    <SelectCountry onChangeCountrySuccess={onChangeCountrySuccess}>
      {(country) => (
        <BlockView
          row
          horizontal
          gap={Spacing.SPACE_04}
          margin={{ right: Spacing.SPACE_16 }}
        >
          <FastImage
            source={country.flag}
            style={{
              width: Spacing.SPACE_24,
              height: Spacing.SPACE_16,
              borderRadius: BorderRadius.RADIUS_04,
            }}
          />
          <CText>{country.countryCode}</CText>
          <IconImage
            source={IconAssets.icChevronCompactDown}
            size={16}
            color={Colors.neutral200}
          />
        </BlockView>
      )}
    </SelectCountry>
  );
};

export default memo(CountryButton);
