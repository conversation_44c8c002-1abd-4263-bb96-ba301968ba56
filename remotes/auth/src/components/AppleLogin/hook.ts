import { useCallback } from 'react';
import { Alert } from '@btaskee/design-system';
import appleAuth from '@invertase/react-native-apple-authentication';
import { includes } from 'lodash-es';

import { useAppNavigation, useI18n, useLoginSocial } from '@hooks';

const USER_INFO_APPLE_KEY = 'USER_INFO_APPLE_KEY';

const useAppleLogin = () => {
  const { t } = useI18n();
  const navigation = useAppNavigation();
  const { onLoginSocial } = useLoginSocial();

  const onLogin = useCallback(async () => {
    try {
      // call auth with apple

      const appleAuthRequestResponse = await appleAuth.performRequest({
        requestedOperation: appleAuth.Operation.LOGIN,
        requestedScopes: [appleAuth.Scope.EMAIL, appleAuth.Scope.FULL_NAME],
      });

      // let { email, fullName } = appleAuthRequestResponse;

      // const { user } = appleAuthRequestResponse;

      // // save data user to store, because get user info on fist login
      // if (email) {
      //   AppStorage.setObject(USER_INFO_APPLE_KEY, { user, email, fullName });
      // }

      // // get user info from store if email null
      // if (!email) {
      //   const userInfoStore = (await AppStorage.getObject(
      //     USER_INFO_APPLE_KEY,
      //   )) as AppleRequestResponse | null;

      //   if (userInfoStore) {
      //     fullName = userInfoStore?.fullName;
      //     email = userInfoStore?.email;
      //   }
      // }

      // let nameUser = compact([
      //   fullName?.familyName,
      //   fullName?.middleName,
      //   fullName?.givenName,
      // ]).join(' ');
      // if (!nameUser) {
      //   nameUser = 'Guest';
      // }

      onLoginSocial({
        provider: 'apple',
        id_token: appleAuthRequestResponse.identityToken!,
      });
    } catch (error: any) {
      const errorCode = error?.code;
      //user cancel, not show alert
      if (
        includes([appleAuth.Error.CANCELED, appleAuth.Error.UNKNOWN], errorCode)
      ) {
        return;
      }

      const defaultObj = {
        title: t('DIALOG_TITLE_ERROR'),
        message: t('ERROR_TRY_AGAIN'),
        actions: [{ text: t('CLOSE') }],
      };

      // user locked
      if (errorCode === 'USER_LOCKED') {
        defaultObj.message = t('LOGIN_MESSAGE_ACCOUNT_LOCK');
      }

      return Alert.alert.open(defaultObj);
    }
  }, [onLoginSocial, t]);

  return { navigation, t, onLogin };
};

export default useAppleLogin;
