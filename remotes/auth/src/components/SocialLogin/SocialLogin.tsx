import React, { memo } from 'react';
import { BlockView, isIOS, Spacing } from '@btaskee/design-system';

import AppleLogin from '../AppleLogin';
import FacebookLogin from '../FacebookLogin';
import GoogleLogin from '../GoogleLogin';

const SocialLogin = () => {
  return (
    <BlockView
      row
      center
      padding={Spacing.SPACE_16}
      gap={Spacing.SPACE_16}
    >
      {isIOS && <AppleLogin />}
      <FacebookLogin />
      <GoogleLogin />
    </BlockView>
  );
};

export default memo(SocialLogin);
