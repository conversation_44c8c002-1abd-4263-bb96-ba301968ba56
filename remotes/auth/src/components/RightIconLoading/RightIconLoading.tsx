import React, { memo } from 'react';
import { <PERSON><PERSON> } from '@btaskee/design-system';
import { loadingValidate, validateFailed, validatePassed } from '@src/assets';

import styles from './styles';

export interface RightIconLoadingProps {
  isLoading: boolean;
  isError: boolean;
}

const RightIconLoading = ({ isLoading, isError }: RightIconLoadingProps) => {
  if (isLoading) {
    return (
      <Lottie
        style={styles.lottieLoading}
        source={loadingValidate}
        autoPlay={true}
        loop={true}
      />
    );
  }
  if (isError) {
    return (
      <Lottie
        style={styles.lottieLoading}
        source={validateFailed}
        autoPlay={true}
        count={3}
      />
    );
  }
  return (
    <Lottie
      style={styles.lottieLoading}
      source={validatePassed}
      autoPlay={true}
      count={3}
    />
  );
};

export default memo(RightIconLoading);
