import React, { memo } from 'react';
import {
  BorderRadius,
  Colors,
  DeviceHelper,
  IconImage,
  Spacing,
  TouchableOpacity,
} from '@btaskee/design-system';
import { debounce } from 'lodash-es';

import { facebook } from '@images';

import useFacebookLogin from './hook';

const FacebookLogin = () => {
  const { onLogin } = useFacebookLogin();

  return (
    <TouchableOpacity
      onPress={debounce(onLogin, 300)}
      border={{
        width: 1,
        color: Colors.neutral100,
      }}
      padding={Spacing.SPACE_12}
      radius={BorderRadius.RADIUS_16}
    >
      <IconImage
        source={facebook}
        size={28 * DeviceHelper.WIDTH_RATIO}
      />
    </TouchableOpacity>
  );
};

export default memo(FacebookLogin);
