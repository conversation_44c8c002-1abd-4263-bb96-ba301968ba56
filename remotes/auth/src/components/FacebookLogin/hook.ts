import { useCallback } from 'react';
import {
  AccessToken,
  AuthenticationToken,
  GraphRequest,
  GraphRequestManager,
  LoginManager,
} from 'react-native-fbsdk-next';
import { Alert, isIOS, useAppLoading } from '@btaskee/design-system';

import { useAppNavigation, useI18n, useLoginSocial } from '@hooks';

type IRecord = Record<string, unknown> | undefined;

const useFacebookLogin = () => {
  const { t } = useI18n();
  const navigation = useAppNavigation();
  const { showAppLoading, hideAppLoading } = useAppLoading();

  const { onLoginSocial } = useLoginSocial();

  const responseInfoCallback = useCallback(
    async (error: IRecord, result: IRecord, id_token: string) => {
      if (error) {
        return Promise.reject(error);
      }
      onLoginSocial({
        provider: 'facebook',
        id_token,
        userSocial: result,
      });
    },
    [onLoginSocial],
  );

  const FBGraphRequest = useCallback(
    async (fields: string) => {
      if (isIOS) {
        const authentication =
          await AuthenticationToken.getAuthenticationTokenIOS();

        if (!authentication) return Promise.reject();
        return new GraphRequest(
          '/me',
          {
            accessToken: authentication.authenticationToken,
            parameters: { fields: { string: fields } },
          },
          (error, result) =>
            responseInfoCallback(
              error,
              result,
              authentication.authenticationToken,
            ),
        );
      } else {
        const currentAccessToken = await AccessToken.getCurrentAccessToken();

        if (!currentAccessToken) return Promise.reject();

        return new GraphRequest(
          '/me',
          {
            accessToken: currentAccessToken?.accessToken,
            parameters: { fields: { string: fields } },
          },
          (error, result) =>
            responseInfoCallback(error, result, currentAccessToken.accessToken),
        );
      }
    },
    [responseInfoCallback],
  );

  const onLogin = useCallback(async () => {
    try {
      showAppLoading();
      LoginManager.logOut();
      const userInfo = await LoginManager.logInWithPermissions(
        ['email', 'public_profile'],
        'limited',
      );
      if (userInfo.isCancelled) {
        hideAppLoading();
        return;
      }
      const infoRequest = await FBGraphRequest('email,name,picture');

      new GraphRequestManager().addRequest(infoRequest).start();
    } catch (error: any) {
      const errorCode = error?.code;

      const defaultObj = {
        title: t('DIALOG_TITLE_ERROR'),
        message: t('ERROR_TRY_AGAIN'),
        actions: [{ text: t('CLOSE') }],
      };

      // user locked
      if (errorCode === 'USER_LOCKED') {
        defaultObj.message = t('LOGIN_MESSAGE_ACCOUNT_LOCK');
      }

      return Alert.alert.open(defaultObj);
    } finally {
      hideAppLoading();
    }
  }, [FBGraphRequest, hideAppLoading, showAppLoading, t]);

  return { navigation, t, onLogin };
};

export default useFacebookLogin;
