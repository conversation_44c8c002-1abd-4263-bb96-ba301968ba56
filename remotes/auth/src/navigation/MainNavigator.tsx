import '../i18n';

import React from 'react';
import {
  AuthRouteName,
  AuthStackParamList,
  Colors,
  NavBar,
} from '@btaskee/design-system';
import {
  createNativeStackNavigator,
  NativeStackHeaderProps,
  NativeStackNavigationOptions,
} from '@react-navigation/native-stack';
import { useI18n } from '@src/hooks';
import ForgotPasswordScreen from '@src/screens/ForgotPasswordScreen';
import InputPasswordScreen from '@src/screens/InputPasswordScreen';
import LoginScreen from '@src/screens/LoginScreen';
import OTPScreen from '@src/screens/OTPScreen';
import RegisterScreen from '@src/screens/RegisterScreen';
import SocialUpdateProfileScreen from '@src/screens/SocialUpdateProfileScreen';

const Stack = createNativeStackNavigator<AuthStackParamList>();

const MainNavigator = () => {
  const { t } = useI18n();
  return (
    <Stack.Navigator
      screenOptions={({ navigation }): NativeStackNavigationOptions => ({
        headerShown: true,
        animation: 'slide_from_right',
        animationDuration: 200,
        contentStyle: { backgroundColor: Colors.neutralBackground },
        // eslint-disable-next-line react/no-unstable-nested-components
        header: (props: NativeStackHeaderProps) => {
          // Get the title from options
          const getTitle = () => {
            if (
              props?.options?.headerTitle &&
              typeof props.options.headerTitle === 'function'
            ) {
              // @ts-ignore - React Navigation headerTitle function compatibility
              return props.options.headerTitle();
            }
            if (
              props?.options?.headerTitle &&
              typeof props.options.headerTitle === 'string'
            ) {
              return props.options.headerTitle;
            }
            if (props?.options?.title) {
              return props.options.title;
            }
            return '';
          };

          return (
            <NavBar
              // @ts-ignore - NavBar title accepts ReactNode but types are strict
              title={getTitle()}
              backgroundColor={Colors.neutralWhite}
              onGoBack={navigation?.goBack}
              isShadow={true}
              // eslint-disable-next-line react-native/no-inline-styles
              style={{ borderBottomWidth: 0 }}
              right={
                props.options.headerRight
                  ? props.options.headerRight({
                      canGoBack: navigation?.canGoBack(),
                      tintColor: Colors.neutral800,
                    })
                  : undefined
              }
            />
          );
        },
      })}
    >
      <Stack.Screen
        name={AuthRouteName.Login}
        component={LoginScreen}
        options={{ title: t('LOGIN') }}
      />
      <Stack.Screen
        name={AuthRouteName.OTP}
        component={OTPScreen}
        options={{ title: 'OTP' }}
      />
      <Stack.Screen
        name={AuthRouteName.Register}
        component={RegisterScreen}
        options={{ title: t('REGISTER') }}
      />
      <Stack.Screen
        name={AuthRouteName.ForgotPassword}
        component={ForgotPasswordScreen}
        options={{ title: t('FORGOT_PASSWORD') }}
      />
      <Stack.Screen
        name={AuthRouteName.InputPassword}
        component={InputPasswordScreen}
        options={{ title: t('NEW_PASSWORD') }}
      />
      <Stack.Screen
        name={AuthRouteName.SocialUpdateProfile}
        component={SocialUpdateProfileScreen}
        options={{ title: t('TITLE_UPDATE_PROFILE') }}
      />
    </Stack.Navigator>
  );
};

export default MainNavigator;
