import { useCallback, useState } from 'react';
import {
  AuthRouteName,
  ILoginSocialBody,
  OryKratosApi,
  OryUtils,
  useAppLoading,
  useAuth,
  useMutation,
  useQuery,
} from '@btaskee/design-system';
import { useAppNavigation, useI18n } from '@src/hooks';

interface IUserSocial {
  name?: string;
  email?: string;
}

export const useLoginSocial = () => {
  const { t } = useI18n();
  const navigation = useAppNavigation();
  const { showAppLoading, hideAppLoading } = useAppLoading();
  const { login } = useAuth();
  const [userSocial, setUserSocial] = useState<IUserSocial>();

  const { data: loginFlow } = useQuery({
    queryKey: ['getLoginFlow'],
    queryFn: OryKratosApi.getLoginFlow,
    staleTime: 1 * 60 * 1000,
  });

  const { mutate: getWhoAmIMutate } = useMutation({
    mutationFn: OryKratosApi.getWhoAmI,
    onSettled: hideAppLoading,
    onSuccess: async (response, variables) => {
      await login({
        token: response?.tokenized!,
        session_token: variables.token,
      });
    },
  });

  const { mutate: loginSocialMutate, mutateAsync } = useMutation({
    mutationFn: OryKratosApi.loginSocial,
    onMutate: showAppLoading,
    onSuccess: (response) => {
      if (response?.isNewUser) {
        hideAppLoading();
        navigation.navigate(AuthRouteName.SocialUpdateProfile, {
          session_token:
            OryUtils.getSessionToken(response?.continue_with) ||
            response.session_token,
          name: userSocial?.name,
          email: userSocial?.email,
        });
      } else {
        getWhoAmIMutate({ token: response.session_token });
      }
    },
    onError: OryUtils.onError,
  });

  const onLoginSocial = useCallback(
    async ({
      id_token,
      provider,
      userSocial,
      id_token_nonce,
    }: Omit<ILoginSocialBody, 'flowId'> & {
      userSocial?: IUserSocial;
    }) => {
      setUserSocial(userSocial);

      loginSocialMutate({
        provider,
        id_token,
        id_token_nonce,
        flowId: loginFlow?.id!,
      });
    },
    [loginFlow?.id, loginSocialMutate],
  );

  return { navigation, t, onLoginSocial };
};

export default useLoginSocial;
