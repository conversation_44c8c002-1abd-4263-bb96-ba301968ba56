const { device } = require('detox');
const {
  reloadApp,
  tapId,
  waitForElement,
  expectElementVisible,
  swipe,
  typeToTextField,
} = require('./step-definition');

describe('Authen Flow', () => {
  beforeEach(async () => {
    // Reset app state and data for consistent testing
    await reloadApp();
    await device.reloadReactNative();
  });
  it('should display login screen', async () => {
    await waitForElement('phoneNumber', 8000);
    await expectElementVisible('phoneNumber');
  });
});
