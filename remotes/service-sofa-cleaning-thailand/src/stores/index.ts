import {
  AppStorage,
  createZustand,
  IAddress,
  IDate,
  IPaymentMethodInfo,
  IPrice,
  IPromotion,
  IRelatedTask,
  IService,
  PaymentService,
  SERVICES,
  TrackingPostTaskStep,
  TYPE_OF_PAYMENT,
} from '@btaskee/design-system';
import { createJSONStorage, persist } from 'zustand/middleware';

import {
  BedItem,
  CarpetItem,
  CleaningData,
  CurtainItem,
  SofaItem,
} from '../types/task';

interface IState {
  address: IAddress;
  duration: number;
  date: IDate | null;
  note: string;
  isApplyNoteForAllTask: boolean;
  isFavouriteTasker: boolean;
  price: IPrice | null;
  service: IService | null;
  paymentMethod?: IPaymentMethodInfo;
  promotion?: IPromotion;
  relatedTask: IRelatedTask | null;
  carpet: CarpetItem[];
  carpetNote: string;
  sofa: SofaItem[];
  bed: BedItem[];
  curtain: CurtainItem[];
  curtainWashing: any;
  cleaningData: CleaningData;
  loadingPrice: boolean;
  sofaTabIndex: number;
  mattressTabIndex: number;
  carpetQtyCustomArea: number;
  carpetCustomArea: number;
  // Tracking-related properties
  stepPostTask: TrackingPostTaskStep;
  isBookedTask: boolean;
  isFirstOpenService: boolean;
}

interface IActions {
  setSofaData: (sofa: SofaItem[]) => void;
  setBed: (bed: BedItem[]) => void;
  setCurtain: (curtain: CurtainItem[]) => void;
  setCarpet: (carpet: CarpetItem[]) => void;
  setCarpetNote: (carpetNote: string) => void;
  setAddress: (address: IAddress) => void;
  setDuration: (duration: number) => void;
  setDateTime: (date: IDate) => void;
  setNote: (note: string) => void;
  setIsApplyNoteForAllTask: (isApplyNoteForAllTask: boolean) => void;
  setIsFavouriteTasker: (isFavouriteTasker: boolean) => void;
  setPrice: (price: IPrice | null) => void;
  setPaymentMethod: (paymentMethod?: IPaymentMethodInfo) => void;
  setPromotion: (promotion?: IPromotion) => void;
  setRelatedTask: (relatedTask: IRelatedTask) => void;
  setService: (service: IService) => void;
  setLoadingPrice: (loadingPrice: boolean) => void;
  setSofaCleaningTabIndex: (sofaCleaningTabIndex: number) => void;
  setMattressCleaningTabIndex: (mattressCleaningTabIndex: number) => void;
  setCarpetQtyCustomArea: (carpetQtyCustomArea: number) => void;
  setCarpetCustomArea: (carpetCustomArea: number) => void;
  // Tracking-related actions
  setStepPostTask: (stepPostTask: TrackingPostTaskStep) => void;
  setIsBookedTask: (isBookedTask: boolean) => void;
  setIsFirstOpenService: (isFirstOpenService: boolean) => void;
  resetState: () => void;
}

type SofaCleaningState = IState & IActions;

export const usePostTaskStore = createZustand<SofaCleaningState>()(
  persist(
    (set) => ({
      address: {} as IAddress,
      duration: 0,
      date: null,
      note: '',
      isApplyNoteForAllTask: false,
      isFavouriteTasker: false,
      price: null,
      service: null,
      carpet: [],
      carpetNote: '',
      sofa: [],
      cleaningData: {},
      bed: [],
      curtain: [],
      curtainWashing: null,
      loadingPrice: false,
      sofaTabIndex: 0,
      mattressTabIndex: 0,
      carpetQtyCustomArea: 0,
      carpetCustomArea: 0,
      // Tracking-related state
      stepPostTask: TrackingPostTaskStep.STEP_2,
      isBookedTask: false,
      isFirstOpenService: true,
      setSofaData: (sofa: SofaItem[]) => set({ sofa: sofa }),
      setBed: (bed: BedItem[]) => set({ bed: bed }),
      setCurtain: (curtain: CurtainItem[]) => set({ curtain: curtain }),
      setCarpet: (carpet: CarpetItem[]) => set({ carpet: carpet }),
      setCarpetNote: (carpetNote: string) => set({ carpetNote: carpetNote }),
      paymentMethod: PaymentService.getDefaultPaymentMethod({
        serviceName: SERVICES.SOFA,
        type: TYPE_OF_PAYMENT.bookTask,
      }),
      promotion: undefined,
      relatedTask: null,
      setAddress: (address: IAddress) => set({ address: address }),
      setDuration: (duration: number) => set({ duration: duration }),
      setDateTime: (date: IDate) => set({ date: date }),
      setNote: (note: string) => set({ note: note }),
      setIsApplyNoteForAllTask: (isApplyNoteForAllTask: boolean) =>
        set({ isApplyNoteForAllTask: isApplyNoteForAllTask }),
      setIsFavouriteTasker: (isFavouriteTasker: boolean) =>
        set({ isFavouriteTasker: isFavouriteTasker }),
      setPrice: (price: IPrice | null) => set({ price: price }),
      setPaymentMethod: (paymentMethod) =>
        set({ paymentMethod: paymentMethod }),
      setPromotion: (promotion) => set({ promotion: promotion }),
      setRelatedTask: (relatedTask: IRelatedTask) =>
        set({ relatedTask: relatedTask }),
      setService: (service: IService) => set({ service: service }),
      setCleaningData: (data: CleaningData) => set({ cleaningData: data }),
      setLoadingPrice: (loadingPrice: boolean) =>
        set({ loadingPrice: loadingPrice }),
      setSofaCleaningTabIndex: (sofaCleaningTabIndex: number) =>
        set({ sofaTabIndex: sofaCleaningTabIndex }),
      setMattressCleaningTabIndex: (mattressCleaningTabIndex: number) =>
        set({ mattressTabIndex: mattressCleaningTabIndex }),
      setCarpetQtyCustomArea: (carpetQtyCustomArea: number) =>
        set({ carpetQtyCustomArea: carpetQtyCustomArea }),
      setCarpetCustomArea: (carpetCustomArea: number) =>
        set({ carpetCustomArea: carpetCustomArea }),
      // Tracking-related actions
      setStepPostTask: (stepPostTask: TrackingPostTaskStep) =>
        set({ stepPostTask: stepPostTask }),
      setIsBookedTask: (isBookedTask: boolean) =>
        set({ isBookedTask: isBookedTask }),
      setIsFirstOpenService: (isFirstOpenService: boolean) =>
        set({ isFirstOpenService: isFirstOpenService }),
      resetState: () =>
        set((state) => ({
          address: {} as IAddress,
          duration: 0,
          date: null,
          note: '',
          isApplyNoteForAllTask: false,
          isFavouriteTasker: false,
          price: null,
          service: null,
          paymentMethod: PaymentService.getDefaultPaymentMethod({
            serviceName: SERVICES.SOFA,
            type: TYPE_OF_PAYMENT.bookTask,
          }),
          promotion: undefined,
          relatedTask: null,
          carpet: [],
          carpetNote: '',
          sofa: [],
          cleaningData: {},
          bed: [],
          curtain: [],
          curtainWashing: null,
          loadingPrice: false,
          sofaTabIndex: 0,
          mattressTabIndex: 0,
          carpetQtyCustomArea: 0,
          carpetCustomArea: 0,
          // Preserve tracking state during reset
          stepPostTask: state.stepPostTask,
          isBookedTask: state.isBookedTask,
          isFirstOpenService: state.isFirstOpenService,
        })),
    }),
    {
      name: 'sofa-th-cleaning-service-storage',
      storage: createJSONStorage(() => AppStorage.zustandStorage),
      version: 1,
      partialize: (state) => ({
        isFirstOpenService: state.isFirstOpenService,
      }),
    },
  ),
);
