import React, { useCallback, useEffect, useState } from 'react';
import { AppState, AppStateStatus } from 'react-native';
import { TabView } from 'react-native-tab-view';
import {
  BlockView,
  checkSupportCity,
  Colors,
  ConditionView,
  CText,
  <PERSON>ceHelper,
  IAddress,
  IconAssets,
  IconImage,
  isIOS,
  NotSupportCity,
  PostTaskHelpers,
  PriceButton,
  SERVICES,
  Spacing,
  TouchableOpacity,
  TrackingActions,
  TrackingPostTaskStep,
} from '@btaskee/design-system';
import { SOFA_TYPE_FROM } from '@constants';
import { useIsFocused } from '@react-navigation/native';
import { find, get } from 'lodash-es';

import { useAppNavigation, useI18n, useTracking } from '@hooks';
import { RouteName } from '@navigation/RouteName';
import { usePostTaskStore } from '@stores';

import { CarpetTab } from './components/CarpetTab';
import { CurtainTab } from './components/CurtainTab';
import { MattressTab } from './components/MattressTab';
import { SofaTab } from './components/SofaTab';
import { styles } from './styles';

// Types
interface ChooseServiceProps {
  previousServiceId?: string;
  resetStateSofaCleaning?: () => void;
}

interface TabRoute {
  key: string;
  name: string;
  title: string;
}

interface BaseServiceItem {
  quantity: number;
  name?: string;
  text?: string;
}

interface ServiceItem extends BaseServiceItem {
  type?: string;
}

interface SofaServiceItem extends BaseServiceItem {
  additionalSeat?: number;
  type?: ServiceItem[];
  typeSofa?: ServiceItem[];
  others?: ServiceItem[];
  stool?: ServiceItem[];
}

interface CarpetOrCurtainItem {
  quantityCustomArea?: number;
  type?: ServiceItem[];
}

interface ServiceData {
  carpet: CarpetOrCurtainItem[];
  bed: ServiceItem[];
  sofa: SofaServiceItem[];
  curtain: CarpetOrCurtainItem[];
  curtainWashing?: CarpetOrCurtainItem[];
}

// Constants
const TAB_KEYS = {
  SOFA: SOFA_TYPE_FROM.SOFA,
  CARPET: SOFA_TYPE_FROM.CARPET,
  MATTRESS: SOFA_TYPE_FROM.MATTRESS,
  CURTAIN: SOFA_TYPE_FROM.CURTAIN,
} as const;

// Utility Functions
const calculateItemQuantity = (items: ServiceItem[]): number => {
  if (!items?.length) return 0;
  return items.reduce((total, item) => total + item.quantity, 0);
};

const calculateSofaQuantity = (sofaItems: SofaServiceItem[]): number => {
  return sofaItems.reduce((total, sofaType) => {
    let subtotal = 0;

    if (sofaType?.others?.length) {
      subtotal += calculateItemQuantity(sofaType.others);
    }
    if (sofaType?.stool?.length) {
      subtotal += calculateItemQuantity(sofaType.stool);
    }
    if (sofaType?.type?.length) {
      subtotal += calculateItemQuantity(sofaType.type);
    }
    if (sofaType?.additionalSeat) {
      subtotal += sofaType.additionalSeat;
    }

    return total + subtotal;
  }, 0);
};

const calculateCarpetOrCurtainQuantity = (
  items: CarpetOrCurtainItem[],
): number => {
  return items.reduce((total, item) => {
    let subtotal = 0;

    if (item?.quantityCustomArea) {
      subtotal += item.quantityCustomArea;
    }
    if (item?.type?.length) {
      subtotal += calculateItemQuantity(item.type);
    }

    return total + subtotal;
  }, 0);
};

const getItemCount = (routeName: string, data: ServiceData): number => {
  const { carpet, bed, sofa, curtain } = data;

  switch (routeName) {
    case TAB_KEYS.CARPET:
      return calculateCarpetOrCurtainQuantity(carpet);
    case TAB_KEYS.MATTRESS:
      return calculateItemQuantity(bed);
    case TAB_KEYS.SOFA:
      return calculateSofaQuantity(sofa);
    case TAB_KEYS.CURTAIN:
      return calculateCarpetOrCurtainQuantity(curtain);
    default:
      return 0;
  }
};

// Custom Hooks
const useServiceDetail = (service: any, address: IAddress) => {
  const [detailSofa, setDetailSofa] = useState<any>([]);

  const setDetailSofaByCity = useCallback(() => {
    const cityFromAddress = get(address, 'city', null);
    const detailsSofa = get(service, 'detailSofaTH', null);

    if (!detailsSofa || !cityFromAddress) {
      return;
    }

    const listCitySupport = detailsSofa.city || [];
    const detailSofaResult = find(listCitySupport, { name: cityFromAddress });
    setDetailSofa(detailSofaResult);
  }, [address, service]);

  useEffect(() => {
    setDetailSofaByCity();
  }, [setDetailSofaByCity]);

  return { detailSofa, setDetailSofaByCity };
};

const useTabConfiguration = () => {
  const { t } = useI18n();

  const routes: TabRoute[] = [
    {
      key: TAB_KEYS.SOFA,
      name: TAB_KEYS.SOFA,
      title: t('SV_SOFA_SCR2_TAB_SOFA_TITLE'),
    },
    {
      key: TAB_KEYS.MATTRESS,
      name: TAB_KEYS.MATTRESS,
      title: t('SV_SOFA_SCR2_TAB_MATTRESS_TITLE'),
    },
    {
      key: TAB_KEYS.CURTAIN,
      name: TAB_KEYS.CURTAIN,
      title: t('SV_SOFA_SCR2_TAB_CURTAIN_TITLE'),
    },
    {
      key: TAB_KEYS.CARPET,
      name: TAB_KEYS.CARPET,
      title: t('SV_SOFA_SCR2_TAB_CARPET_TITLE'),
    },
  ];

  return routes;
};

const useItemCounting = (serviceData: ServiceData) => {
  const getCount = useCallback(
    (routeName: string) => getItemCount(routeName, serviceData),
    [serviceData],
  );

  return getCount;
};

// Components
const HeaderTitle: React.FC<{ address: IAddress }> = ({ address }) => (
  <BlockView
    flex
    row
    horizontal
    jBetween>
    <BlockView
      flex
      row>
      <IconImage
        source={IconAssets.icLocation}
        size={24}
        color={Colors.red500}
      />
      <BlockView
        flex
        margin={{ left: Spacing.SPACE_08 }}>
        <CText>{address?.shortAddress}</CText>
        <CText
          bold
          numberOfLines={1}
          margin={{ right: Spacing.SPACE_16 }}>
          {address?.address}
        </CText>
      </BlockView>
    </BlockView>
  </BlockView>
);

const TabBadge: React.FC<{ count: number }> = ({ count }) => (
  <ConditionView
    condition={Boolean(count)}
    viewTrue={
      <BlockView style={styles.badgeContainer}>
        <CText
          bold
          style={styles.badgeTxt}>
          {count}
        </CText>
      </BlockView>
    }
  />
);

const CustomTabBar: React.FC<{
  routes: TabRoute[];
  tabSelected: number;
  onTabPress: (index: number) => void;
  getCount: (routeName: string) => number;
}> = ({ routes, tabSelected, onTabPress, getCount }) => (
  <BlockView
    key="tab-bar"
    center
    row>
    {routes.map((route, index) => {
      const isFocusedTab = tabSelected === index;
      const count = getCount(route.name);

      return (
        <TouchableOpacity
          key={`${route.name}-${index}`}
          activeOpacity={0.6}
          style={[styles.tabBarItem, isFocusedTab && styles.tabBarItemActive]}
          onPress={() => onTabPress(index)}>
          <BlockView
            center
            row>
            <CText
              bold={isFocusedTab}
              numberOfLines={2}
              style={[isFocusedTab ? styles.titleTxtActive : styles.titleTxt]}>
              {route.title}
            </CText>
            <TabBadge count={count} />
          </BlockView>
        </TouchableOpacity>
      );
    })}
  </BlockView>
);

const TabSceneRenderer: React.FC<{
  route: TabRoute;
  detailSofa: any;
}> = ({ route, detailSofa }) => {
  switch (route.name) {
    case TAB_KEYS.CARPET:
      return <CarpetTab serviceCarpet={detailSofa?.carpet || []} />;
    case TAB_KEYS.MATTRESS:
      return <MattressTab serviceMattress={detailSofa?.bed || []} />;
    case TAB_KEYS.SOFA:
      return <SofaTab serviceSofa={detailSofa?.sofa || []} />;
    case TAB_KEYS.CURTAIN:
      return <CurtainTab serviceCurtain={detailSofa?.curtain || []} />;
    default:
      return <BlockView />;
  }
};

// Main Component
export const ChooseService: React.FC<ChooseServiceProps> = ({
  previousServiceId,
  resetStateSofaCleaning,
}) => {
  const navigation = useAppNavigation();
  const isFocused = useIsFocused();
  const {
    price,
    address,
    bed,
    curtain,
    curtainWashing,
    sofa,
    carpet,
    service,
    date,
    setDateTime,
    setStepPostTask,
  } = usePostTaskStore();

  const {
    trackingBackNextActionChooseSofaInfo,
    trackingChooseSofaInfoScreenView,
    trackingPostTaskAbandoned,
  } = useTracking();

  const [tabSelected, setTabSelected] = useState(0);

  // Custom hooks
  const { detailSofa, setDetailSofaByCity } = useServiceDetail(
    service,
    address,
  );
  const routes = useTabConfiguration();

  const serviceData: ServiceData = {
    carpet,
    bed,
    sofa,
    curtain,
    curtainWashing,
  };

  const getCount = useItemCounting(serviceData);

  // Track screen view on component mount
  useEffect(() => {
    trackingChooseSofaInfoScreenView();
    setStepPostTask(TrackingPostTaskStep.STEP_2);
  }, []);

  // Handle tracking for back navigation
  const handleTrackingActionStep2 = useCallback(
    (action: TrackingActions) => {
      if (!isFocused) {
        return null;
      }
      trackingBackNextActionChooseSofaInfo(action);
    },
    [trackingBackNextActionChooseSofaInfo, isFocused],
  );

  useEffect(() => {
    const unsubscribe = navigation.addListener('beforeRemove', () => {
      handleTrackingActionStep2(TrackingActions.Back);
    });

    return unsubscribe; // Cleanup listener when component unmounts
  }, [navigation, handleTrackingActionStep2]);

  // Handle app state changes for tracking
  const handleAppStateChange = (nextAppState: AppStateStatus) => {
    if (isIOS) {
      if (nextAppState === 'inactive') {
        trackingPostTaskAbandoned(TrackingActions.EXITED_APP);
      }
    } else if (nextAppState === 'background') {
      trackingPostTaskAbandoned(TrackingActions.EXITED_APP);
    }
  };

  useEffect(() => {
    const subscribe = AppState.addEventListener('change', handleAppStateChange);
    return () => {
      trackingPostTaskAbandoned(TrackingActions.TAP_HEADER_BACK);
      subscribe?.remove();
    };
  }, []);

  // Handlers
  const handleConfirm = useCallback(() => {
    trackingBackNextActionChooseSofaInfo(TrackingActions.Next);
    navigation.navigate(RouteName.ChooseDateTime);
  }, [navigation, trackingBackNextActionChooseSofaInfo]);

  const renderScene = useCallback(
    ({ route }: { route: TabRoute }) => (
      <TabSceneRenderer
        route={route}
        detailSofa={detailSofa}
      />
    ),
    [detailSofa],
  );

  // Effects
  useEffect(() => {
    navigation.setOptions({
      headerTitle: () => <HeaderTitle address={address} />,
    });
  }, [navigation, address]);

  useEffect(() => {
    // Initialize default date time if not set
    if (!date) {
      setDateTime(
        PostTaskHelpers.getDefaultDateTime(
          {
            serviceName: SERVICES.SOFA,
            defaultTaskTime: service?.defaultTaskTime,
          },
          service?.defaultTaskTime,
          address?.city,
        ),
      );
    }
  }, [date, setDateTime, service?.defaultTaskTime, address?.city]);

  useEffect(() => {
    // Check if service changed and reset state if needed
    if (previousServiceId && previousServiceId !== service?._id) {
      resetStateSofaCleaning?.();
    }
    setDetailSofaByCity();
  }, [
    previousServiceId,
    service?._id,
    resetStateSofaCleaning,
    setDetailSofaByCity,
  ]);

  // Check city support
  if (!checkSupportCity(service?.city, address?.city)) {
    return <NotSupportCity />;
  }

  return (
    <BlockView style={{ flex: 1 }}>
      <TabView
        key="sofa-cleaning-tab-view"
        lazy
        navigationState={{ index: tabSelected, routes }}
        renderScene={renderScene}
        renderLazyPlaceholder={() => <BlockView />}
        onIndexChange={setTabSelected}
        initialLayout={{ width: DeviceHelper.WINDOW.WIDTH }}
        renderTabBar={() => (
          <CustomTabBar
            routes={routes}
            tabSelected={tabSelected}
            onTabPress={setTabSelected}
            getCount={getCount}
          />
        )}
      />
      <PriceButton
        pricePostTask={price}
        testID="btnNextStep2"
        onPress={handleConfirm}
        fromScreen={service?.name}
      />
    </BlockView>
  );
};
