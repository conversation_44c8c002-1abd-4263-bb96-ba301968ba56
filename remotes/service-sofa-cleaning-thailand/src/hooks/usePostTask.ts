import { useCallback, useMemo } from 'react';
import {
  <PERSON><PERSON>,
  DateTimeHel<PERSON>,
  EndpointKeys,
  handleError,
  IApiError,
  PaymentService,
  PostTaskHelpers,
  useApiMutation,
  useAppLoadingStore,
  useAppStore,
  usePostTaskAction,
  useUserStore,
} from '@btaskee/design-system';
import { IDataBooking, IParamsGetPrice } from '@types';
import { cloneDeep, debounce, findIndex, isEmpty } from 'lodash-es';

import { usePostTaskStore } from '@stores';

// Import types
import {
  BedItem,
  CarpetDataParams,
  CarpetItem,
  CarpetType,
  ChangeServiceOptionParams,
  ExtendedTaskData,
  SetCurtainDataParams,
  SetSofaCleaningDataParams,
} from '../types/task';
import {
  updateBedData,
  updateCurtainData,
  updateCurtainServiceOption,
  updateSofaCleaningData,
  validateBedData,
  validateCurtainData,
  validateSofaCleaningParams,
} from '../utils/sofaDataHelpers';
// Import utilities
import {
  buildDetailSofaData,
  validateSofaData,
} from '../utils/taskDataBuilders';
import { useI18n } from './useI18n';
import { useTracking } from './useTracking';

/**
 * Enhanced hook for managing sofa cleaning task operations
 * with improved error handling and performance optimizations
 *
 * @returns {Object} Hook methods for task management
 * @returns {Function} getPrice - Get pricing for current task configuration
 * @returns {Function} postTask - Submit task for booking
 * @returns {Function} setSofaCleaningData - Update sofa cleaning configuration
 * @returns {Function} setBedData - Update bed cleaning configuration
 * @returns {Function} setCurtainData - Update curtain cleaning configuration
 * @returns {Function} changeServiceOption - Change curtain service options
 * @returns {Function} setCarpetData - Update carpet cleaning configuration
 */
export const usePostTask = () => {
  const {
    setPrice,
    setLoadingPrice,
    setDuration,
    resetState,
    setSofaData,
    setBed,
    setCurtain,
    setCarpet,
    setIsBookedTask,
  } = usePostTaskStore();
  const { showLoading, hideLoading } = useAppLoadingStore();
  const { handlePostTaskError } = usePostTaskAction();
  const { user } = useUserStore();
  const { isoCode } = useAppStore();
  const { t } = useI18n();

  const { trackingPostTaskSuccess } = useTracking();

  // Memoize API mutations to prevent unnecessary re-renders
  const { mutate: getPriceSofaCleaning } = useApiMutation({
    key: EndpointKeys.getPriceSofaCleaning,
    options: {
      onMutate: () => {
        setLoadingPrice(true);
        showLoading();
      },
      onSettled: () => {
        setLoadingPrice(false);
        hideLoading();
      },
    },
  });

  const { mutate: postTaskSofaCleaning } = useApiMutation({
    key: EndpointKeys.postTaskSofaCleaning,
    options: {
      onSuccess: async (data: any) => {
        if (data?.bookingId) {
          trackingPostTaskSuccess();
          resetState();
          setIsBookedTask(true);
          await PaymentService.onPostTaskSuccess({
            bookingId: data.bookingId,
            isPrepayment: data.isPrepayment,
          });
        }
      },
      onError: (error: IApiError) => {
        handlePostTaskError(error);
      },
      onMutate: () => {
        showLoading();
      },
      onSettled() {
        hideLoading();
      },
    },
  });

  const { mutate: checkTaskSameTime } = useApiMutation({
    key: EndpointKeys.checkTaskSameTime,
    options: {
      onMutate: () => {
        showLoading();
      },
      onSettled() {
        hideLoading();
      },
    },
  });

  // Memoize pricing data building for performance
  const buildPricingData = useCallback((): IParamsGetPrice | null => {
    try {
      const currentState = usePostTaskStore.getState();
      const {
        address,
        date,
        duration,
        service,
        paymentMethod,
        sofa,
        bed,
        curtain,
        carpet,
        curtainWashing,
        promotion,
      } = currentState;

      if (!address || !date) {
        return null;
      }

      const timezone = DateTimeHelpers.getTimezoneByCity(address.city);
      const sofaData = {
        sofa,
        bed,
        curtain,
        carpet,
        curtainWashing,
      };

      // Validate sofa data before processing
      if (!validateSofaData(sofaData)) {
        return null;
      }

      const detailSofaData = buildDetailSofaData(sofaData);

      const task: IParamsGetPrice['task'] = {
        timezone,
        date: DateTimeHelpers.formatToString({ timezone, date }),
        autoChooseTasker: true,
        taskPlace: {
          country: address.country,
          city: address.city,
          district: address.district,
        },
        homeType: address.homeType,
        duration,
        detailSofaTH: detailSofaData,
        ...PostTaskHelpers.formatDataToParams({ paymentMethod, promotion }),
      };

      return {
        task,
        service: { _id: service?._id },
        isoCode: isoCode as string,
      };
    } catch (error) {
      return null;
    }
  }, []);

  // Memoize task data building
  const buildTaskData = useCallback((): IDataBooking & ExtendedTaskData => {
    const currentState = usePostTaskStore.getState();
    const {
      address,
      date,
      duration,
      service,
      paymentMethod,
      isApplyNoteForAllTask,
      note,
      isFavouriteTasker,
      relatedTask,
      sofa,
      bed,
      curtain,
      carpet,
      curtainWashing,
      promotion,
    } = currentState;

    const timezone = DateTimeHelpers.getTimezoneByCity(address.city);
    const detailSofaData = buildDetailSofaData({
      sofa,
      bed,
      curtain,
      carpet,
      curtainWashing,
    });

    const task: IDataBooking & ExtendedTaskData = {
      address: address.address,
      contactName: address.contact || user?.name || '',
      lat: address.lat,
      lng: address.lng,
      phone: address.phoneNumber || user?.phone || '',
      countryCode: address.countryCode || user?.countryCode || '',
      description: address.description,
      askerId: user?._id || '',
      autoChooseTasker: true,
      date: date ? DateTimeHelpers.formatToString({ date, timezone }) : '',
      timezone,
      duration,
      homeType: address.homeType,
      houseNumber: address.description,
      isoCode: isoCode || '',
      serviceId: service?._id || '',
      taskPlace: {
        country: address.country,
        city: address.city,
        district: address.district,
      },
      shortAddress: address.shortAddress,
      isSendToFavTaskers: isFavouriteTasker,
      updateTaskNoteToUser: isApplyNoteForAllTask,
      detailSofaTH: detailSofaData,
      ...PostTaskHelpers.formatDataToParams({ paymentMethod, promotion }),
    } as IDataBooking & ExtendedTaskData;

    // Add optional fields
    if (address?.isAddressMaybeWrong) {
      task.taskPlace!.isAddressMaybeWrong = true;
    }

    if (service?.isTet) {
      task.isTetBooking = true;
    }

    if (note?.trim()) {
      task.taskNote = note.trim();
    }

    if (!isEmpty(relatedTask?.relatedTaskId)) {
      task.source = {
        from: relatedTask?.service?.name || '',
        taskId: relatedTask?.relatedTaskId || '',
      };
    }

    return task;
  }, []);

  /**
   * Get price for sofa cleaning service with error handling
   * @returns {Promise<void>} Promise that resolves when pricing is complete
   */
  const getPrice = useCallback(async (): Promise<void> => {
    try {
      const pricingData = buildPricingData();

      if (!pricingData) {
        setPrice(null);
        return;
      }

      setLoadingPrice(true);

      await getPriceSofaCleaning(pricingData, {
        onSuccess: (result: any) => {
          setPrice(result);
          setDuration(result?.duration || 0);
        },
        onError: (error: IApiError) => {
          handleError(error);
          setPrice(null);
        },
      });
    } catch (error) {
      setPrice(null);
    } finally {
      setLoadingPrice(false);
    }
  }, [
    buildPricingData,
    getPriceSofaCleaning,
    setPrice,
    setDuration,
    setLoadingPrice,
  ]);

  /**
   * Execute task posting with error handling
   */
  const executeTaskPosting = debounce(
    async ({ isExistTask }: { isExistTask?: boolean }) => {
      const taskData = buildTaskData();

      // time ok
      if (!isExistTask) {
        // call api book task
        postTaskSofaCleaning(taskData);
        return;
      }

      // same time, alert for user
      return Alert.alert.open({
        title: t('DIALOG_TITLE_INFORMATION'),
        message: t('TASK_SAME_TIME_MESSAGE'),
        actions: [
          { text: t('CLOSE'), style: 'cancel' },
          {
            text: t('OK'),
            onPress: async () => {
              // wait modal close
              setTimeout(async () => {
                postTaskSofaCleaning(taskData);
              }, 300);
            },
          },
        ],
      });
    },
    300,
  );

  /**
   * Post task with same time checking
   * @param {Function} [callback] - Optional callback to execute on success
   * @returns {Promise<any>} Promise that resolves with booking result
   */
  const postTask = useCallback(async (): Promise<any> => {
    try {
      const currentState = usePostTaskStore.getState();
      const { date, service, address } = currentState;

      if (!date || !service?._id || !address) {
        return;
      }

      const timezone = DateTimeHelpers.getTimezoneByCity(address.city);

      // check task same time
      checkTaskSameTime(
        {
          taskDate: DateTimeHelpers.formatToString({
            date: date,
            timezone,
          }),
          serviceId: service?._id || '',
        },
        {
          onSuccess: (isExistTask) => {
            executeTaskPosting({ isExistTask });
          },
        },
      );
    } catch (error) {
      handleError(error as IApiError);
    }
  }, [executeTaskPosting, checkTaskSameTime]);

  /**
   * Set sofa cleaning data with validation
   * @param {SetSofaCleaningDataParams} params - Sofa cleaning parameters
   * @returns {Promise<void>} Promise that resolves when data is updated
   */
  const setSofaCleaningData = useCallback(
    async (params: SetSofaCleaningDataParams): Promise<void> => {
      try {
        // Validate parameters
        if (!validateSofaCleaningParams(params)) {
          return;
        }

        const currentState = usePostTaskStore.getState();
        const { sofa } = currentState;

        const updatedSofaData = updateSofaCleaningData(sofa, params);
        await setSofaData(updatedSofaData);
        await getPrice();
      } catch (error) {
        handleError(error as IApiError);
      }
    },
    [getPrice, setSofaData],
  );

  /**
   * Set bed data with validation
   */
  const setBedData = useCallback(
    async (bedData: BedItem): Promise<void> => {
      try {
        // Validate bed data
        if (!validateBedData(bedData)) {
          return;
        }

        const currentState = usePostTaskStore.getState();
        const { bed } = currentState;

        const updatedBedData = updateBedData(bed, bedData);
        await setBed(updatedBedData);
        await getPrice();
      } catch (error) {
        handleError(error as IApiError);
      }
    },
    [getPrice, setBed],
  );

  /**
   * Set curtain data with validation
   */
  const setCurtainData = useCallback(
    async (data: SetCurtainDataParams): Promise<void> => {
      try {
        // Validate curtain data
        if (!validateCurtainData(data)) {
          return;
        }

        const currentState = usePostTaskStore.getState();
        const { curtain } = currentState;

        const updatedCurtainData = updateCurtainData(curtain, data);
        await setCurtain(updatedCurtainData);
        await getPrice();
      } catch (error) {
        handleError(error as IApiError);
      }
    },
    [getPrice, setCurtain],
  );

  /**
   * Change service option for curtain with validation
   */
  const changeServiceOption = useCallback(
    async (data: ChangeServiceOptionParams): Promise<void> => {
      try {
        if (!data.name || !data.serviceOption) {
          return;
        }

        const currentState = usePostTaskStore.getState();
        const { curtain } = currentState;

        const updatedCurtainData = updateCurtainServiceOption(curtain, data);
        await setCurtain(updatedCurtainData);
        await getPrice();
      } catch (error) {
        handleError(error as IApiError);
      }
    },
    [getPrice, setCurtain],
  );

  /**
   * Update carpet type helper function
   */
  const updateNewCarpetType = useCallback(
    (newCarpet: CarpetItem, type: CarpetType) => {
      // Update new type
      const newType = newCarpet?.type || [];
      const indexType = findIndex(
        newType,
        (e: CarpetType) => e.name === type.name,
      );
      // Type has exist
      if (indexType !== -1) {
        if (type?.quantity === 0) {
          newType.splice(indexType, 1);
        } else {
          newType[indexType] = type;
        }
      } else {
        // Push new item to array
        newType.push(type);
      }

      if (newType?.length === 0) {
        return { indexType: indexType };
      }

      return { newType: newType };
    },
    [],
  );

  /**
   * Set carpet data with proper typing and validation
   */
  const setCarpetData = useCallback(
    async (data: CarpetDataParams): Promise<void> => {
      try {
        const { type, name, text, quantityCustomArea, customArea } = data;

        if (!name || !text) {
          return;
        }

        const { carpet } = usePostTaskStore.getState();
        const newCarpet = cloneDeep(carpet);
        const index = findIndex(newCarpet, (e: CarpetItem) => e.name === name);

        // Data exist
        if (index !== -1) {
          if (!isNaN(quantityCustomArea || 0) || customArea) {
            if (quantityCustomArea && customArea) {
              newCarpet[index].customArea = customArea;
              newCarpet[index].quantityCustomArea = quantityCustomArea;
            } else {
              if (!newCarpet[index].type) {
                newCarpet.splice(index, 1);
              } else {
                delete newCarpet[index].quantityCustomArea;
                delete newCarpet[index].customArea;
              }
            }
          }

          if (type) {
            const newCarpetItem = Object.assign({}, newCarpet[index]);
            const result = updateNewCarpetType(newCarpetItem, type);

            // Update new type
            if (result?.newType && result.newType.length > 0) {
              newCarpet[index].type = result.newType;
            }

            if (
              result?.indexType !== -1 &&
              (!newCarpetItem?.type || newCarpetItem.type.length <= 0) &&
              !newCarpetItem.quantityCustomArea
            ) {
              newCarpet.splice(index, 1);
            } else if (
              result?.indexType !== -1 &&
              (!newCarpetItem?.type || newCarpetItem.type.length <= 0)
            ) {
              delete newCarpet[index].type;
            }
          }
        } else {
          const objNewCarpet: CarpetItem = {
            name,
            text,
          };

          if (quantityCustomArea && customArea) {
            objNewCarpet.customArea = customArea;
            objNewCarpet.quantityCustomArea = quantityCustomArea;
          }

          if (type) {
            objNewCarpet.type = [type];
          }

          if (objNewCarpet.quantityCustomArea || objNewCarpet?.type) {
            newCarpet.push(objNewCarpet);
          }
        }

        await setCarpet(newCarpet);
        await getPrice();
      } catch (error) {
        handleError(error as IApiError);
      }
    },
    [getPrice, setCarpet, updateNewCarpetType],
  );

  // Memoize the return object to prevent unnecessary re-renders
  return useMemo(
    () => ({
      getPrice,
      postTask,
      setSofaCleaningData,
      setBedData,
      setCurtainData,
      changeServiceOption,
      setCarpetData,
    }),
    [
      getPrice,
      postTask,
      setSofaCleaningData,
      setBedData,
      setCurtainData,
      changeServiceOption,
      setCarpetData,
    ],
  );
};
