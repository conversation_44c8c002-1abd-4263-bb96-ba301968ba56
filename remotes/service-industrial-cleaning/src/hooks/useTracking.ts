import { useCallback } from 'react';
import {
  DateTimeHelpers,
  getTextWithLocale,
  IEventTaskAbandoned,
  IEventTaskPostSuccess,
  SERVICES,
  TrackingActions,
  TrackingPostTaskStep,
  TrackingScreenNames,
  TrackingServices,
  TypeFormatDate,
} from '@btaskee/design-system';

import { usePostTaskStore } from '@stores';

/**
 * Industrial-Cleaning Service Tracking Hook
 *
 * Provides comprehensive tracking functionality for industrial-cleaning service following
 * the multi-provider tracking architecture with provider-agnostic interface.
 *
 * Features:
 * - Screen view tracking
 * - User action tracking (back, next, changes)
 * - Task abandonment tracking
 * - Task success tracking
 * - Industrial cleaning specific data tracking (area, space, customArea, duration, homeType, servicesAddOn)
 * - App state change tracking
 * - Navigation tracking patterns
 */
export const useTracking = () => {
  const { setStepPostTask, setIsBookedTask } = usePostTaskStore();

  /**
   * Track back navigation from choose service screen (Step 2)
   * Includes current industrial cleaning configuration data
   */
  const trackingActionChooseService = useCallback((action: TrackingActions) => {
    const currentState = usePostTaskStore.getState();
    const { duration, area, space, customArea, homeType, servicesAddOn } =
      currentState;

    TrackingServices.trackingServiceClick({
      screenName: TrackingScreenNames.DetailInformation,
      serviceName: SERVICES.INDUSTRIAL_CLEANING,
      action,
      additionalInfo: {
        duration,
        area: area,
        space: space,
        customArea: customArea,
        homeType: homeType,
        servicesAddOn: servicesAddOn?.map((service: any) =>
          getTextWithLocale(service.text || service.name),
        ),
      },
    });
  }, []);

  /**
   * Track area change action
   * Tracks changes to area selection
   */
  const trackingChangeArea = useCallback(
    ({ oldArea, newArea }: { oldArea?: any; newArea: any }) => {
      TrackingServices.trackingServiceClick({
        screenName: TrackingScreenNames.DetailInformation,
        serviceName: SERVICES.INDUSTRIAL_CLEANING,
        action: TrackingActions.ChangeOptions,
        additionalInfo: {
          oldArea,
          newArea,
        },
      });
    },
    [],
  );

  /**
   * Track space change action
   * Tracks changes to space selection
   */
  const trackingChangeSpace = useCallback(
    ({ oldSpace, newSpace }: { oldSpace?: any; newSpace: any }) => {
      const currentState = usePostTaskStore.getState();
      const { service } = currentState;
      TrackingServices.trackingServiceClick({
        screenName: TrackingScreenNames.DetailInformation,
        serviceName: SERVICES.INDUSTRIAL_CLEANING,
        action: TrackingActions.ChangeOptions,
        isTetBooking: service?.isTet,
        additionalInfo: {
          oldSpace,
          newSpace,
        },
      });
    },
    [],
  );

  /**
   * Track custom area change action
   * Tracks changes to custom area input
   */
  const trackingChangeCustomArea = useCallback(
    ({
      oldCustomArea,
      newCustomArea,
    }: {
      oldCustomArea?: string;
      newCustomArea: string;
    }) => {
      TrackingServices.trackingServiceClick({
        screenName: TrackingScreenNames.DetailInformation,
        serviceName: SERVICES.INDUSTRIAL_CLEANING,
        action: TrackingActions.ChangeOptions,
        additionalInfo: {
          oldCustomArea,
          newCustomArea,
        },
      });
    },
    [],
  );

  /**
   * Track home type change action
   * Tracks changes to home type selection
   */
  const trackingChangeHomeType = useCallback(
    ({ oldHomeType, newHomeType }: { oldHomeType?: any; newHomeType: any }) => {
      TrackingServices.trackingServiceClick({
        screenName: TrackingScreenNames.DetailInformation,
        serviceName: SERVICES.INDUSTRIAL_CLEANING,
        action: TrackingActions.ChangeOptions,
        additionalInfo: {
          oldHomeType,
          newHomeType,
        },
      });
    },
    [],
  );

  /**
   * Track add-on services change action
   * Tracks changes to additional services selection
   */
  const trackingChangeServicesAddOn = useCallback((servicesAddOn: any[]) => {
    TrackingServices.trackingServiceClick({
      screenName: TrackingScreenNames.DetailInformation,
      serviceName: SERVICES.INDUSTRIAL_CLEANING,
      action: TrackingActions.ChangeOptions,
      additionalInfo: {
        servicesAddOn: servicesAddOn?.map((service: any) =>
          getTextWithLocale(service.text || service.name),
        ),
      },
    });
  }, []);

  /**
   * Track back/next actions from choose date time screen (Step 3)
   * Includes comprehensive industrial cleaning service data
   */
  const trackingActionChooseDateTime = useCallback(
    ({ action }: { action: TrackingActions }) => {
      const currentState = usePostTaskStore.getState();
      const {
        duration,
        date,
        area,
        space,
        customArea,
        homeType,
        servicesAddOn,
        note,
      } = currentState;

      TrackingServices.trackingServiceClick({
        screenName: TrackingScreenNames.ChooseWorkingTime,
        serviceName: SERVICES.INDUSTRIAL_CLEANING,
        action,
        additionalInfo: {
          duration,
          date: date
            ? DateTimeHelpers.formatToString({
                date: date,
                typeFormat: TypeFormatDate.HourMinuteDate,
              })
            : null,
          area: area,
          space: space,
          customArea: customArea,
          homeType: homeType,
          servicesAddOn: servicesAddOn?.map((service: any) =>
            getTextWithLocale(service.text || service.name),
          ),
          note: note,
        },
      });
    },
    [],
  );

  /**
   * Track screen view for Choose Date Time screen (matches service-cleaning pattern)
   */
  const trackingChooseDateTimeScreenView = useCallback(() => {
    TrackingServices.trackingServiceView({
      screenName: TrackingScreenNames.ChooseWorkingTime,
      serviceName: SERVICES.INDUSTRIAL_CLEANING,
      entryPoint: TrackingScreenNames.DetailInformation,
    });
  }, []);

  /**
   * Track screen view for Choose Service screen (matches service-cleaning pattern)
   * Used for DetailInformation screen tracking
   */
  const trackingChooseServiceScreenView = useCallback((entryPoint: string) => {
    TrackingServices.trackingServiceView({
      serviceName: SERVICES.INDUSTRIAL_CLEANING,
      screenName: TrackingScreenNames.DetailInformation,
      entryPoint: entryPoint,
    });
  }, []);

  /**
   * Track task abandonment with comprehensive industrial cleaning data
   * Called when user exits app, navigates back, or abandons the booking flow
   */
  const trackingPostTaskAbandoned = useCallback((action: TrackingActions) => {
    const currentState = usePostTaskStore.getState();
    const {
      service,
      isBookedTask,
      price,
      address,
      duration,
      date,
      promotion,
      stepPostTask,
    } = currentState;

    setStepPostTask(TrackingPostTaskStep.STEP_2);

    // If the task has been booked, the event will not be recorded
    if (isBookedTask) {
      return setIsBookedTask(false);
    }

    const params: IEventTaskAbandoned = {
      action: action,
      step: stepPostTask,
      serviceId: service?._id,
      serviceName: SERVICES.INDUSTRIAL_CLEANING,
      price: price?.finalCost,
      duration: duration,
      address: address?.address,
      district: address?.district,
      city: address?.city,
      country: address?.country,
      date: date,
      promotionCode: promotion?.code,
    };
    TrackingServices.trackingTaskAbandoned(params);
  }, []);

  /**
   * Track screen view for Confirm Payment screen
   */
  const trackingConfirmPaymentScreenView = useCallback(() => {
    TrackingServices.trackingServiceView({
      screenName: TrackingScreenNames.ConfirmPayment,
      serviceName: SERVICES.INDUSTRIAL_CLEANING,
      entryPoint: TrackingScreenNames.ChooseWorkingTime,
    });
  }, []);

  /**
   * Track back/next actions from confirm payment screen (Step 4)
   * Includes payment method and comprehensive service data
   */
  const trackingActionConfirmPayment = useCallback(
    ({ action }: { action: TrackingActions }) => {
      const currentState = usePostTaskStore.getState();
      const {
        paymentMethod,
        address,
        promotion,
        isBookedTask,
        area,
        space,
        customArea,
        homeType,
        servicesAddOn,
      } = currentState;

      if (isBookedTask) {
        return null;
      }

      TrackingServices.trackingServiceClick({
        screenName: TrackingScreenNames.ConfirmPayment,
        serviceName: SERVICES.INDUSTRIAL_CLEANING,
        action,
        additionalInfo: {
          phoneNumber: address?.phoneNumber,
          contactName: address?.contact,
          paymentMethod: {
            method: paymentMethod?.name,
            promotion: promotion?.code,
          },
          area: area,
          space: space,
          customArea: customArea,
          homeType: homeType,
          servicesAddOn: servicesAddOn?.map((service: any) =>
            getTextWithLocale(service.text || service.name),
          ),
        },
      });
    },
    [],
  );

  /**
   * Track successful task posting with comprehensive industrial cleaning data
   * Called when user successfully completes the booking
   */
  const trackingPostTaskSuccess = useCallback(() => {
    const currentState = usePostTaskStore.getState();
    const {
      service,
      duration,
      date,
      address,
      promotion,
      price,
      area,
      space,
      customArea,
      homeType,
      servicesAddOn,
      note,
    } = currentState;

    const params: IEventTaskPostSuccess = {
      serviceId: service?._id,
      serviceName: SERVICES.INDUSTRIAL_CLEANING,
      duration: duration,
      address: address?.address,
      district: address?.district,
      city: address?.city,
      country: address?.country,
      date: date,
      promotionCode: promotion?.code,
      taskValue: price?.finalCost,
      // Industrial cleaning specific data
      additionalInfo: {
        area: area,
        space: space,
        customArea: customArea,
        homeType: homeType,
        servicesAddOn: servicesAddOn?.map((service: any) =>
          getTextWithLocale(service.text || service.name),
        ),
        note: note,
      },
    };
    TrackingServices.trackingTaskPostSuccess(params);
  }, []);

  return {
    trackingActionChooseService,
    trackingChangeArea,
    trackingChangeSpace,
    trackingChangeCustomArea,
    trackingChangeHomeType,
    trackingChangeServicesAddOn,
    trackingActionChooseDateTime,
    trackingChooseDateTimeScreenView,
    trackingChooseServiceScreenView,
    trackingPostTaskAbandoned,
    trackingConfirmPaymentScreenView,
    trackingActionConfirmPayment,
    trackingPostTaskSuccess,
  };
};
