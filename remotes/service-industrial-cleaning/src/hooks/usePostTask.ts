import { useCallback } from 'react';
import {
  <PERSON><PERSON>,
  DateT<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Endpoint<PERSON>eys,
  getPhoneNumber,
  handleError,
  IApiError,
  IDate,
  IndustrialCleaningRouteName,
  PaymentService,
  PostTaskHelpers,
  useApiMutation,
  useAppStore,
  usePostTaskAction,
  useUserStore,
} from '@btaskee/design-system';
import { IDataBooking, IParamsGetPrice, NameHomeTypeIC } from '@types';
import { debounce, isEmpty } from 'lodash-es';

import { imgHouseMoving, imgServicedApartment } from '@images';
import { usePostTaskStore } from '@stores';

import { useAppNavigation } from './useAppNavigation';
import { useI18n } from './useI18n';

// Constants

// Utility functions
const createTaskTimezone = (address: any) =>
  DateTimeHelpers.getTimezoneByCity(address?.city);

const formatTaskDate = (date: any, timezone: string) =>
  DateTimeHelpers.formatToString({ timezone, date });

const createTaskPlace = (address: any) => ({
  country: address?.country,
  city: address?.city,
  district: address?.district,
});

export const usePostTask = () => {
  // Store hooks
  const {
    setPrice,
    setLoadingPrice,
    setLoadingPostTask,
    setDuration,
    resetPostTask,
    setIsBookedTask,
    reset,
    service,
    address,
    homeType,
    servicesAddOn,
  } = usePostTaskStore();
  const { user } = useUserStore();
  const { isoCode } = useAppStore();
  const { handlePostTaskError } = usePostTaskAction();

  // API mutations
  const { mutate: getPriceIndustrialCleaning } = useApiMutation({
    key: EndpointKeys.getPriceIndustrialCleaning,
  });
  const { mutate: postTaskIndustrialCleaning } = useApiMutation({
    key: EndpointKeys.postTaskIndustrialCleaning,
  });
  const { mutate: checkTaskSameTime } = useApiMutation({
    key: EndpointKeys.checkTaskSameTime,
  });

  // Navigation and i18n
  const navigation = useAppNavigation();
  const { t } = useI18n();

  const buildPricingData = useCallback((): {
    task: IParamsGetPrice['task'];
    service: { _id: string };
    isoCode: string;
  } | null => {
    const currentState = usePostTaskStore.getState();
    const { date, duration, paymentMethod, promotion } = currentState;

    if (!address || !date || isEmpty(homeType)) {
      return null;
    }

    const timezone = createTaskTimezone(address);

    const task: IParamsGetPrice['task'] = {
      timezone,
      date: formatTaskDate(date, timezone),
      autoChooseTasker: true,
      taskPlace: createTaskPlace(address),
      homeType: address?.homeType,
      duration,
      detailIndustrialCleaning: {
        homeType,
        services: servicesAddOn,
      },
      // Add payment method and promotion using PostTaskHelpers
      ...PostTaskHelpers.formatDataToParams({ paymentMethod, promotion }),
    };

    return {
      task,
      service: { _id: service?._id || '' },
      isoCode: isoCode || '',
    };
  }, [address, homeType, servicesAddOn, service?._id]);

  const getPrice = useCallback(async () => {
    const data = buildPricingData();

    if (!data) {
      setPrice(null);
      return;
    }

    setLoadingPostTask(true);
    setLoadingPrice(true);

    try {
      await getPriceIndustrialCleaning(data, {
        onSuccess: (result: any) => {
          setPrice(result as any);
          setDuration(result?.duration);
        },
        onError: (error: IApiError) => {
          handleError(error);
          setPrice(null);
        },
      });
    } finally {
      setLoadingPrice(false);
    }
  }, [
    buildPricingData,
    getPriceIndustrialCleaning,
    setPrice,
    setDuration,
    setLoadingPostTask,
    setLoadingPrice,
  ]);

  const buildPostTaskData = useCallback((): IDataBooking => {
    const currentState = usePostTaskStore.getState();

    const {
      date,
      duration,
      paymentMethod,
      isApplyNoteForAllTask,
      note,
      isFavouriteTasker,
      relatedTask,
      promotion,
    } = currentState;

    const timezone = createTaskTimezone(address);

    const task: IDataBooking = {
      address: address?.address,
      contactName: address?.contact || user?.name,
      lat: address?.lat,
      lng: address?.lng,
      phone: getPhoneNumber(
        address?.phoneNumber || user?.phone || '',
        address?.countryCode || user?.countryCode || '',
      ),
      countryCode: address?.countryCode || user?.countryCode,
      description: address?.description,
      askerId: user?._id,
      autoChooseTasker: true,
      date: formatTaskDate(date, timezone),
      timezone,
      deviceInfo: DeviceHelper.getDeviceInfo(),
      duration,
      homeType: address?.homeType,
      houseNumber: address?.description,
      isoCode,
      serviceId: service?._id,
      taskPlace: {
        ...createTaskPlace(address),
        ...(address?.isAddressMaybeWrong && { isAddressMaybeWrong: true }),
      },
      isSendToFavTaskers: isFavouriteTasker,
      updateTaskNoteToUser: isApplyNoteForAllTask,
      shortAddress: address?.shortAddress,
      detailIndustrialCleaning: {
        homeType,
        services: servicesAddOn,
      },
      // Add payment method and promotion using PostTaskHelpers
      ...PostTaskHelpers.formatDataToParams({ paymentMethod, promotion }),
    };

    // Add optional fields
    if (service?.isTet) {
      task.isTetBooking = true;
    }

    if (note?.trim()) {
      task.taskNote = note.trim();
    }

    if (!isEmpty(relatedTask?.relatedTaskId)) {
      task.source = {
        from: relatedTask?.service?.name,
        taskId: relatedTask?.relatedTaskId,
      };
    }

    return task;
  }, [address, homeType, servicesAddOn, service]);

  const executePostTask = useCallback(async (): Promise<any> => {
    const dataTask = buildPostTaskData();

    postTaskIndustrialCleaning(dataTask, {
      onSuccess: async (result: any) => {
        setLoadingPostTask(false);
        setIsBookedTask(true);
        const data = result?.data || result;
        if (data?.bookingId) {
          await PaymentService.onPostTaskSuccess({
            bookingId: data.bookingId,
            isPrepayment: data.isPrepayment,
          });
        }
        reset();
      },
      onError: (error: IApiError) => {
        setLoadingPostTask(false);
        handlePostTaskError(error);
      },
    });
  }, [
    buildPostTaskData,
    postTaskIndustrialCleaning,
    reset,
    setIsBookedTask,
    setLoadingPostTask,
    handlePostTaskError,
  ]);

  /**
   * @description function booking task
   * @param payload {{object}} data of task
   */
  const _addTask = debounce(
    async ({ isExistTask }: { isExistTask: boolean }) => {
      // time ok
      if (isExistTask) {
        // call api book task
        executePostTask();
        return;
      }

      // same time, alert for user
      return Alert.alert.open({
        title: t('DIALOG_TITLE_INFORMATION'),
        message: t('TASK_SAME_TIME_MESSAGE'),
        actions: [
          { text: t('CLOSE'), style: 'cancel' },
          {
            text: t('OK'),
            onPress: async () => {
              // wait modal close
              setTimeout(async () => {
                executePostTask();
              }, 300);
            },
          },
        ],
      });
    },
    300,
  );

  const postTask = useCallback(async () => {
    const currentState = usePostTaskStore.getState();
    const { date } = currentState;
    const timezone = createTaskTimezone(address);

    setLoadingPostTask(true);

    // Check for task time conflicts
    checkTaskSameTime(
      {
        taskDate: DateTimeHelpers.formatToString({
          date: date as IDate,
          timezone,
        }),
        serviceId: service?._id || '',
      },
      {
        onSuccess: (data) => {
          _addTask({ isExistTask: !data });
        },
      },
    );

    // Handle same time conflict
  }, [_addTask, address, checkTaskSameTime, service?._id, setLoadingPostTask]);

  const getImageHomeType = useCallback((nameHomeType?: NameHomeTypeIC) => {
    const imageMap = {
      [NameHomeTypeIC.HOME]: imgHouseMoving,
      [NameHomeTypeIC.OFFICE]: imgServicedApartment,
    };

    return nameHomeType ? imageMap[nameHomeType] || null : null;
  }, []);

  const updateLocation = useCallback(() => {
    if (homeType.name) {
      Alert.alert.open({
        title: t('INDUSTRIAL.CHANGE_ADDRESS'),
        message: t('INDUSTRIAL.CHANGE_ADDRESS_DES'),
        actions: [
          { text: t('CLOSE'), style: 'cancel' },
          {
            text: t('CONTINUE'),
            onPress: () => {
              navigation.navigate(IndustrialCleaningRouteName.ChooseAddress);
              resetPostTask();
            },
          },
        ],
      });
      return;
    }
    navigation.navigate(IndustrialCleaningRouteName.ChooseAddress);
  }, [homeType.name, t, navigation, resetPostTask]);

  const getServiceSettings = useCallback(() => {
    const citySettings = service?.detailService?.industrialCleaning?.city?.find(
      (e) => e.name === address?.city,
    );

    return {
      homeTypes: citySettings?.homeType || [],
      services: citySettings?.services || [],
    };
  }, [address?.city, service?.detailService?.industrialCleaning?.city]);

  const getListHomeTypeSetting = useCallback(() => {
    const { homeTypes } = getServiceSettings();
    return homeTypes;
  }, [getServiceSettings]);

  const getListServicesAddOnSetting = useCallback(() => {
    const { services } = getServiceSettings();
    return services;
  }, [getServiceSettings]);

  const getListTypeByHomeTypeSetting = useCallback(() => {
    const homeTypes = getListHomeTypeSetting();
    return homeTypes?.find((item) => item.name === homeType.name)?.type;
  }, [getListHomeTypeSetting, homeType.name]);

  const getListArea = useCallback(() => {
    const types = getListTypeByHomeTypeSetting();
    return types?.find((e) => e.name === homeType.type?.name)?.area;
  }, [getListTypeByHomeTypeSetting, homeType.type?.name]);

  return {
    getPrice,
    postTask,
    updateLocation,
    getListArea,
    getListTypeByHomeTypeSetting,
    getListServicesAddOnSetting,
    getListHomeTypeSetting,
    getImageHomeType,
  };
};
