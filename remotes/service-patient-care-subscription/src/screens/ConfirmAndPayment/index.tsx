import React, { useCallback, useEffect } from 'react';
import {
  BlockView,
  BookingButton,
  Colors,
  LocationPostTask,
  PAYMENT_METHOD,
  PaymentDetail,
  PaymentMethodBlock,
  RefundNote,
  ScrollView,
  Spacing,
  TrackingActions,
  TrackingPostTaskStep,
  TYPE_OF_PAYMENT,
  usePostTaskAction,
} from '@btaskee/design-system';
import { isEmpty } from 'lodash-es';

import { TaskDetail } from '@components';
import { useAppNavigation, usePostTask, useTracking } from '@hooks';
import { usePostTaskStore } from '@stores';

import styles from './styles';

export const ConfirmAndPayment = () => {
  const navigation = useAppNavigation();
  const {
    address,
    homeNumber,
    setAddress,
    price,
    setPromotion,
    paymentMethod,
    promotion,
    setPaymentMethod,
    setStepPostTask,
    isBookedTask,
    date,
  } = usePostTaskStore();
  const { postTask } = usePostTask();
  const { getPaymentMethodWhenBooking } = usePostTaskAction();

  // Tracking hooks
  const {
    trackingConfirmPaymentScreenView,
    trackingNextBackActionConfirmPayment,
  } = useTracking();

  useEffect(() => {
    // Set tracking step for confirm and payment screen
    setStepPostTask(TrackingPostTaskStep.ConfirmAndPayment);

    // Track screen view - matches legacy subscription-step-4 pattern
    trackingConfirmPaymentScreenView();
  }, []);

  useEffect(() => {
    const paymentMethodDefault = getPaymentMethodWhenBooking({
      promotion,
      price: price,
      paymentMethod: paymentMethod,
    });
    if (!isEmpty(paymentMethodDefault)) {
      setPaymentMethod(paymentMethodDefault);
    }
  }, [
    getPaymentMethodWhenBooking,
    promotion,
    price,
    paymentMethod,
    setPaymentMethod,
  ]);

  const handleTrackingBack = useCallback(() => {
    if (isBookedTask) {
      return null;
    }
    trackingNextBackActionConfirmPayment({
      action: TrackingActions.Back,
    });
  }, [isBookedTask, trackingNextBackActionConfirmPayment]);

  useEffect(() => {
    const unsubscribe = navigation.addListener('beforeRemove', () => {
      handleTrackingBack();
    });

    return unsubscribe; // Cleanup listener khi component unmount
  }, [navigation, handleTrackingBack]);

  const handlePostTask = () => {
    trackingNextBackActionConfirmPayment({
      action: TrackingActions.Next,
    });
    postTask();
  };

  return (
    <BlockView
      flex
      backgroundColor={Colors.neutralBackground}
    >
      <ScrollView
        testID="scrollStep4"
        scrollIndicatorInsets={{ right: 1 }}
        contentContainerStyle={styles.content}
        showsVerticalScrollIndicator={false}
      >
        <LocationPostTask
          address={address}
          homeNumber={homeNumber}
          setAddress={setAddress}
        />

        <TaskDetail />

        <PaymentDetail price={price} />

        <PaymentMethodBlock
          type={TYPE_OF_PAYMENT.subscription}
          horizontal
          paymentMethodOptions={{
            currentPaymentMethod: paymentMethod,
            blackList: [PAYMENT_METHOD.cash],
          }}
          promotionOptions={{
            currentPromotion: promotion,
            taskCost: price?.cost,
            taskDate: date,
            taskPlace: {
              country: address?.country,
              city: address?.city,
              district: address?.district,
            },
          }}
          onChangePaymentMethod={setPaymentMethod}
          onChangePromotion={setPromotion}
        />

        <RefundNote />
      </ScrollView>
      <BookingButton
        price={price}
        onPostTask={handlePostTask}
      />
    </BlockView>
  );
};
