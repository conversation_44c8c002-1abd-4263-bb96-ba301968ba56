import {
  DateT<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Endpoint<PERSON>eys,
  getPhoneNumber,
  handleError,
  IApiError,
  IDate,
  IPriceSub,
  PaymentService,
  PostTaskHelpers,
  useApiMutation,
  useAppLoadingStore,
  useAppStore,
  usePostTaskAction,
  useUserStore,
} from '@btaskee/design-system';
import { isEmpty } from 'lodash-es';

import { usePostTaskStore } from '@stores';

import { useTracking } from './useTracking';

export const usePostTask = () => {
  const { isoCode } = useAppStore();
  const { user } = useUserStore();
  const { showLoading, hideLoading } = useAppLoadingStore();
  const { trackingSubscriptionSuccess } = useTracking();
  const { handlePostTaskError } = usePostTaskAction();

  const { service, setPrice, setLoadingPrice, resetState, setIsBookedTask } =
    usePostTaskStore();

  const { mutate: getPricePatientCareSubscription } = useApiMutation({
    key: EndpointKeys.getPricePatientCareSubscription,
    options: {
      onMutate: () => {
        setLoadingPrice(true);
        showLoading();
      },
      onSettled: () => {
        setLoadingPrice(false);
        hideLoading();
      },
    },
  });

  const { mutate: postTaskSubscriptionCleaning } = useApiMutation({
    key: EndpointKeys.postTaskSubscriptionCleaning,
    options: {
      onSuccess: async (data) => {
        const paymentMethod = usePostTaskStore.getState().paymentMethod;
        trackingSubscriptionSuccess();

        resetState();
        setIsBookedTask(true);

        await PaymentService.onPostTaskSubscriptionSuccess({
          paymentMethod,
          data: data?.data,
        });
      },
      onError: (error: IApiError) => {
        handlePostTaskError(error);
      },
      onMutate: () => {
        showLoading();
      },
      onSettled() {
        hideLoading();
      },
    },
  });

  const buildPricingData = async () => {
    const currentState = usePostTaskStore.getState();
    const {
      address,
      duration,
      addons,
      schedule,
      month,
      promotion,
      paymentMethod,
    } = currentState;

    if (isEmpty(schedule) || !duration || !month) {
      return null;
    }

    const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);
    const schedules = schedule.map((e) =>
      DateTimeHelpers.formatToString({ timezone, date: e }),
    );

    const params: any = {
      schedule: schedules,
      timezone,
      service: {
        _id: service?._id || '',
      },
      task: {
        taskPlace: {
          country: address?.country,
          city: address?.city,
          district: address?.district,
        },
        duration: duration,
        autoChooseTasker: true,
        homeType: address?.homeType,
      },
      month,
      isoCode: isoCode || '',
      ...PostTaskHelpers.formatDataToParams({ paymentMethod, promotion }),
    };

    if (!isEmpty(addons)) {
      params.task.addons = addons;
    }

    return params;
  };

  const getPrice = async (): Promise<void> => {
    const pricingData = await buildPricingData();

    if (!pricingData) {
      setPrice(null);
      return;
    }

    setLoadingPrice(true);

    getPricePatientCareSubscription(pricingData, {
      onSuccess: (result: any) => {
        setPrice(result as IPriceSub);
      },
      onError: (error: IApiError) => {
        handleError(error);
        setPrice(null);
      },
    });

    setLoadingPrice(false);
  };

  const buildTaskData = () => {
    const currentState = usePostTaskStore.getState();
    const {
      address,
      homeNumber,
      promotion,
      startDate,
      endDate,
      weekdays,
      duration,
      paymentMethod,
      note,
      schedule,
      month,
      addons,
    } = currentState;

    const isAddressMaybeWrong = Boolean(address?.isAddressMaybeWrong);
    const city = address?.city;
    const timezone = DateTimeHelpers.getTimezoneByCity(city);

    const task = {
      startDate: DateTimeHelpers.formatToString({
        date: startDate as IDate,
        timezone,
      }),
      endDate: DateTimeHelpers.formatToString({
        date: endDate as IDate,
        timezone,
      }),
      timezone,
      taskPlace: {
        country: address?.country,
        city: address?.city,
        district: address?.district,
      },
      weekday: weekdays,
      duration: duration,
      serviceId: service?._id,
      homeType: address?.homeType,
      address: address?.address,
      contactName: address?.contact,
      location: {
        lat: address?.lat,
        lng: address?.lng,
      },
      countryCode: address?.countryCode || user?.countryCode,
      description: homeNumber,
      deviceInfo: DeviceHelper.getDeviceInfo(),
      houseNumber: homeNumber,
      taskNote: note?.trim(),
      isoCode: isoCode || '',
      shortAddress: address?.shortAddress,
      month: month,
      phone: getPhoneNumber(
        address?.phoneNumber || user?.phone || '',
        address?.countryCode || user?.countryCode || '',
      ),
      ...PostTaskHelpers.formatDataToParams({ paymentMethod, promotion }),
    };

    // Add optional fields
    if (schedule && schedule.length > 0) {
      task.schedule = schedule.map((e) =>
        DateTimeHelpers.formatToString({ date: e, timezone }),
      );
    }

    if (isAddressMaybeWrong) {
      task.taskPlace.isAddressMaybeWrong = isAddressMaybeWrong;
    }

    if (!isEmpty(addons)) {
      task.addons = addons;
    }

    return task;
  };

  const executeTaskPosting = async (): Promise<any> => {
    const taskData = buildTaskData();
    return postTaskSubscriptionCleaning(taskData);
  };

  const postTask = async (): Promise<any> => {
    return executeTaskPosting();
  };

  return { getPrice, postTask };
};
