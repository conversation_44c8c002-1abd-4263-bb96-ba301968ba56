import {
  AppStorage,
  createZustand,
  IAddress,
  IDate,
  IPaymentMethodInfo,
  IPrice,
  IPromotion,
  IRelatedTask,
  IService,
  Maybe,
  PaymentService,
  SERVICES,
  TrackingPostTaskStep,
  TYPE_OF_PAYMENT,
} from '@btaskee/design-system';
import { createJSONStorage, persist } from 'zustand/middleware';

interface IState {
  address: Maybe<IAddress>;
  duration: number;
  date: IDate | null;
  note: string;
  isApplyNoteForAllTask: boolean;
  price: IPrice | null;
  service: IService | null;
  paymentMethod?: IPaymentMethodInfo;
  promotion?: IPromotion;
  relatedTask: IRelatedTask | null;
  carpet: any;
  carpetNote: any;
  sofa: any;
  mattress: any;
  curtainDryClean: any;
  curtainWashing: any;
  curtainWashingQty: number;
  cleaningData: any;
  loadingPrice: boolean;
  // Tracking-related properties
  stepPostTask: TrackingPostTaskStep;
  isBookedTask: boolean;
  isFirstOpenService: boolean;
}

interface IActions {
  setSofaData: (sofa: any) => void;
  setMattressData: (mattress: any) => void;
  setCarpetData: (carpet: any) => void;
  setCarpetNote: (carpetNote: any) => void;
  setAddress: (address: IAddress) => void;
  setDuration: (duration: number) => void;
  setDateTime: (date: IDate) => void;
  setNote: (note: string) => void;
  setIsApplyNoteForAllTask: (isApplyNoteForAllTask: boolean) => void;
  setPrice: (price: IPrice | null) => void;
  setPaymentMethod: (paymentMethod?: IPaymentMethodInfo) => void;
  setPromotion: (promotion?: IPromotion) => void;
  setRelatedTask: (relatedTask: IRelatedTask) => void;
  setService: (service: IService) => void;
  setCurtainDryData: (curtainDryClean: any) => void;
  setCurtainWashing: (curtainWashing: any) => void;
  setCurtainWashingQty: (curtainWashingQty: any) => void;
  setLoadingPrice: (loadingPrice: boolean) => void;
  // Tracking-related actions
  setStepPostTask: (stepPostTask: TrackingPostTaskStep) => void;
  setIsBookedTask: (isBookedTask: boolean) => void;
  setIsFirstOpenService: (isFirstOpenService: boolean) => void;
  resetState: () => void;
}

const INITIAL_STATE: IState = {
  address: {},
  duration: 0,
  date: null,
  note: '',
  isApplyNoteForAllTask: false,
  price: null,
  service: null,
  carpet: [],
  carpetNote: '',
  sofa: [],
  cleaningData: {},
  mattress: [],
  curtainDryClean: [],
  curtainWashing: [],
  curtainWashingQty: 0,
  loadingPrice: false,
  paymentMethod: PaymentService.getDefaultPaymentMethod({
    serviceName: SERVICES.SOFA,
    type: TYPE_OF_PAYMENT.bookTask,
  }),
  promotion: undefined,
  relatedTask: null,
  // Tracking-related initial state
  stepPostTask: TrackingPostTaskStep.STEP_2,
  isBookedTask: false,
  isFirstOpenService: true,
};

type SofaCleaningState = IState & IActions;

export const usePostTaskStore = createZustand<SofaCleaningState>()(
  persist(
    (set) => ({
      ...INITIAL_STATE,
      setSofaData: (sofa: any) => set({ sofa: sofa }),
      setMattressData: (mattress: any) => set({ mattress: mattress }),
      setCurtainDryData: (curtainDryClean: any) =>
        set({ curtainDryClean: curtainDryClean }),
      setCurtainWashing: (curtainWashing: any) =>
        set({ curtainWashing: curtainWashing }),
      setCarpetData: (carpet: any) => set({ carpet: carpet }),
      setCarpetNote: (carpetNote: any) => set({ carpetNote: carpetNote }),
      setAddress: (address: IAddress) => set({ address: address }),
      setDuration: (duration: number) => set({ duration: duration }),
      setDateTime: (date: IDate) => set({ date: date }),
      setNote: (note: string) => set({ note: note }),
      setIsApplyNoteForAllTask: (isApplyNoteForAllTask: boolean) =>
        set({ isApplyNoteForAllTask: isApplyNoteForAllTask }),
      setPrice: (price: IPrice | null) => set({ price: price }),
      setPaymentMethod: (paymentMethod?: IPaymentMethodInfo) =>
        set({ paymentMethod: paymentMethod }),
      setPromotion: (promotion?: IPromotion) => set({ promotion: promotion }),
      setRelatedTask: (relatedTask: IRelatedTask) =>
        set({ relatedTask: relatedTask }),
      setService: (service: IService) => set({ service: service }),
      setCleaningData: (data: any) => set({ cleaningData: data }),
      setCurtainDryCleanData: (curtainDryClean: any) =>
        set({ curtainDryClean: curtainDryClean }),
      setCurtainWashingData: (curtainWashing: any) =>
        set({ curtainWashing: curtainWashing }),
      setCurtainWashingQty: (curtainWashingQty: any) =>
        set({ curtainWashingQty: curtainWashingQty }),
      setLoadingPrice: (loadingPrice: boolean) =>
        set({ loadingPrice: loadingPrice }),
      // Tracking-related actions
      setStepPostTask: (stepPostTask: TrackingPostTaskStep) =>
        set({ stepPostTask }),
      setIsBookedTask: (isBookedTask: boolean) => set({ isBookedTask }),
      setIsFirstOpenService: (isFirstOpenService: boolean) =>
        set({ isFirstOpenService }),
      resetState: () =>
        set((state) => ({
          ...INITIAL_STATE,
          isBookedTask: state.isBookedTask,
          isFirstOpenService: state.isFirstOpenService,
        })),
    }),
    {
      name: 'sofa-cleaning-service-storage',
      storage: createJSONStorage(() => AppStorage.zustandStorage),
      version: 1,
      partialize: (state) => ({
        isFirstOpenService: state.isFirstOpenService,
      }),
    },
  ),
);
