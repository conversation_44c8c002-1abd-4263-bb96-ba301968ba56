import {
  Alert,
  DateTimeHelpers,
  DeviceHelper,
  Endpoint<PERSON>eys,
  getPhoneNumber,
  handleError,
  IApiError,
  PaymentService,
  PostTaskHelpers,
  useApiMutation,
  useAppLoadingStore,
  useAppStore,
  usePostTaskAction,
  useUserStore,
} from '@btaskee/design-system';
import { IDataBooking, IParamsGetPrice } from '@types';
import { debounce, isEmpty } from 'lodash-es';

import { usePostTaskStore } from '@stores';

import { useI18n } from './useI18n';
import { useTracking } from './useTracking';

// Types for better type safety
interface DetailSofaData {
  sofa?: any[];
  mattress?: any[];
  curtain?: {
    dryclean?: any[];
    washing?: any[];
  };
  carpet?: any[];
}

// Utility functions for building task data
const buildDetailSofaData = (sofaData: {
  sofa?: any[];
  mattress?: any[];
  curtainDryClean?: any[];
  carpet?: any[];
  curtainWashing?: any;
}): DetailSofaData => {
  const { sofa, mattress, curtainDryClean, carpet, curtainWashing } = sofaData;
  const detailSofaData: DetailSofaData = {};
  const curtain: { dryclean?: any[]; washing?: any[] } = {};

  if (!isEmpty(sofa)) {
    detailSofaData.sofa = sofa;
  }

  if (!isEmpty(mattress)) {
    detailSofaData.mattress = mattress;
  }

  if (!isEmpty(curtainDryClean)) {
    curtain.dryclean = curtainDryClean;
    detailSofaData.curtain = curtain;
  }

  if (!isEmpty(carpet)) {
    detailSofaData.carpet = carpet;
  }

  if (!isEmpty(curtainWashing)) {
    curtain.washing = [curtainWashing];
    detailSofaData.curtain = curtain;
  }

  return detailSofaData;
};

const buildBaseTaskInfo = (
  address: any,
  user: any,
  date: Date,
  duration: number,
  service: any,
  isoCode: string,
  paymentMethod: any,
  promotion: any,
): Partial<IDataBooking> => {
  const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

  return {
    address: address?.address,
    contactName: address?.contact || user?.name,
    lat: address?.lat,
    lng: address?.lng,
    phone: getPhoneNumber(
      address?.phoneNumber || user?.phone || '',
      address?.countryCode || user?.countryCode || '',
    ),
    countryCode: address?.countryCode || user?.countryCode,
    description: address?.description,
    askerId: user?._id,
    autoChooseTasker: true,
    date: DateTimeHelpers.formatToString({ date, timezone }),
    timezone,
    deviceInfo: DeviceHelper.getDeviceInfo(),
    duration,
    homeType: address?.homeType,
    houseNumber: address?.description,
    isoCode,
    serviceId: service?._id,
    taskPlace: {
      country: address?.country,
      city: address?.city,
      district: address?.district,
    },
    shortAddress: address?.shortAddress,
    ...PostTaskHelpers.formatDataToParams({ paymentMethod, promotion }),
  };
};

export const usePostTask = () => {
  const {
    setPrice,
    setLoadingPrice,
    setDuration,
    resetState,
    setIsBookedTask,
  } = usePostTaskStore();
  const { showLoading, hideLoading } = useAppLoadingStore();
  const { trackingPostTaskSuccess } = useTracking();
  const { handlePostTaskError } = usePostTaskAction();
  const { user } = useUserStore();
  const { isoCode } = useAppStore();
  const { t } = useI18n();

  const { mutate: getPriceSofaCleaning } = useApiMutation({
    key: EndpointKeys.getPriceSofaCleaning,
    options: {
      onMutate: () => {
        setLoadingPrice(true);
        showLoading();
      },
      onSettled: () => {
        setLoadingPrice(false);
        hideLoading();
      },
    },
  });

  const { mutate: postTaskSofaCleaning } = useApiMutation({
    key: EndpointKeys.postTaskSofaCleaning,
    options: {
      onSuccess: async (data) => {
        if (data?.bookingId) {
          trackingPostTaskSuccess();
          resetState();
          setIsBookedTask(true);
          await PaymentService.onPostTaskSuccess({
            bookingId: data.bookingId,
            isPrepayment: data.isPrepayment,
          });
        }
      },
      onError: (error: IApiError) => {
        handlePostTaskError(error);
      },
      onMutate: () => {
        showLoading();
      },
      onSettled() {
        hideLoading();
      },
    },
  });

  const { mutate: checkTaskSameTime } = useApiMutation({
    key: EndpointKeys.checkTaskSameTime,
    options: {
      onMutate: () => {
        showLoading();
      },
      onSettled() {
        hideLoading();
      },
    },
  });

  const buildPricingData = (): IParamsGetPrice | null => {
    const currentState = usePostTaskStore.getState();
    const {
      address,
      date,
      duration,
      service,
      paymentMethod,
      sofa,
      mattress,
      curtainDryClean,
      carpet,
      curtainWashing,
      promotion,
    } = currentState;

    if (!address || !date) {
      return null;
    }

    const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);
    const detailSofaData = buildDetailSofaData({
      sofa,
      mattress,
      curtainDryClean,
      carpet,
      curtainWashing,
    });

    // If no sofa cleaning data is selected, return null
    if (isEmpty(detailSofaData)) {
      return null;
    }

    const task: IParamsGetPrice['task'] = {
      timezone,
      date: DateTimeHelpers.formatToString({ timezone, date }),
      autoChooseTasker: true,
      taskPlace: {
        country: address?.country,
        city: address?.city,
        district: address?.district,
      },
      homeType: address?.homeType,
      duration,
      detailSofa: detailSofaData,
      // Add payment method and promotion using PostTaskHelpers
      ...PostTaskHelpers.formatDataToParams({ paymentMethod, promotion }),
    };

    return { task, service: { _id: service?._id }, isoCode: isoCode as string };
  };

  const getPrice = async (): Promise<void> => {
    const pricingData = buildPricingData();

    if (!pricingData) {
      setPrice(null);
      return;
    }

    setLoadingPrice(true);

    await getPriceSofaCleaning(pricingData, {
      onSuccess: (result: any) => {
        setPrice(result);
        setDuration(result?.duration || 0);
      },
      onError: (error: IApiError) => {
        handleError(error);
        setPrice(null);
      },
    });

    setLoadingPrice(false);
  };

  const buildTaskData = (): IDataBooking => {
    const currentState = usePostTaskStore.getState();
    const {
      address,
      date,
      duration,
      service,
      paymentMethod,
      isApplyNoteForAllTask,
      note,
      isFavouriteTasker,
      relatedTask,
      sofa,
      mattress,
      curtainDryClean,
      carpet,
      curtainWashing,
      promotion,
    } = currentState;

    const baseTask = buildBaseTaskInfo(
      address,
      user,
      date,
      duration,
      service,
      isoCode,
      paymentMethod,
      promotion,
    );

    const detailSofaData = buildDetailSofaData({
      sofa,
      mattress,
      curtainDryClean,
      carpet,
      curtainWashing,
    });

    const task: IDataBooking = {
      ...baseTask,
      isSendToFavTaskers: isFavouriteTasker,
      updateTaskNoteToUser: isApplyNoteForAllTask,
      detailSofa: detailSofaData,
    } as IDataBooking;

    // Add optional fields
    if (address?.isAddressMaybeWrong) {
      task.taskPlace.isAddressMaybeWrong = true;
    }

    if (service?.isTet) {
      task.isTetBooking = true;
    }

    if (note?.trim()) {
      task.taskNote = note.trim();
    }

    if (!isEmpty(relatedTask?.relatedTaskId)) {
      task.source = {
        from: relatedTask?.service?.name,
        taskId: relatedTask?.relatedTaskId,
      };
    }

    return task;
  };

  const executeTaskPosting = debounce(
    async ({ isExistTask }: { isExistTask?: boolean }) => {
      const taskData = buildTaskData();

      // time ok
      if (!isExistTask) {
        // call api book task
        postTaskSofaCleaning(taskData);
        return;
      }

      // same time, alert for user
      return Alert.alert.open({
        title: t('DIALOG_TITLE_INFORMATION'),
        message: t('TASK_SAME_TIME_MESSAGE'),
        actions: [
          { text: t('CLOSE'), style: 'cancel' },
          {
            text: t('OK'),
            onPress: async () => {
              // wait modal close
              setTimeout(async () => {
                postTaskSofaCleaning(taskData);
              }, 300);
            },
          },
        ],
      });
    },
    300,
  );

  const postTask = async (): Promise<any> => {
    const currentState = usePostTaskStore.getState();
    const { date, service, address } = currentState;
    const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

    // check task same time
    checkTaskSameTime(
      {
        taskDate: DateTimeHelpers.formatToString({
          date: date,
          timezone,
        }),
        serviceId: service?._id || '',
      },
      {
        onSuccess: (isExistTask) => {
          executeTaskPosting({ isExistTask });
        },
      },
    );
  };

  return { getPrice, postTask };
};
