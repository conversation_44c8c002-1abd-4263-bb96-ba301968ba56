import { useCallback } from 'react';
import {
  DateTimeHelpers,
  IEventTaskAbandoned,
  IEventTaskPostSuccess,
  SERVICES,
  TrackingActions,
  TrackingPostTaskStep,
  TrackingScreenNames,
  TrackingServices,
  TypeFormatDate,
} from '@btaskee/design-system';

import { usePostTaskStore } from '@stores';

/**
 * Sofa Cleaning Service Tracking Hook
 *
 * Provides comprehensive tracking functionality for sofa cleaning service following
 * the multi-provider tracking architecture with provider-agnostic interface.
 *
 * Features:
 * - Screen view tracking
 * - User action tracking (back, next, changes)
 * - Task abandonment tracking
 * - Task success tracking
 * - Sofa cleaning specific data tracking (sofa, mattress, carpet, curtain details)
 */
export const useTracking = () => {
  /**
   * Track back/next actions from sofa info selection screen (Step 2)
   * Includes comprehensive sofa cleaning service data
   */
  const trackingBackNextActionChooseSofaInfo = useCallback(
    (action: TrackingActions) => {
      const currentState = usePostTaskStore.getState();
      const {
        service,
        sofa,
        mattress,
        carpet,
        curtainDryClean,
        curtainWashing,
      } = currentState;

      TrackingServices.trackingServiceClick({
        screenName: TrackingScreenNames.DetailInformation,
        serviceName: SERVICES.SOFA,
        action: action,
        isTetBooking: service?.isTet,
        additionalInfo: {
          sofaDetails: {
            sofa: sofa,
            mattress: mattress,
            carpet: carpet,
            curtainDryClean: curtainDryClean,
            curtainWashing: curtainWashing,
          },
        },
      });
    },
    [],
  );

  /**
   * Track screen view for service introduction
   */
  const trackingServiceIntroductionScreenView = useCallback(
    (entryPoint?: string) => {
      const currentState = usePostTaskStore.getState();
      const { service } = currentState;

      TrackingServices.trackingServiceView({
        screenName: TrackingScreenNames.ServiceIntroduction,
        serviceName: SERVICES.SOFA,
        entryPoint: entryPoint,
        isTetBooking: service?.isTet,
      });
    },
    [],
  );

  /**
   * Track screen view for choose service screen
   */
  const trackingChooseServiceScreenView = useCallback((entryPoint?: string) => {
    const currentState = usePostTaskStore.getState();
    const { service } = currentState;

    TrackingServices.trackingServiceView({
      screenName: TrackingScreenNames.ChooseService,
      serviceName: SERVICES.SOFA,
      entryPoint: entryPoint || TrackingScreenNames.ListOfPlaces,
      isTetBooking: service?.isTet,
    });
  }, []);

  /**
   * Track screen view for sofa info selection screen
   */
  const trackingChooseSofaInfoScreenView = useCallback(() => {
    const currentState = usePostTaskStore.getState();
    const { service } = currentState;

    TrackingServices.trackingServiceView({
      screenName: TrackingScreenNames.DetailInformation,
      serviceName: SERVICES.SOFA,
      isTetBooking: service?.isTet,
    });
  }, []);

  /**
   * Track screen view for date/time selection screen
   */
  const trackingChooseDateTimeScreenView = useCallback(() => {
    const currentState = usePostTaskStore.getState();
    const { service } = currentState;

    TrackingServices.trackingServiceView({
      screenName: TrackingScreenNames.TimeAndOther,
      serviceName: SERVICES.SOFA,
      isTetBooking: service?.isTet,
    });
  }, []);

  /**
   * Track next/back actions on date/time selection screen
   */
  const trackingNextBackActionChooseDateTime = useCallback(
    ({ action }: { action: TrackingActions }) => {
      const currentState = usePostTaskStore.getState();
      const { service, date, address } = currentState;

      TrackingServices.trackingServiceClick({
        screenName: TrackingScreenNames.TimeAndOther,
        serviceName: SERVICES.SOFA,
        action,
        isTetBooking: service?.isTet,
        additionalInfo: {
          date: date
            ? DateTimeHelpers.formatToString({
                date: date,
                typeFormat: TypeFormatDate.DateTimeShort,
              })
            : null,
          address: address?.address,
          district: address?.district,
          city: address?.city,
        },
      });
    },
    [],
  );

  /**
   * Track screen view for confirm payment screen
   */
  const trackingConfirmPaymentScreenView = useCallback(() => {
    const currentState = usePostTaskStore.getState();
    const { service } = currentState;

    TrackingServices.trackingServiceView({
      screenName: TrackingScreenNames.ConfirmPayment,
      serviceName: SERVICES.SOFA,
      isTetBooking: service?.isTet,
    });
  }, []);

  /**
   * Track next/back actions on confirm payment screen
   */
  const trackingNextBackActionConfirmPayment = useCallback(
    ({ action }: { action: TrackingActions }) => {
      const currentState = usePostTaskStore.getState();
      const { service, paymentMethod, address, promotion } = currentState;

      TrackingServices.trackingServiceClick({
        screenName: TrackingScreenNames.ConfirmPayment,
        serviceName: SERVICES.SOFA,
        action,
        isTetBooking: service?.isTet,
        additionalInfo: {
          phoneNumber: address?.phoneNumber,
          contactName: address?.contact,
          paymentMethod: {
            method: paymentMethod?.name,
            promotion: promotion?.code,
          },
        },
      });
    },
    [],
  );

  /**
   * Track task abandonment
   * Called when user exits the booking flow without completing
   */
  const trackingPostTaskAbandoned = useCallback(
    async (action: TrackingActions) => {
      const currentState = usePostTaskStore.getState();
      const {
        service,
        isBookedTask,
        price,
        address,
        date,
        promotion,
        stepPostTask,
        setStepPostTask,
        setIsBookedTask,
      } = currentState;

      setStepPostTask(TrackingPostTaskStep.STEP_2);

      // If the task has been booked, the event will not be recorded
      if (isBookedTask) {
        return setIsBookedTask(false);
      }

      const params: IEventTaskAbandoned = {
        action: action,
        step: stepPostTask,
        serviceId: service?._id,
        serviceName: SERVICES.SOFA,
        price: price?.finalCost,
        address: address?.address,
        district: address?.district,
        city: address?.city,
        country: address?.country,
        date: date || undefined,
        promotionCode: promotion?.code,
        isTetBooking: service?.isTet,
      };
      TrackingServices.trackingTaskAbandoned(params);
    },
    [],
  );

  /**
   * Track successful task posting
   * Called after task is successfully created
   */
  const trackingPostTaskSuccess = useCallback(() => {
    const currentState = usePostTaskStore.getState();
    const { service, date, address, promotion, price } = currentState;

    const params: IEventTaskPostSuccess = {
      serviceId: service?._id,
      serviceName: SERVICES.SOFA,
      address: address?.address,
      district: address?.district,
      city: address?.city,
      country: address?.country,
      date: date || undefined,
      promotionCode: promotion?.code,
      taskValue: price?.finalCost,
      schedule: [], // Sofa cleaning doesn't use recurring schedule
    };
    TrackingServices.trackingTaskPostSuccess(params);
  }, []);

  return {
    trackingBackNextActionChooseSofaInfo,
    trackingServiceIntroductionScreenView,
    trackingChooseServiceScreenView,
    trackingChooseSofaInfoScreenView,
    trackingChooseDateTimeScreenView,
    trackingNextBackActionChooseDateTime,
    trackingConfirmPaymentScreenView,
    trackingNextBackActionConfirmPayment,
    trackingPostTaskAbandoned,
    trackingPostTaskSuccess,
  };
};
