import React, { useCallback, useEffect, useMemo } from 'react';
import {
  Al<PERSON>,
  BlockView,
  DatePicker,
  DateTimeHelpers,
  NotePostTask,
  PostTaskHelpers,
  PriceButton,
  PriceIncrease,
  ScrollView,
  Spacing,
  TimePicker,
  TrackingActions,
  TrackingPostTaskStep,
  useSettingsStore,
} from '@btaskee/design-system';
import { useIsFocused } from '@react-navigation/native';
import { get } from 'lodash-es';

import { useAppNavigation, useChangeData, useI18n, useTracking } from '@hooks';
import { RouteName } from '@navigation/RouteName';
import { usePostTaskStore } from '@stores';

import { styles } from './styles';

// Import component
export const ChooseDateTime = () => {
  const navigation = useAppNavigation();
  const { t } = useI18n();
  const isFocused = useIsFocused();
  const { onChangeDateTime } = useChangeData();
  const {
    address,
    date,
    duration,
    note,
    setNote,
    setIsApplyNoteForAllTask,
    isApplyNoteForAllTask,
    price,
    service,
    setStepPostTask,
  } = usePostTaskStore();
  const { settings } = useSettingsStore();

  const {
    trackingNextBackActionChooseDateTime,
    trackingChooseDateTimeScreenView,
  } = useTracking();

  const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

  // Track screen view on component mount
  useEffect(() => {
    setStepPostTask(TrackingPostTaskStep.STEP_3);
    trackingChooseDateTimeScreenView();
  }, []);

  const onConfirmed = () => {
    trackingNextBackActionChooseDateTime({ action: TrackingActions.Next });
    // check time before 60min
    if (
      !PostTaskHelpers.validateDateTime(
        timezone,
        date,
        settings?.settingSystem?.minPostTaskTime,
      )
    ) {
      return Alert?.alert?.open({
        title: t('DIALOG_TITLE_INFORMATION'),
        message: t('POSTTASK_STEP2_ERROR_TIME', {
          t: settings?.settingSystem?.minPostTaskTime,
        }),
        actions: [{ text: t('PT2_POPUP_ERROR_TIME_CLOSE') }],
      });
    }

    // check posting limit, ex 6AM - 10PM
    const postingLimits = service?.postingLimits;
    if (
      !PostTaskHelpers.checkTimeValidFromService(
        timezone,
        date,
        duration,
        postingLimits,
      )
    ) {
      const postingLimitsFormat = PostTaskHelpers.formatPostingLimits({
        timezone,
        postingLimits,
      });
      return Alert?.alert?.open({
        title: t('DIALOG_TITLE_INFORMATION'),
        message: t('PT2_POPUP_ERROR_TIME_INVALID_CONTENT', {
          from: postingLimitsFormat.from,
          to: postingLimitsFormat.to,
        }),
        actions: [{ text: t('PT2_POPUP_ERROR_TIME_INVALID_CLOSE') }],
      });
    }
    // data ok
    onGotoStep4();
  };

  const onGotoStep4 = () => {
    // data ok
    trackingNextBackActionChooseDateTime({ action: TrackingActions.Next });
    navigation.navigate(RouteName.ConfirmAndPayment);
  };

  // Handle tracking for back navigation
  const handleTrackingActionStep3 = useCallback(
    (action: TrackingActions) => {
      if (!isFocused) {
        return null;
      }
      trackingNextBackActionChooseDateTime({ action });
    },
    [trackingNextBackActionChooseDateTime, isFocused],
  );

  useEffect(() => {
    const unsubscribe = navigation.addListener('beforeRemove', () => {
      handleTrackingActionStep3(TrackingActions.Back);
    });

    return unsubscribe; // Cleanup listener when component unmounts
  }, [navigation, handleTrackingActionStep3]);

  const shouldRenderChooseDateTime = useMemo(() => {
    return (
      <BlockView>
        <DatePicker
          testID="date-picker-sofa-cleaning"
          value={date}
          onChange={onChangeDateTime}
          settingSystem={settings?.settingSystem}
          timezone={timezone}
        />
        <TimePicker
          testID="time-picker-sofa-cleaning"
          value={date}
          onChange={onChangeDateTime}
          settingSystem={settings?.settingSystem}
          timezone={timezone}
        />
      </BlockView>
    );
  }, [date, onChangeDateTime, settings?.settingSystem, timezone]);

  return (
    <BlockView
      inset={['bottom']}
      style={styles.container}>
      <BlockView style={styles.content}>
        <ScrollView
          testID="scrollChooseDateTime"
          contentContainerStyle={styles.containerScroll}
          showsVerticalScrollIndicator={false}>
          {shouldRenderChooseDateTime}
          <PriceIncrease
            price={price}
            address={address}
            service={service}
            increaseReasons={get(price, 'increaseReasons', false)}
            isShow={Boolean(get(price, 'isIncrease', false))}
          />

          <NotePostTask
            setNote={setNote}
            value={note}
            testID="textInputNote"
            service={service}
            isApplyNoteForAllTask={isApplyNoteForAllTask}
            setNoteForAllTask={setIsApplyNoteForAllTask}
            containerStyle={{ marginTop: Spacing.SPACE_24 }}
            placeholder={t('SERVICE_NOTE_CONTENT')}
            title={t('LABEL_NOTE_FOR_TASKER')}
            description={t('TASK_NOTE_DESCRIPTION')}
          />
        </ScrollView>
      </BlockView>

      <PriceButton
        testID="btnNextStep3"
        onPress={onConfirmed}
        fromScreen={service?.name}
        pricePostTask={price}
      />
    </BlockView>
  );
};
