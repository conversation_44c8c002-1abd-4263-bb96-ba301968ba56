import React, { useCallback, useEffect, useState } from 'react';
import { AppState, AppStateStatus } from 'react-native';
import { TabView } from 'react-native-tab-view';
import {
  BlockView,
  checkSupportCity,
  Colors,
  ConditionView,
  CText,
  <PERSON>ceHelper,
  IAddress,
  IconAssets,
  IconImage,
  isIOS,
  PostTaskHelpers,
  PriceButton,
  SERVICES,
  Spacing,
  TouchableOpacity,
  TrackingActions,
} from '@btaskee/design-system';
import { SOFA_TYPE_FROM } from '@constants';
import { useIsFocused } from '@react-navigation/native';
import { find, get, isEmpty } from 'lodash-es';

import { useAppNavigation, useI18n, useTracking } from '@hooks';
import { RouteName } from '@navigation/RouteName';
import { usePostTaskStore } from '@stores';

import { CarpetTab } from './components/CarpetTab';
import { CurtainTab } from './components/CurtainTab';
import { MattressTab } from './components/MattressTab';
import { SofaTab } from './components/SofaTab';
import { styles } from './styles';

// Types
interface ChooseServiceProps {
  previousServiceId?: string;
  resetStateSofaCleaning?: () => void;
}

interface TabRoute {
  key: string;
  name: string;
  title: string;
}

interface ServiceItem {
  quantity: number;
  type?: string;
  name?: string;
  text?: any;
  typeSofa?: ServiceItem[];
  others?: ServiceItem[];
  stool?: ServiceItem[];
}

// Constants
const TAB_KEYS = {
  SOFA: SOFA_TYPE_FROM.SOFA,
  CARPET: SOFA_TYPE_FROM.CARPET,
  MATTRESS: SOFA_TYPE_FROM.MATTRESS,
  CURTAIN: SOFA_TYPE_FROM.CURTAIN,
} as const;

// Utility Functions
const calculateItemQuantity = (items: ServiceItem[]): number => {
  if (!items || items.length === 0) return 0;
  return items.map((item) => item.quantity).reduce((a, b) => a + b, 0);
};

const calculateSofaQuantity = (sofaItems: any[]): number => {
  let total = 0;

  sofaItems.forEach((sofaType) => {
    if (sofaType?.others?.length > 0) {
      total += calculateItemQuantity(sofaType.others);
    }
    if (sofaType?.stool?.length > 0) {
      total += calculateItemQuantity(sofaType.stool);
    }
    if (sofaType?.typeSofa?.length > 0) {
      total += calculateItemQuantity(sofaType.typeSofa);
    }
  });

  return total;
};

const getItemCount = (routeName: string, data: any): number => {
  const { carpet, mattress, sofa, curtainDryClean, curtainWashing } = data;

  switch (routeName) {
    case TAB_KEYS.CARPET:
      return calculateItemQuantity(carpet);
    case TAB_KEYS.MATTRESS:
      return calculateItemQuantity(mattress);
    case TAB_KEYS.SOFA:
      return calculateSofaQuantity(sofa);
    case SOFA_TYPE_FROM.CURTAIN:
      const curtainCount = calculateItemQuantity(curtainDryClean);
      return curtainCount + (!isEmpty(curtainWashing) ? 1 : 0);
    default:
      return 0;
  }
};

// Components
const HeaderTitle: React.FC<{ address: IAddress }> = ({ address }) => (
  <BlockView
    flex
    row
    horizontal
    jBetween>
    <BlockView
      flex
      row>
      <IconImage
        source={IconAssets.icLocation}
        size={24}
        color={Colors.red500}
      />
      <BlockView
        flex
        margin={{ left: Spacing.SPACE_08 }}>
        <CText>{address?.shortAddress}</CText>
        <CText
          bold
          numberOfLines={1}
          margin={{ right: Spacing.SPACE_16 }}>
          {address?.address}
        </CText>
      </BlockView>
    </BlockView>
  </BlockView>
);

const TabBadge: React.FC<{ count: number }> = ({ count }) => (
  <ConditionView
    condition={Boolean(count)}
    viewTrue={
      <BlockView style={styles.badgeContainer}>
        <CText
          bold
          style={styles.badgeTxt}>
          {count}
        </CText>
      </BlockView>
    }
  />
);

const CustomTabBar: React.FC<{
  routes: TabRoute[];
  tabSelected: number;
  onTabPress: (index: number) => void;
  getCount: (routeName: string) => number;
}> = ({ routes, tabSelected, onTabPress, getCount }) => (
  <BlockView
    key="tab-bar"
    center
    row>
    {routes.map((route, index) => {
      const isFocusedTab = tabSelected === index;
      const count = getCount(route.name);

      return (
        <TouchableOpacity
          key={`${route.name}-${index}`}
          testID={`btn-tab-${route.name.toLowerCase()}`}
          activeOpacity={0.6}
          style={[styles.tabBarItem, isFocusedTab && styles.tabBarItemActive]}
          onPress={() => onTabPress(index)}>
          <BlockView
            center
            row>
            <CText
              bold={isFocusedTab}
              numberOfLines={2}
              style={[isFocusedTab ? styles.titleTxtActive : styles.titleTxt]}>
              {route.title}
            </CText>
            <TabBadge count={count} />
          </BlockView>
        </TouchableOpacity>
      );
    })}
  </BlockView>
);

// Main Component
export const ChooseService: React.FC<ChooseServiceProps> = ({
  previousServiceId,
  resetStateSofaCleaning,
}) => {
  const navigation = useAppNavigation();
  const { t } = useI18n();
  const isFocused = useIsFocused();
  const {
    price,
    address,
    mattress,
    curtainDryClean,
    curtainWashing,
    sofa,
    carpet,
    service,
    date,
    setDateTime,
  } = usePostTaskStore();

  const {
    trackingBackNextActionChooseSofaInfo,
    trackingChooseSofaInfoScreenView,
    trackingPostTaskAbandoned,
  } = useTracking();

  const [tabSelected, setTabSelected] = useState(0);
  const [detailSofa, setDetailSofa] = useState<any>([]);

  React.useEffect(() => {
    // Did mount here
    // Check selected service difference with previous service: reset states before begining
    // The same service: keep state for user continue the booking
    if (previousServiceId && previousServiceId !== service?._id) {
      // resetState();
    }
    !date &&
      setDateTime(
        PostTaskHelpers.getDefaultDateTime(
          {
            serviceName: SERVICES.SOFA,
            defaultTaskTime: service?.defaultTaskTime,
          },
          service?.defaultTaskTime,
          address?.city,
        ),
      );
  }, []);

  // Utility function to set detail sofa by city
  const setDetailSofaByCity = useCallback(() => {
    const cityFromAddress = get(address, 'city', null);
    const detailsSofa = get(service, 'detailSofa', null);

    if (!detailsSofa || !cityFromAddress) {
      return;
    }

    const listCitySupport = detailsSofa.city || [];
    const detailSofaResult = find(listCitySupport, { name: cityFromAddress });
    setDetailSofa(detailSofaResult);
  }, [address, service]);

  // Count function using store data
  const getCount = useCallback(
    (routeName: string) => {
      return getItemCount(routeName, {
        carpet,
        mattress,
        sofa,
        curtainDryClean,
        curtainWashing,
      });
    },
    [carpet, mattress, sofa, curtainDryClean, curtainWashing],
  );

  // Navigation handler
  const handleConfirm = useCallback(() => {
    trackingBackNextActionChooseSofaInfo(TrackingActions.Next);
    navigation.navigate(RouteName.ChooseDateTime);
  }, [navigation, trackingBackNextActionChooseSofaInfo]);

  // Tab routes configuration
  const routes: TabRoute[] = [
    {
      key: TAB_KEYS.SOFA,
      name: TAB_KEYS.SOFA,
      title: t('SV_SOFA_SCR2_TAB_SOFA_TITLE'),
    },
    {
      key: TAB_KEYS.CARPET,
      name: TAB_KEYS.CARPET,
      title: t('SV_SOFA_SCR2_TAB_CARPET_TITLE'),
    },
    {
      key: TAB_KEYS.MATTRESS,
      name: TAB_KEYS.MATTRESS,
      title: t('SV_SOFA_SCR2_TAB_MATTRESS_TITLE'),
    },
    {
      key: TAB_KEYS.CURTAIN,
      name: TAB_KEYS.CURTAIN,
      title: t('SV_SOFA_SCR2_TAB_CURTAIN_TITLE'),
    },
  ];

  // Scene renderer
  const renderScene = useCallback(
    ({ route }: { route: TabRoute }) => {
      switch (route.name) {
        case TAB_KEYS.CARPET:
          return <CarpetTab serviceCarpet={detailSofa?.carpet || []} />;
        case TAB_KEYS.MATTRESS:
          return <MattressTab serviceMattress={detailSofa?.mattress || []} />;
        case TAB_KEYS.SOFA:
          return <SofaTab serviceSofa={detailSofa?.sofa || []} />;
        case TAB_KEYS.CURTAIN:
          return <CurtainTab serviceCurtain={detailSofa?.curtain || []} />;
        default:
          return <BlockView />;
      }
    },
    [detailSofa],
  );

  // Effects
  useEffect(() => {
    navigation.setOptions({
      headerTitle: () => <HeaderTitle address={address} />,
    });
  }, [navigation, address]);

  useEffect(() => {
    // Check if service changed and reset state if needed
    if (previousServiceId && previousServiceId !== service?._id) {
      resetStateSofaCleaning?.();
    }
    setDetailSofaByCity();
  }, [
    previousServiceId,
    service?._id,
    resetStateSofaCleaning,
    setDetailSofaByCity,
  ]);

  useEffect(() => {
    setDetailSofaByCity();
  }, [setDetailSofaByCity]);

  // Track screen view on component mount
  useEffect(() => {
    trackingChooseSofaInfoScreenView();
  }, [trackingChooseSofaInfoScreenView]);

  // Handle tracking for back navigation
  const handleTrackingActionStep2 = useCallback(
    (action: TrackingActions) => {
      if (!isFocused) {
        return null;
      }
      trackingBackNextActionChooseSofaInfo(action);
    },
    [trackingBackNextActionChooseSofaInfo, isFocused],
  );

  useEffect(() => {
    const unsubscribe = navigation.addListener('beforeRemove', () => {
      handleTrackingActionStep2(TrackingActions.Back);
    });

    return unsubscribe; // Cleanup listener when component unmounts
  }, [navigation, handleTrackingActionStep2]);

  // Handle app state changes for tracking
  const handleAppStateChange = (nextAppState: AppStateStatus) => {
    if (isIOS) {
      if (nextAppState === 'inactive') {
        trackingPostTaskAbandoned(TrackingActions.EXITED_APP);
      }
    } else if (nextAppState === 'background') {
      trackingPostTaskAbandoned(TrackingActions.EXITED_APP);
    }
  };

  useEffect(() => {
    const subscribe = AppState.addEventListener('change', handleAppStateChange);
    return () => {
      trackingPostTaskAbandoned(TrackingActions.TAP_HEADER_BACK);
      subscribe?.remove();
    };
  }, []);

  // Check city support
  if (!checkSupportCity(service?.city, address?.city)) {
    return null;
  }

  return (
    <BlockView style={styles.tabContent}>
      <TabView
        key="sofa-cleaning-tab-view"
        lazy
        navigationState={{ index: tabSelected, routes }}
        renderScene={renderScene}
        renderLazyPlaceholder={() => <BlockView />}
        onIndexChange={setTabSelected}
        initialLayout={{ width: DeviceHelper.WINDOW.WIDTH }}
        renderTabBar={() => (
          <CustomTabBar
            routes={routes}
            tabSelected={tabSelected}
            onTabPress={setTabSelected}
            getCount={getCount}
          />
        )}
      />
      <PriceButton
        pricePostTask={price}
        testID="btnNextStep2"
        onPress={handleConfirm}
        fromScreen={service?.name}
      />
    </BlockView>
  );
};
