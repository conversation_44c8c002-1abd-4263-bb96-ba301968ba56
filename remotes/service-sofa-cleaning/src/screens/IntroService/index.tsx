import React, { useEffect } from 'react';
import {
  BlockView,
  Colors,
  ConditionView,
  CText,
  FastImage,
  Markdown,
  ScrollView,
  TouchableOpacity,
} from '@btaskee/design-system';
import { get } from 'lodash-es';

import { useI18n, useTracking } from '@hooks';
import {
  backgroundIntroOfficeCarpet,
  headerBackground,
  iconIntroOfficeCarpet1,
  iconIntroOfficeCarpet2,
  iconIntroOfficeCarpet3,
  iconIntroOfficeCarpet4,
} from '@images';
import { RouteName } from '@navigation/RouteName';

import { styles } from './styles';

const TextItem = ({ icon, title }: any) => {
  return (
    <BlockView
      row
      horizontal
      style={styles.wrapMarginTop}>
      <FastImage
        source={icon}
        style={styles.icon}
      />
      <BlockView
        flex
        style={styles.boxText}>
        <CText style={styles.txtHeader}>{title}</CText>
      </BlockView>
    </BlockView>
  );
};

export const IntroService = ({ navigation, user, route }) => {
  const { t } = useI18n();
  const { trackingServiceIntroductionScreenView } = useTracking();

  // const entryPointRoute = route?.params?.entryPoint;
  const isHideButton = route?.params?.isHideButton;

  // Track screen view on component mount
  useEffect(() => {
    const entryPointRoute = route?.params?.entryPoint;
    trackingServiceIntroductionScreenView(entryPointRoute);
  }, [trackingServiceIntroductionScreenView, route?.params?.entryPoint]);

  // React.useEffect(() => {
  //   trackingServiceView({
  //     screenName: TrackingScreenNames.ServiceIntroduction,
  //     serviceName: SERVICES.CHILD_CARE,
  //     entryPoint: entryPointRoute || entryPoint,
  //   });
  // }, [entryPointRoute, entryPoint]);

  const setAddressToReducer = async (address = {}) => {
    // await setAddress({
    //   lat: address.lat,
    //   lng: address.lng,
    //   country: address.country,
    //   city: address.city,
    //   district: address.district,
    //   address: address.address,
    //   contact: address.contact,
    //   phoneNumber: address.phoneNumber,
    //   shortAddress: address.shortAddress,
    //   countryCode: address.countryCode,
    //   isAddressMaybeWrong: Boolean(address?.isAddressMaybeWrong),
    // });
    // await setHomeType(address.homeType);
    // await setHomeNumber(address.description);
    // return null;
  };

  const onSubmit = async () => {
    // No see intro later
    // setFirstOpenChildCare();

    // set address default
    // get first address
    let addressList = get(user, 'locations', []) || [];
    addressList = addressList.filter((e) => {
      return e.isoCode === user?.isoCode;
    });
    // for old user, user already has an address
    // go to post task step 2

    if (addressList && addressList.length > 0) {
      // use default address
      let address = addressList.find((item) => item?.isDefault === true);
      if (!address) {
        address = addressList[0];
      }
      await setAddressToReducer(address);
      return navigation.replace(RouteName.ChooseAddress, {
        // entryPoint: TrackingScreenNames.ServiceIntroduction,
      });
    }
    return navigation.replace(RouteName.ChooseAddress, {
      // entryPoint: TrackingScreenNames.ServiceIntroduction,
    });
    // for new user, user does not have an address
    // not address history, go to Map to select location
    // navigation.replace(RouteName.Map, {
    //   redirectedFrom: RouteName.PostTaskStep2,
    //   paramsNavigate: { entryPoint: TrackingScreenNames.ServiceIntroduction },
    //   callback: (address) => {
    //     setAddressToReducer(address);
    //   },
    // });
  };

  const onBack = () => navigation.goBack();

  return (
    <BlockView
      inset={['bottom']}
      style={styles.container}>
      <BlockView flex>
        <BlockView>
          <FastImage
            resizeMode="cover"
            source={headerBackground}
            style={styles.headerBackgroundStyle}
          />
        </BlockView>
        <BlockView style={styles.content}>
          <FastImage
            resizeMode="cover"
            source={backgroundIntroOfficeCarpet}
            style={styles.imageStyle}
          />
          <ScrollView
            style={styles.wrap_note}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.contentContainerStyle}>
            <CText
              bold
              style={styles.txt_serviceName}>
              {t('OFFICE_CARPET_CLEANING.INTRO_TITLE')}
            </CText>
            <Markdown
              textStyle={styles.txtHeader}
              text={t('OFFICE_CARPET_CLEANING.INTRO_TITLE_1')}
            />
            <Markdown
              textStyle={styles.txtHeader}
              text={t('OFFICE_CARPET_CLEANING.INTRO_TITLE_2')}
            />
            <TextItem
              icon={iconIntroOfficeCarpet1}
              title={t('OFFICE_CARPET_CLEANING.INTRO_CONTENT_1')}
            />
            <TextItem
              icon={iconIntroOfficeCarpet2}
              title={t('OFFICE_CARPET_CLEANING.INTRO_CONTENT_2')}
            />
            <TextItem
              icon={iconIntroOfficeCarpet3}
              title={t('OFFICE_CARPET_CLEANING.INTRO_CONTENT_3')}
            />
            <TextItem
              icon={iconIntroOfficeCarpet4}
              title={t('OFFICE_CARPET_CLEANING.INTRO_CONTENT_4')}
            />
          </ScrollView>
        </BlockView>
      </BlockView>
      <ConditionView
        condition={Boolean(isHideButton)}
        viewFalse={
          <TouchableOpacity
            style={styles.wrap_bottom}
            onPress={onSubmit}>
            <CText
              bold
              style={styles.titleBtn}
              color={Colors.neutralWhite}>
              {t('INTRO_START_EXPERIENCE')}
            </CText>
          </TouchableOpacity>
        }
        viewTrue={
          <TouchableOpacity
            style={styles.wrap_bottom}
            onPress={onBack}>
            <CText
              bold
              style={styles.titleBtn}
              color={Colors.neutralWhite}>
              {t('BTN_BACK')}
            </CText>
          </TouchableOpacity>
        }
      />
    </BlockView>
  );
};
