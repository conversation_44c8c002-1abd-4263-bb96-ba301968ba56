import React from 'react';
import { AppStateStatus } from 'react-native';
import {
  COMMUNITY_SORT_KEY,
  IEventCommunityClick,
  IEventCommunityView,
  isIOS,
  TrackingActions,
  TrackingCommunityActions,
  TrackingItem,
  TrackingScreenNames,
  TrackingServices,
  TrackingTab,
} from '@btaskee/design-system';
import { useIsFocused } from '@react-navigation/native';

/**
 * Community Service Tracking Hook
 *
 * Provides comprehensive tracking functionality for community service following
 * the multi-provider tracking architecture with provider-agnostic interface.
 *
 * Features:
 * - Screen view tracking for all community screens
 * - User action tracking (clicks, navigation, interactions)
 * - Community-specific event tracking (posts, likes, follows, etc.)
 * - App state change tracking (background/foreground)
 * - Navigation tracking patterns (back, next, exit)
 * - Multi-provider support (CleverTap, Appmetrica, Firebase Analytics)
 * - Complete backward compatibility with legacy tracking events
 */
export const useTracking = () => {
  const isFocused = useIsFocused();

  /**
   * Track community screen view events
   * Maps to legacy trackingCommunityView function
   */
  const trackingCommunityScreenView = React.useCallback(
    (params: IEventCommunityView) => {
      TrackingServices.trackingCommunityView(params);
    },
    [],
  );

  /**
   * Track community user action events
   * Maps to legacy trackingCommunityClick function
   */
  const trackingCommunityAction = React.useCallback(
    (params: IEventCommunityClick) => {
      TrackingServices.trackingCommunityClick(params);
    },
    [],
  );

  /**
   * Handle back navigation tracking
   * Standard pattern used across all super-app services
   */
  const handleTrackingBack = React.useCallback(
    (
      screenName: TrackingScreenNames,
      additionalParams?: Partial<IEventCommunityClick>,
    ) => {
      if (!isFocused) return;

      trackingCommunityAction({
        screenName,
        action: TrackingActions.Back,
        ...additionalParams,
      });
    },
    [isFocused, trackingCommunityAction],
  );

  /**
   * Handle app state change tracking
   * Standard pattern for tracking app backgrounding/foregrounding
   */
  const handleAppStateChange = React.useCallback(
    (
      nextAppState: AppStateStatus,
      screenName: TrackingScreenNames,
      additionalParams?: Partial<IEventCommunityClick>,
    ) => {
      if (!isFocused) return;

      if (isIOS) {
        if (nextAppState === 'inactive') {
          trackingCommunityAction({
            screenName,
            action: TrackingActions.EXITED_APP,
            ...additionalParams,
          });
        }
      } else if (nextAppState === 'background') {
        trackingCommunityAction({
          screenName,
          action: TrackingActions.EXITED_APP,
          ...additionalParams,
        });
      }
    },
    [isFocused, trackingCommunityAction],
  );

  // =============================================================================
  // NEWS FEED TRACKING FUNCTIONS
  // =============================================================================

  /**
   * Track news feed screen view
   * Legacy: trackingCommunityView in news-feed/hook/index.tsx
   */
  const trackingNewsFeedScreenView = React.useCallback(
    (params: {
      entryPoint?: TrackingScreenNames;
      isLoggedInCommunity?: boolean;
    }) => {
      trackingCommunityScreenView({
        screenName: TrackingScreenNames.NewsFeeds,
        entryPoint: params.entryPoint,
        isLoggedInCommunity: params.isLoggedInCommunity,
      });
    },
    [trackingCommunityScreenView],
  );

  /**
   * Track news feed sorting action
   * Legacy: onSortNews in news-feed/hook/index.tsx
   */
  const trackingNewsFeedSort = React.useCallback(
    (params: {
      sortOption: COMMUNITY_SORT_KEY;
      isLoggedInCommunity?: boolean;
    }) => {
      trackingCommunityAction({
        screenName: TrackingScreenNames.NewsFeeds,
        action: TrackingCommunityActions.Sort,
        isLoggedInCommunity: params.isLoggedInCommunity,
        sortOption: params.sortOption,
      });
    },
    [trackingCommunityAction],
  );

  /**
   * Track hashtag filter action
   * Legacy: onChangeHashtag/onPressHashtag in news-feed/hook/index.tsx
   */
  const trackingNewsFeedFilter = React.useCallback(
    (params: {
      filterPostTag: string;
      item: TrackingItem;
      isLoggedInCommunity?: boolean;
    }) => {
      trackingCommunityAction({
        screenName: TrackingScreenNames.NewsFeeds,
        action: TrackingCommunityActions.Filter,
        isLoggedInCommunity: params.isLoggedInCommunity,
        filterPostTag: params.filterPostTag,
        item: params.item,
      });
    },
    [trackingCommunityAction],
  );

  /**
   * Track profile access from news feed
   * Legacy: handleClickProfile in news-feed/hook/index.tsx
   */
  const trackingNewsFeedProfileAccess = React.useCallback(
    (params: { profileId: string; item: TrackingItem }) => {
      trackingCommunityAction({
        screenName: TrackingScreenNames.NewsFeeds,
        action: TrackingCommunityActions.AccessUserProfile,
        item: params.item,
        destination: TrackingScreenNames.UserCommunityProfile,
        profileId: params.profileId,
      });
    },
    [trackingCommunityAction],
  );

  /**
   * Track post like/unlike action
   * Legacy: handleLikePost in news-feed/hook/index.tsx
   */
  const trackingNewsFeedLike = React.useCallback(
    (params: {
      postId: string;
      isLiked: boolean;
      isLoggedInCommunity?: boolean;
    }) => {
      trackingCommunityAction({
        screenName: TrackingScreenNames.NewsFeeds,
        action: params.isLiked
          ? TrackingCommunityActions.UnLiked
          : TrackingCommunityActions.Liked,
        isLoggedInCommunity: params.isLoggedInCommunity,
        postId: params.postId,
      });
    },
    [trackingCommunityAction],
  );

  /**
   * Track post detail access
   * Legacy: onPressNewsFeedDetail in news-feed/hook/index.tsx
   */
  const trackingNewsFeedDetailAccess = React.useCallback(
    (params: { postId: string }) => {
      trackingCommunityAction({
        screenName: TrackingScreenNames.NewsFeeds,
        action: TrackingCommunityActions.AccessPostDetail,
        postId: params.postId,
      });
    },
    [trackingCommunityAction],
  );

  // =============================================================================
  // CREATE POST TRACKING FUNCTIONS
  // =============================================================================

  /**
   * Track create post screen view
   * Legacy: onTrackingScreenView in create-new-post/hook/index.tsx
   */
  const trackingCreatePostScreenView = React.useCallback(() => {
    trackingCommunityScreenView({
      screenName: TrackingScreenNames.CreatePost,
      entryPoint: TrackingScreenNames.NewsFeeds,
    });
  }, [trackingCommunityScreenView]);

  /**
   * Track create post back confirmation
   * Legacy: showAlertConfirmGoBack in create-new-post/hook/index.tsx
   */
  const trackingCreatePostBackConfirm = React.useCallback(
    (params: {
      action: TrackingActions;
      additionalInfo?: {
        isNeedHelpPost?: boolean;
        content?: string;
        postTag?: string[];
        images?: any[];
      };
    }) => {
      trackingCommunityAction({
        screenName: TrackingScreenNames.CreatePost,
        action: params.action,
        additionalInfo: params.additionalInfo,
      });
    },
    [trackingCommunityAction],
  );

  /**
   * Track clear content action
   * Legacy: onClearContent in create-new-post/hook/index.tsx
   */
  const trackingCreatePostClearContent = React.useCallback(() => {
    trackingCommunityAction({
      screenName: TrackingScreenNames.CreatePost,
      action: TrackingCommunityActions.ClearAllContent,
    });
  }, [trackingCommunityAction]);

  // =============================================================================
  // USER PROFILE TRACKING FUNCTIONS
  // =============================================================================

  /**
   * Track user profile screen view
   * Legacy: onTrackingScreenView in user-profile/hook/index.tsx
   */
  const trackingUserProfileScreenView = React.useCallback(
    (params: { profileId: string; entryPoint?: TrackingScreenNames }) => {
      trackingCommunityScreenView({
        screenName: TrackingScreenNames.UserCommunityProfile,
        profileId: params.profileId,
        entryPoint: params.entryPoint,
      });
    },
    [trackingCommunityScreenView],
  );

  /**
   * Track user profile back action
   * Legacy: onGoBack in user-profile/hook/index.tsx
   */
  const trackingUserProfileBack = React.useCallback(
    (params: { profileId: string }) => {
      trackingCommunityAction({
        screenName: TrackingScreenNames.UserCommunityProfile,
        action: TrackingActions.Back,
        profileId: params.profileId,
      });
    },
    [trackingCommunityAction],
  );

  /**
   * Track post detail access from user profile
   * Legacy: onPressNewsFeedDetail in user-profile/hook/index.tsx
   */
  const trackingUserProfilePostAccess = React.useCallback(
    (params: { postId: string }) => {
      trackingCommunityAction({
        screenName: TrackingScreenNames.UserCommunityProfile,
        action: TrackingCommunityActions.AccessPostDetail,
        postId: params.postId,
      });
    },
    [trackingCommunityAction],
  );

  // =============================================================================
  // USER PROFILE UPDATE TRACKING FUNCTIONS
  // =============================================================================

  /**
   * Track user profile update screen view
   * Legacy: onTrackingScreenView in user-profile-update/hook/index.tsx
   */
  const trackingUserProfileUpdateScreenView = React.useCallback(
    (params: { profileId: string }) => {
      trackingCommunityScreenView({
        screenName: TrackingScreenNames.UserCommunityUpdateProfile,
        profileId: params.profileId,
        entryPoint: TrackingScreenNames.UserCommunityProfile,
      });
    },
    [trackingCommunityScreenView],
  );

  /**
   * Track user profile update back action
   * Legacy: onGoBack in user-profile-update/hook/index.tsx
   */
  const trackingUserProfileUpdateBack = React.useCallback(
    (params: { profileId: string }) => {
      trackingCommunityAction({
        screenName: TrackingScreenNames.UserCommunityUpdateProfile,
        action: TrackingActions.Back,
        profileId: params.profileId,
      });
    },
    [trackingCommunityAction],
  );

  /**
   * Track user profile update action
   * Legacy: onUpdateUserProfile in user-profile-update/hook/index.tsx
   */
  const trackingUserProfileUpdate = React.useCallback(
    (params: { profileId: string; additionalInfo?: any }) => {
      trackingCommunityAction({
        screenName: TrackingScreenNames.UserCommunityUpdateProfile,
        action: TrackingCommunityActions.UpdateProfile,
        profileId: params.profileId,
        additionalInfo: params.additionalInfo,
      });
    },
    [trackingCommunityAction],
  );

  /**
   * Track user profile change badge action
   * Legacy: onSummitChangeMedal in user-profile-update/hook/index.tsx
   */
  const trackingUserProfileChangeBadge = React.useCallback(
    (params: { profileId: string; additionalInfo?: any }) => {
      trackingCommunityAction({
        screenName: TrackingScreenNames.UserCommunityUpdateProfile,
        action: TrackingCommunityActions.ChangeBadge,
        profileId: params.profileId,
        additionalInfo: params.additionalInfo,
      });
    },
    [trackingCommunityAction],
  );

  // =============================================================================
  // SEARCH TRACKING FUNCTIONS
  // =============================================================================

  /**
   * Track community search screen view
   * Legacy: trackingCommunityView in search/hook/index.ts
   */
  const trackingSearchScreenView = React.useCallback(() => {
    trackingCommunityScreenView({
      screenName: TrackingScreenNames.CommunitySearch,
      entryPoint: TrackingScreenNames.NewsFeeds,
    });
  }, [trackingCommunityScreenView]);

  /**
   * Track search action
   * Legacy: onSearch in search/hook/index.ts
   */
  const trackingSearchAction = React.useCallback(
    (params: { searchText: string; sortKey?: string; item: TrackingItem }) => {
      trackingCommunityAction({
        screenName: TrackingScreenNames.CommunitySearch,
        action: TrackingCommunityActions.Search,
        additionalInfo: {
          searchText: params.searchText,
          sortKey: params.sortKey,
        },
        item: params.item,
      });
    },
    [trackingCommunityAction],
  );

  /**
   * Track search back action
   * Legacy: onGoBack in search/hook/index.ts
   */
  const trackingSearchBack = React.useCallback(
    (params: { searchText: string; sortKey?: string }) => {
      trackingCommunityAction({
        screenName: TrackingScreenNames.CommunitySearch,
        action: TrackingActions.Back,
        additionalInfo: {
          searchText: params.searchText,
          sortKey: params.sortKey,
        },
      });
    },
    [trackingCommunityAction],
  );

  /**
   * Track search recent post detail access
   * Legacy: onPressNewsFeedDetail in search-recent/hook/index.ts
   */
  const trackingSearchRecentPostAccess = React.useCallback(
    (params: { postId: string }) => {
      trackingCommunityAction({
        screenName: TrackingScreenNames.SearchRecent,
        action: TrackingCommunityActions.AccessPostDetail,
        tab: 'MostRecent' as any,
        postId: params.postId,
      });
    },
    [trackingCommunityAction],
  );

  /**
   * Track search relevant post detail access
   * Legacy: onPressNewsFeedDetail in search-relevant/hook/index.ts
   */
  const trackingSearchRelevantPostAccess = React.useCallback(
    (params: { postId: string }) => {
      trackingCommunityAction({
        screenName: TrackingScreenNames.SearchRelevant,
        action: TrackingCommunityActions.AccessPostDetail,
        tab: TrackingTab.MostRelevant,
        postId: params.postId,
      });
    },
    [trackingCommunityAction],
  );

  // =============================================================================
  // EDIT POST TRACKING FUNCTIONS
  // =============================================================================

  /**
   * Track edit post screen view
   * Legacy: onTrackingScreenView in edit-post/hook/index.ts
   */
  const trackingEditPostScreenView = React.useCallback(
    (params: { entryPoint?: TrackingScreenNames }) => {
      trackingCommunityScreenView({
        screenName: TrackingScreenNames.EditPost,
        entryPoint: params.entryPoint,
      });
    },
    [trackingCommunityScreenView],
  );

  // =============================================================================
  // NEWS FEED DETAIL TRACKING FUNCTIONS
  // =============================================================================

  /**
   * Track news feed detail screen view
   * Legacy: trackingCommunityView in news-feed-detail/hook/index.ts
   */
  const trackingNewsFeedDetailScreenView = React.useCallback(
    (params: {
      entryPoint?: TrackingScreenNames;
      isLoggedInCommunity?: boolean;
    }) => {
      trackingCommunityScreenView({
        screenName: TrackingScreenNames.NewsFeedsDetail,
        entryPoint: params.entryPoint,
        isLoggedInCommunity: params.isLoggedInCommunity,
      });
    },
    [trackingCommunityScreenView],
  );

  return {
    // Core tracking functions
    trackingCommunityScreenView,
    trackingCommunityAction,
    handleTrackingBack,
    handleAppStateChange,

    // News Feed tracking
    trackingNewsFeedScreenView,
    trackingNewsFeedSort,
    trackingNewsFeedFilter,
    trackingNewsFeedProfileAccess,
    trackingNewsFeedLike,
    trackingNewsFeedDetailAccess,
    trackingNewsFeedDetailScreenView,

    // Create Post tracking
    trackingCreatePostScreenView,
    trackingCreatePostBackConfirm,
    trackingCreatePostClearContent,

    // User Profile tracking
    trackingUserProfileScreenView,
    trackingUserProfileBack,
    trackingUserProfilePostAccess,

    // User Profile Update tracking
    trackingUserProfileUpdateScreenView,
    trackingUserProfileUpdateBack,
    trackingUserProfileUpdate,
    trackingUserProfileChangeBadge,

    // Search tracking
    trackingSearchScreenView,
    trackingSearchAction,
    trackingSearchBack,
    trackingSearchRecentPostAccess,
    trackingSearchRelevantPostAccess,

    // Edit Post tracking
    trackingEditPostScreenView,
  };
};
