import {
  DateTime<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Endpoint<PERSON>eys,
  getPhoneNumber,
  handleError,
  IApiError,
  IDate,
  IPriceSub,
  IPromotion,
  PaymentService,
  PostTaskHelpers,
  useApiMutation,
  useAppLoadingStore,
  useAppStore,
  usePostTaskAction,
  useUserStore,
} from '@btaskee/design-system';
import { IDataBooking, IParamsGetPrice } from '@types';
import { isEmpty } from 'lodash-es';

import { usePostTaskStore } from '@stores';

const buildBaseTaskInfo = (
  address: any,
  user: any,
  startDate: IDate,
  endDate: IDate,
  duration: number,
  service: any,
  isoCode: string,
  paymentMethod: any,
  timezone: string,
  promotion?: IPromotion,
): Partial<IDataBooking> => {
  return {
    startDate: DateTimeHelpers.formatToString({
      date: startDate,
      timezone,
    }),
    endDate: DateTimeHelpers.formatToString({
      date: endDate,
      timezone,
    }),
    timezone,
    address: address?.address,
    contactName: address?.contact || user?.name,
    location: {
      lat: address?.lat,
      lng: address?.lng,
    },
    phone: getPhoneNumber(
      address?.phoneNumber || user?.phone || '',
      address?.countryCode || user?.countryCode || '',
    ),
    countryCode: address?.countryCode || user?.countryCode,
    description: address?.description,
    deviceInfo: DeviceHelper.getDeviceInfo(),
    duration,
    homeType: address?.homeType,
    houseNumber: address?.description,
    isoCode,
    serviceId: service?._id,
    taskPlace: {
      country: address?.country,
      city: address?.city,
      district: address?.district,
    },
    shortAddress: address?.shortAddress,
    ...PostTaskHelpers.formatDataToParams({ paymentMethod, promotion }),
  };
};

export const usePostTask = () => {
  const { isoCode } = useAppStore();
  const { user } = useUserStore();
  const { setPrice, setLoadingPrice, resetState } = usePostTaskStore();
  const { showLoading, hideLoading } = useAppLoadingStore();
  const { handlePostTaskError } = usePostTaskAction();

  const { mutate: getPriceElderlyCareSubscription } = useApiMutation({
    key: EndpointKeys.getPriceElderlyCareSubscription,
    options: {
      onMutate: () => {
        setLoadingPrice(true);
        showLoading();
      },
      onSettled: () => {
        setLoadingPrice(false);
        hideLoading();
      },
    },
  });

  const { mutate: postTaskSubscriptionCleaning } = useApiMutation({
    key: EndpointKeys.postTaskSubscriptionCleaning,
    options: {
      onSuccess: async (data) => {
        const paymentMethod = usePostTaskStore.getState().paymentMethod;
        resetState();
        await PaymentService.onPostTaskSubscriptionSuccess({
          paymentMethod,
          data: data?.data,
        });
      },
      onError: (error: IApiError) => {
        handlePostTaskError(error);
      },
      onMutate: () => {
        showLoading();
      },
      onSettled() {
        hideLoading();
      },
    },
  });

  const buildPricingData = (): IParamsGetPrice | null => {
    const currentState = usePostTaskStore.getState();
    const {
      address,
      duration,
      addons,
      schedule,
      month,
      promotion,
      paymentMethod,
      service,
    } = currentState;

    if (isEmpty(schedule) || !duration || !month) {
      return null;
    }

    const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);
    const schedules = schedule.map((e) =>
      DateTimeHelpers.formatToString({ timezone, date: e }),
    );

    const task: IParamsGetPrice['task'] = {
      taskPlace: {
        country: address?.country,
        city: address?.city,
        district: address?.district,
      },
      duration: duration,
      autoChooseTasker: true,
      homeType: address?.homeType,
      ...PostTaskHelpers.formatDataToParams({ paymentMethod, promotion }),
    };

    if (!isEmpty(addons)) {
      task.addons = addons;
    }

    return {
      schedule: schedules,
      timezone,
      task,
      service: { _id: service?._id || '' },
      month,
      isoCode: isoCode as string,
    };
  };

  const getPrice = async (): Promise<void> => {
    const pricingData = buildPricingData();

    if (!pricingData) {
      setPrice(null);
      return;
    }

    setLoadingPrice(true);

    await getPriceElderlyCareSubscription(pricingData, {
      onSuccess: (result: any) => {
        setPrice(result as IPriceSub);
      },
      onError: (error: IApiError) => {
        handleError(error);
        setPrice(null);
      },
    });

    setLoadingPrice(false);
  };

  const buildTaskData = (): IDataBooking => {
    const currentState = usePostTaskStore.getState();
    const {
      address,
      promotion,
      startDate,
      endDate,
      weekdays,
      duration,
      paymentMethod,
      note,
      schedule,
      month,
      addons,
      service,
    } = currentState;

    const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

    const baseTask = buildBaseTaskInfo(
      address,
      user,
      startDate as IDate,
      endDate as IDate,
      duration,
      service,
      isoCode as string,
      paymentMethod,
      timezone,
      promotion,
    );

    const task: IDataBooking = {
      ...baseTask,
      weekday: weekdays,
      month: month,
    } as IDataBooking;

    if (schedule && schedule.length > 0) {
      task.schedule = schedule.map((e) =>
        DateTimeHelpers.formatToString({ date: e, timezone }),
      );
    }

    // Add optional fields
    if (address?.isAddressMaybeWrong) {
      task.taskPlace.isAddressMaybeWrong = true;
    }

    if (note?.trim()) {
      task.taskNote = note.trim();
    }

    if (!isEmpty(addons)) {
      task.addons = addons;
    }

    return task;
  };

  const postTask = async (): Promise<any> => {
    const taskData = buildTaskData();
    return postTaskSubscriptionCleaning(taskData);
  };

  return { getPrice, postTask };
};
