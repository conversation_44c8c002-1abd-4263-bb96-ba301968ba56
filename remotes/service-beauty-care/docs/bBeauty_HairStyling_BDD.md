**Business Design Document - bBeauty Hair Relaxing Service**

**1. Purpose** Enable users to book professional hair relaxing and styling services via a mobile app, allowing them to specify the service type (hair wash, root touch-up), desired location, schedule, and payment method for certified hairstylists (Taskers) to provide comprehensive hair care for various needs.

**2. Scope / Features**

* **Choose Service Type**
  * **Service Category Selection:** Users can choose from primary beauty service categories such as "Makeup & Hairstyling" and "Hair Relaxing".
  * **Customer Count:** Users specify the number of customers for the service (currently limited to 1 or 2 per task).
* **Choose Task Details (Hair Relaxing)**
  * **Service Selection:** Users can select one or both predefined hair relaxing services (e.g., "Relaxing hair wash", "Root Touch-Up").
  * **Optional Add-ons:** Users can opt for additional services like "Heat Styling" for "Relaxing hair wash" or "Hair Color" selection for "Root Touch-Up".
  * **Multi-Customer Detail Application:** When booking for 2 customers, users have the option to apply the selected hair relaxing service and add-on details of the first customer to the second customer.
  * **Service Information Access:** Users can access detailed information about the service process, tools, equipment, and products used.
  * **Dynamic Pricing:** Real-time pricing calculation based on the selected hair relaxing service, add-ons, and quantity.
* **Choose an Address**
  * **Location Selection:** Users can choose their service location from existing saved addresses or create new addresses for booking.
  * **Address and Contact Management:** Handle contact details associated with each location including name and phone number for service coordination, allowing users to save frequently used addresses and set preferences for future bookings.
* **Choose Working Time**
  * **Date and Time Selection:** Flexible scheduling system allowing users to choose preferred appointment dates and times.
  * **Notes Pop-up:** Text-based communication system allowing users to provide specific instructions and requests to service providers.
  * **Choose Execution Order Pop-up (for 2 Customers)**
    * **Simultaneous/Sequential Service:** Users can specify if two customers receive service "At the same time" (by two Taskers) or "One after the other" (by one Tasker).
* **Confirm and Payment**
  * **Booking Confirmation:** Comprehensive booking summary and confirmation system displaying all service details before final commitment.
  * **Payment Processing:** Multi-channel payment system supporting various payment methods including digital wallets, cards, and alternative payment options.
  * **Promotion Management:** Discount and promotion code system allowing users to apply available offers and see immediate savings.
  * **Contact Updates:** Real-time contact information editing capabilities during the booking confirmation process.

**3. Stakeholders**

* **End Users:** Individuals booking hair relaxing and styling services for personal events, routine care, or special occasions.
* **Hair Stylists (Taskers):** Certified beauty professionals who will execute the services according to user specifications and professional standards.
* **Admin:** Platform administrators responsible for service quality oversight, scheduling coordination, pricing management, promotion oversight, and overall system maintenance.

**4. Functional Requirements**

**4.1 Choose Service Type**

* **Service Category Display:** The system shall display clear cards for "Makeup & Hairstyling" and "Hair Relaxing" as primary service categories.
* **Customer Count Selection:** Upon selecting "Hair Relaxing," the system shall prompt the user to select the number of customers (1 or 2).
* **Booking Limitation Notification:** The system shall display a warning stating that only a maximum of 02 service packages are supported per task and advise booking an additional task for more customers.

**4.2 Choose Task Details (Hair Relaxing)**

* **Hair Service Options Display:** The system shall present available hair relaxing services:
  * Options:
    * "Relaxing hair wash" (Price: 150,000₫)
    * "Root Touch-Up" (Price: 420,000₫)
  * Users can tap “+” to add or “–” to remove each service selection.
* **Optional Add-on - Heat Styling:**
  * For "Relaxing hair wash," the system shall present an option to "Add Heat Styling" (Price: 20,000₫).
  * If "Add Heat Styling" is selected, the system shall dynamically update the total price to reflect the combined cost.
* **Optional Add-on - Hair Color Selection (for Root Touch-Up):**
  * For "Root Touch-Up," the system shall provide an option to "Choose Color".
  * The system shall display a list of available hair colors (e.g., "Deep Brown", "Black", "Light Brown", etc.) for the user to select.
  * The system shall indicate if "No color chosen" until a selection is made.
* **Multi-Customer Detail Application:** If 2 customers are selected, the system shall provide an option (e.g., checkbox "Apply details to customer 2") that, when activated, automatically applies the selected hair service, add-ons (like Heat Styling) and specific details (like Hair Color) from customer 1 to customer 2, and disables/hides independent selection options for customer 2.
* **Service Information Access:**
  * The system shall provide a tappable section/button to view "Working Process," which displays a step-by-step methodology.
  * The system shall provide a tappable section/button to view "Tools, Equipment, and Product" details, listing items used in the service.
* **Dynamic Pricing Display:** The total price and estimated duration (e.g., "60 minutes", "165 minutes") shall be dynamically updated and displayed at the bottom of the screen based on the selected hair services and add-ons.

**4.3 Choose an Address**

* **Address Selection Options:** Users can select from previously saved addresses or add new addresses through multiple intuitive input methods.
* **Multi-Input Address System:** Users can input addresses through three distinct methods:
  * GPS location detection for automatic current location identification and nearby address suggestion.
  * Manual text entry with auto-complete and address suggestion functionality for precise address specification.
  * Interactive map selection with pin-dropping capability for visual location picking and address confirmation.
* **Address Validation and Formatting:** The system validates the address. Users receive feedback if the address is invalid and are prevented from proceeding with booking.
* **Address and Contact Information Management:** Users can store and manage both address details and associated contact information including contact name and phone number with validation, save addresses, edit existing addresses, and set a default address for streamlined future booking experiences.

**4.4 Choose Working Time**

* **Calendar Date Selection:** Users can interact with an interactive calendar interface with the following specifications:
  * Users see current date clearly highlighted and marked as starting reference point.
  * Users can select from next 7 consecutive days displayed as available booking options.
  * The system shall indicate "Peak days" with a distinct visual marker (e.g., orange dot).
* **Time Selection Interface:** Users can utilize an intuitive time picker with comprehensive options:
  * Users see 12-hour format with clear AM/PM designation.
  * Users can select 5-minute interval options for flexible scheduling.
  * An "Earliest available" button shall be present to quickly select the next open slot.
* **Notes Pop-up:** Users can utilize a robust text input system for special instructions:
  * Users can leave the optional text field empty without preventing booking completion.
  * The system enforces a maximum character limit (e.g., 400 characters) with a real-time character counter and remaining count display.
  * Users can tick a checkbox labeled "Use for next booking" to save the entered notes as a default for future bookings.
* **Choose Execution Order Pop-up (for 2 Customers)**
  * **Execution Order Selection:** If 2 customers are selected, the system shall present a modal allowing the user to choose between:
    * "At the same time": Service provided simultaneously by two different Taskers.
    * "One after the other": Service provided sequentially by the same Tasker.
  * **Order Confirmation:** The system shall register the user's selected execution order.

**4.5 Confirm and Payment**

* **Complete Booking Summary Display:** Users can review comprehensive booking information:
  * Users see scheduled date and time.
  * Users see detailed service type (e.g., "Hair Relaxing"), specific hair services (e.g., "Relaxing hair wash", "Root Touch-Up", "Heat Styling", "Deep Brown"), and payment details (Service Cost/Discount).
  * Users see complete address display with contact information (name and phone number).
  * Users can edit contact name and phone number directly in the booking summary via an "Edit" or pencil button.
  * The system displays Notes for Tasker notes if provided.
  * Users see estimated service duration (e.g., "60 minutes", "165 minutes").
* **Advanced Promotion Code Management:** Users can access comprehensive promotion functionality:
  * Users can enter promotion codes in dedicated text input field with format validation.
  * Users see automatic discount calculation and application to total pricing with immediate visual feedback.
  * Users can remove promotion codes with pricing recalculation.
* **Comprehensive Payment Method Support:** Users can select from multiple payment channels with robust processing:
  * Users can choose "Cash" payment option with clear instructions for payment upon service completion.
  * Users can use digital wallet integration including bPay, Momo, ZaloPay, ShopeePay with secure connections.
  * Users can process credit card payments for Visa and MasterCard with secure 3D authentication.
  * Users can access buy-now-pay-later option through Kredivo with installment calculation and approval workflow.
  * The system provides payment method validation and error handling for failed transactions.
* **Terms and Conditions Acceptance:** A checkbox for "I have read and understood the terms and conditions of bTaskee" must be present. The "terms and conditions" text shall be a tappable link to the full terms.
* **Confirmation Process:** The system ensures robust booking processing:
  * A "Continue" button shall be prominently displayed.
  * The button shall be enabled only when all required information is complete (e.g., terms and conditions accepted, payment method selected).
  * The system automatically navigates users to a "Success Booking" screen upon successful transaction completion.

**5. Non-Functional Requirements**

* **Mobile-first Responsive Design:** Optimized for hair service booking on various mobile devices.
* **Vietnamese Language Support:** Clear, professional interface in Vietnamese.
* **Real-time Validation and User Feedback:** Immediate feedback for invalid inputs or selections.
* **Secure Handling:** Secure handling of personal and payment information.
* **Reliable Tasker Matching:** Efficient and reliable matching and scheduling system for hairstylists.
* **Performance Optimization:** Fast pricing calculations and highly responsive UI.
* **Accessibility Features:** Designed with accessibility considerations for all users.

**6. Acceptance Criteria (Testable Steps)**

**6.1 Choose Service Type**

* **Select Hair Relaxing:** User taps the "Hair Relaxing" card from the main service selection screen. The system navigates the user to the hair relaxing service selection screen.
* **Select Customer Count (1):** User selects "1 customer" from the "Number of customers" modal. The system registers 1 customer for the service and navigates to the Choose Task Details (Hair Relaxing) page.
* **Select Customer Count (2):** User selects "2 customers" from the "Number of customers" modal. The system registers 2 customers for the service and navigates to the Choose Task Details (Hair Relaxing) page.
* **Attempt Invalid Customer Count:** User can see the "Only supports a maximum of 02 service packages per task..." warning when attempting to select more than 2 customers.

**6.2 Choose Task Details (Hair Relaxing)**

* **Select "Relaxing hair wash":**
  * User taps on the "Relaxing hair wash" service card.
  * The system visually highlights the selected service.
  * The "Total" price displayed at the bottom of the screen accurately reflects "150,000₫".
  * The "Total" duration (e.g., "60 minutes") is displayed.
* **Select "Root Touch-Up":**
  * User taps on the "Root Touch-Up" service card.
  * The system visually highlights the selected service.
  * The "Total" price displayed at the bottom of the screen accurately reflects "420,000₫".
  * The "Total" duration (e.g., "105 minutes") is displayed.
* **Add "Heat Styling" (for Relaxing hair wash):**
  * User first selects "Relaxing hair wash".
  * User then taps the option to add "Heat Styling".
  * The system dynamically updates the "Total" price to reflect the combined cost (e.g., 150,000₫ + 20,000₫ = 170,000₫).
  * The "Total" duration is updated to reflect the estimated time for both services.
  * The system visually marks the "Heat Styling" option as selected.
* **Select Hair Color (for Root Touch-Up):**
  * User first selects "Root Touch-Up".
  * The system displays a "Hair Color" section with "No color chosen" text.
  * User taps on "Choose Color".
  * The system presents a list of color options (e.g., "Deep Brown").
  * User selects a color (e.g., "Deep Brown"). The system updates the "Hair Color" section to display the chosen color.
* **Multi-customer Details Application (for 2 Customers):**
  * **Scenario Setup:** After the user has selected "2 customers" for the service, when navigating to the hair relaxing service selection screen, the system displays distinct sections for "Customer 1's Details" and "Customer 2's Details". Additionally, the system presents a checkbox (e.g., "Apply Customer 1's details to Customer 2") usually near the customer detail sections.
  * **Apply Details to Customer 2 (Ticked):**
    * When the user ticks the "Apply Customer 1's details to Customer 2" checkbox (after selecting options for Customer 1), the system automatically copies the selected hair service (e.g., Relaxing hair wash), add-ons (e.g., Heat Styling), and specific details (e.g., Hair Color) from Customer 1 to Customer 2.
    * Concurrently, the independent selection options (e.g., service cards, add-on toggles, color selection) within "Customer 2's Details" section become disabled or hidden, preventing the user from making separate choices for Customer 2. The total price and duration are updated to reflect the duplicated service.
  * **Independent Selection for Customer 2 (Not Ticked):**
    * If the user *does not* tick the "Apply Customer 1's details to Customer 2" checkbox, the system keeps the selection options within "Customer 2's Details" active.
    * The user can then proceed to independently select hair services and add-ons for Customer 2, which will update the total price and duration accordingly.
* **Information Access (Working Process, Tools/Equipment/Product):**
  * **View Working Process:** User taps the "Working Process" section/button. The system displays a modal or navigates to a new screen detailing the step-by-step working process. The system allows the user to close this view.
  * **View Tools, Equipment, and Product:** User taps the "Tools, Equipment, and Product" section/button. The system displays a modal or navigates to a new screen detailing the tools, equipment, and products used for the service. The system allows the user to close this view.
* **Proceed to Address Selection:** User taps "Continue" button after selecting hair services and add-ons.

**6.3 Choose an Address**

* **Existing Location Selection:** Users can select an already saved address from the "Your saved address" if available.
* **Choose Location Screen (New Address Entry):** Tapping "Choose new address" section navigates to the "Choose new address" screen, where users can add a new location by:
  * Using GPS to automatically detect their current location.
  * Manually entering full address details.
  * Tapping on the map and confirming with "Pick this location."
* **Location Detail Input & Confirmation:** After selecting or adding a location, the system displays a popup for users to input complete address details:
  * "Selected address" (pre-filled from map/search) can be edited.
  * "Phone number" (validated for Vietnamese format, pre-filled if from existing contact).
  * "Contact name" (pre-filled if from existing contact).
  * "House number" (free text input).
  * Users can tap "Confirm" to save these address and contact details.
  * Users may edit all address and contact fields before final confirmation.
* **Proceed to Working Time Selection:** User selects one of the existing addresses or adds a new one. The system navigates to the 'Choose working time' page with the selected address.

**6.4 Choose Working Time**

* **Calendar Date Selection:** Upon navigating to the "Choose working time" screen, users can interact with an interactive calendar interface with the following specifications:
  * Users see current date clearly highlighted and marked as starting reference point.
  * The system displays an orange dot on peak days (if any) within this 7-day window.
  * Users see holiday and peak demand period indicators where applicable.
* **Time Selection Interface:** Users can utilize an intuitive time picker with comprehensive options:
  * Users see 12-hour format with clear AM/PM designation.
  * Users can select 5-minute interval options for flexible scheduling.
* **Select Earliest Available Time:**
  * When the user taps 'Earliest available', the system selects the nearest open time slot and updates the total price and duration accordingly.
* **Add Notes at Notes Pop-up:** User taps to open the 'Notes' modal. The user enters text within the 400-character limit and can optionally check 'Use for next booking.' The user can tap outside to dismiss the modal or tap 'Continue' to navigate to the Confirm and Pay page.
* **Choose Execution Order (for 2 Customers)**
  * **Access Execution Order Modal:** If 2 customers are selected, after choosing service details for both, the system prompts the user with the "Choose the execution order" modal (e.g., before or after time selection).
  * **Select "At the same time":** User taps the "At the same time" option. The system selects this option and user taps "Confirm". The booking process continues, implying two Taskers for simultaneous service.
  * **Select "One after the other":** User taps the "One after the other" option. The system selects this option and user taps "Confirm". The booking process continues, implying one Tasker for sequential service.

**6.5 Confirmation and Payment**

**6.5.1 Booking Summary & Confirmation**

* **Review Details:** User navigates to the "Payment details" screen.
  * Verifies "Working time" (e.g., "EEEE, hh:mm AM/PM - MM/dd/yyyy").
  * Verifies "Task details"
  * Verifies "Payment details" (Service Cost, Discount, Total Payment, e.g., "740,000₫").
  * Verifies "Address" (full address and address details, contact details as name and phone number).
  * Verifies "Note" if provided.
  * Verifies estimated service duration (e.g., "Total: 165 minutes").
* **Edit Contact Information:** User taps the pencil icon in the Address section. The "Change info" modal appears. User changes name/phone number and taps "Confirm." The updated details are reflected in the summary.

**6.5.2 Payment Method Selection**

* **Select Cash:** User taps on "Cash" under "Payment method". The system stores this selection.
* **Select Electronic Payment:** User taps on "Cash" and then selects a digital wallet (e.g., Momo, ZaloPay, ShopeePay,…). The system stores this selection and navigates back to the summary.
* **Select Card Payment:** User taps on "Cash" and selects "Visa/Master". If no card is saved, the system navigates to "Add Card" screen. User inputs card details and saves. The system navigates back to the summary.
* **Secure Transactions:** The system processes credit card payments for Visa and MasterCard with secure 3D authentication.
* **Buy-Now-Pay-Later:** Users can access buy-now-pay-later option through Kredivo with installment calculation and approval workflow.
* **Payment Validation:** The system provides payment method validation and error handling for failed transactions.

**6.5.3 Promotion Code Handling:**

* **Apply Valid Code:** User taps "Add voucher", enters a valid promotion code, and taps apply. The "Discount" amount updates, and the "Total Payment" recalculates.
* **Apply Invalid Code:** User enters an invalid promotion code. A notification "The promotion code is invalid. Please check again." is displayed. The discount and total price remain unchanged.

**6.5.4 Terms and Conditions:**

* **Accept Terms:** User checks the "I have read and understood the terms and conditions of bTaskee" checkbox. The "Continue" (or "Book") button changes from greyed out to active.
* **View Terms:** User taps on the underlined "terms and conditions" text. A new screen/modal displaying the full terms and conditions content appears. User closes this screen/modal.

**6.5.5 Final Confirmation:**

* **Successful Booking:** User taps the active "Continue" button. The system processes the booking. The user is automatically navigated to the "Success Booking" screen.

**7. Error Handling & Validation**

* **Past Date/Time Selection:** Attempting to select a past date or time shows an appropriate error message and prevents selection.
* **Incomplete Address Fields:** Incomplete required fields in the address input prevent progression to the next step, with clear error indicators.
* **Address Validation:** The system validates addresses with intelligent suggestions. If an invalid address is typed, no suggestions are shown, and the user is prevented from proceeding until a valid address is confirmed.
* **Invalid Contact Information:** Inputting an invalid phone number or leaving required contact name empty displays validation errors.
* **Notes Character Limit:** Attempting to type beyond 400 characters in the "Notes for Tasker" field is prevented, and the character counter accurately reflects remaining characters.
* **Network Errors:** During booking submission or payment processing, network errors trigger a message with retry options.
* **Payment Method Required:** The "Continue" button remains disabled until a payment method is selected.
* **Terms Acceptance Required:** The "Continue" button remains disabled until the "Terms and Conditions" checkbox is ticked.
* **Hair Color Not Chosen:** For "Root Touch-Up", if no color is selected, the system should prevent progression or prompt the user to choose a color.

**8. Open Questions / Assumptions**

* Are there specific protocols for different hair types (e.g., oily, dry, fine, thick) or conditions (e.g., sensitive scalp)?
* What is the policy for rescheduling hair relaxing appointments?
* How do we handle instances where a Tasker is unavailable for a chosen time slot?
* Are there additional charges for services outside normal operating hours?
* What is the cancellation policy for users and Taskers?
* How are special requests (e.g., specific hair products, extreme lengths) handled if not covered by standard options?
* What is the maximum number of people that can be serviced by a single Tasker in a single booking for hair relaxing services?
* How do "Working Process" and "Tools, Equipment, and Product" links behave if no content is available?
* Is there a review or rating system for Taskers after a service?
* What is the exact list of available hair colors for "Root Touch-Up"?
* Are there specific requirements or prerequisites for "Root Touch-Up" (e.g., existing hair color, time since last coloring)?

**9. Visual Reference**

* **Service Category Selection:** Screen showing "Makeup & Hairstyling" and "Hair Relaxing" cards.
* **Customer Count Modal:** Pop-up for selecting "1 customer" or "2 customers" with warning message.
* **Hair Relaxing Service Selection:** Screen showing "Relaxing hair wash", "Root Touch-Up" services with prices.
* **Hair Relaxing Service Details (Customer 1 & 2):** Screens showing selection for Customer 1 and options for Customer 2, including "Apply details to Customer 2" checkbox, "Hair Color" selection, and "Heat Styling" add-on.
* **Choose Execution Order Modal:** Pop-up for selecting "At the same time" or "One after the other" for multiple customers.
* **Address Selection Screens:** List of saved addresses, new address input via search/map, and detailed address/contact form.
* **Date and Time Picker:** Interactive calendar for date selection with peak day indicators and scrollable time picker.
* **Notes for Tasker Modal:** Overlay for entering special instructions with character limit.
* **Booking Summary/Payment Details (Hair Relaxing):** Screen showing chosen service, date, time, address, service cost, discount, total, "Add voucher" field, "Payment method" section (Cash, Electronic payments, Cards, BNPL), and "Terms and Conditions" checkbox.

