/**
 * Makeup Service Sequential Flow E2E Tests
 *
 * Comprehensive E2E test suite for React Native makeup service booking application
 * following strict sequential flow: Service Selection → Style Selection → Address → Date/Time → Payment/Confirmation
 *
 * Requirements:
 * - TestID-only selectors (no text expectations)
 * - Sequential flow compliance (cannot skip steps)
 * - Proper scrolling behavior for viewport management
 * - Performance optimized targeting 3-5 minute execution time
 * - Enhanced testID coverage for all interactive elements
 * - Scroll-to-reveal patterns for viewport management
 * - Comprehensive validation for single and double customer flows
 *
 * <AUTHOR> Automation Engineer
 * @framework Detox
 * @version 1.0 - Initial implementation for makeup service
 */

const { device } = require('detox');
const {
  reloadApp,
  tapId,
  waitForElement,
  expectElementVisible,
  swipe,
  scroll,
  waitForLoading,
} = require('./step-definition');

// Performance monitoring utilities
const performanceTracker = {
  startTime: null,
  stepTimes: {},

  startTest: function (testName) {
    this.startTime = Date.now();
    this.stepTimes[testName] = { start: this.startTime };
  },

  endTest: function (testName) {
    const endTime = Date.now();
    const duration = endTime - this.startTime;
    this.stepTimes[testName].end = endTime;
    this.stepTimes[testName].duration = duration;

    // Track performance if test takes longer than expected (5 minutes = 300000ms)
    if (duration > 300000) {
      // Performance warning - test exceeds target time
      this.stepTimes[testName].warning = 'Exceeds 5 minute target';
    }

    return duration;
  },
};

// Optimized wait times for better performance
const WAIT_TIMES = {
  FAST: 2000, // For quick transitions
  NORMAL: 5000, // For standard navigation
  SLOW: 8000, // For complex operations
};

describe('Makeup Service Sequential Flow', () => {
  beforeEach(async () => {
    // Performance optimized app reset
    performanceTracker.startTest('beforeEach-setup');

    // Reset app state and data for consistent testing
    await reloadApp();
    await device.reloadReactNative();

    try {
      // Use optimized wait time for faster execution
      await waitForElement('btnSubmitIntroService', WAIT_TIMES.SLOW);
      // Handle intro service screen if present
      await tapId('btnSubmitIntroService');
    } catch (error) {}

    // Wait for service selection screen
    await waitForElement('service-selection-container', WAIT_TIMES.NORMAL);

    performanceTracker.endTest('beforeEach-setup');
  });

  describe('Step 1: Service Type and Customer Selection', () => {
    it('should display service type selection and allow makeup service selection', async () => {
      // Verify service type selection screen
      await expectElementVisible('service-selection-container');
      await expectElementVisible('makeup-hairstyling-card');
      await expectElementVisible('hair-relaxing-card');

      // Select Makeup & Hairstyling service
      await tapId('makeup-hairstyling-card');

      // Verify customer count selection modal appears
      await waitForElement('customer-count-modal', WAIT_TIMES.FAST);
      await expectElementVisible('customer-count-title');
      await expectElementVisible('btn-one-customer');
      await expectElementVisible('btn-two-customers');
    });

    it('should handle single customer selection and navigate to makeup style selection', async () => {
      // Select Makeup & Hairstyling service
      await expectElementVisible('makeup-hairstyling-card');
      await tapId('makeup-hairstyling-card');

      // Select single customer
      await waitForElement('btn-one-customer', WAIT_TIMES.FAST);
      await tapId('btn-one-customer');

      // Verify navigation to makeup style selection
      await waitForElement(
        'makeup-style-selection-container',
        WAIT_TIMES.NORMAL,
      );
      await expectElementVisible('makeup-style-title');
    });

    it('should handle double customer selection and navigate to makeup style selection', async () => {
      // Select Makeup & Hairstyling service
      await expectElementVisible('makeup-hairstyling-card');
      await tapId('makeup-hairstyling-card');

      // Select double customer
      await waitForElement('btn-two-customers', WAIT_TIMES.FAST);
      await tapId('btn-two-customers');

      // Verify navigation to makeup style selection with customer indicator
      await waitForElement(
        'makeup-style-selection-container',
        WAIT_TIMES.NORMAL,
      );
      await expectElementVisible('makeup-style-title');
      await expectElementVisible('customer-label-indicator');
    });
  });

  describe('Step 2: Makeup Style Selection', () => {
    beforeEach(async () => {
      // Complete Step 1: Service and Customer Selection
      await expectElementVisible('makeup-hairstyling-card');
      await tapId('makeup-hairstyling-card');
      await waitForElement('btn-one-customer', WAIT_TIMES.FAST);
      await tapId('btn-one-customer');
      await waitForElement(
        'makeup-style-selection-container',
        WAIT_TIMES.NORMAL,
      );
    });

    it('should display makeup style carousel and allow style selection', async () => {
      // Verify makeup style selection screen
      await expectElementVisible('makeup-style-selection-container');
      await expectElementVisible('makeup-style-carousel');
      await expectElementVisible('makeup-style-title');

      // Verify carousel items are visible
      await expectElementVisible('makeup-style-item-0');

      // Test carousel navigation
      await swipe('makeup-style-carousel', 'left');
      await expectElementVisible('makeup-style-item-1');
    });

    it('should allow natural makeup selection and continue to address', async () => {
      // Select natural makeup style
      await expectElementVisible('makeup-style-item-natural');
      await tapId('makeup-style-item-natural');

      // Verify style is selected and continue button is enabled
      await expectElementVisible('btn-continue-style-selection');
      await tapId('btn-continue-style-selection');

      // Verify navigation to address selection
      await waitForElement('address-selection-container', WAIT_TIMES.NORMAL);
      await expectElementVisible('address-selection-title');
    });

    it('should allow package-based makeup selection with hairstyling add-on', async () => {
      // Select graduation/fashion makeup package
      await expectElementVisible('makeup-package-graduation');
      await tapId('makeup-package-graduation');

      // Select specific style within package
      await expectElementVisible('makeup-style-natural-option');
      await tapId('makeup-style-natural-option');

      // Add hairstyling option
      await expectElementVisible('hairstyling-addon-toggle');
      await tapId('hairstyling-addon-toggle');

      // Verify price update and continue
      await expectElementVisible('price-display-updated');
      await expectElementVisible('btn-continue-style-selection');
      await tapId('btn-continue-style-selection');

      // Verify navigation to address selection
      await waitForElement('address-selection-container', WAIT_TIMES.NORMAL);
    });
  });

  describe('Step 3: Address Selection', () => {
    beforeEach(async () => {
      // Complete Steps 1-2: Service, Customer, and Style Selection
      await expectElementVisible('makeup-hairstyling-card');
      await tapId('makeup-hairstyling-card');
      await waitForElement('btn-one-customer', WAIT_TIMES.FAST);
      await tapId('btn-one-customer');
      await waitForElement(
        'makeup-style-selection-container',
        WAIT_TIMES.NORMAL,
      );

      // Select makeup style
      await expectElementVisible('makeup-style-item-natural');
      await tapId('makeup-style-item-natural');
      await expectElementVisible('btn-continue-style-selection');
      await tapId('btn-continue-style-selection');
      await waitForElement('address-selection-container', WAIT_TIMES.NORMAL);
    });

    it('should display address selection screen and allow existing address selection', async () => {
      // Verify address selection screen
      await expectElementVisible('address-selection-container');
      await expectElementVisible('address-selection-title');
      await expectElementVisible('saved-addresses-list');

      // Select first saved address
      await expectElementVisible('saved-address-item-0');
      await tapId('saved-address-item-0');

      // Verify navigation to date/time selection
      await waitForElement('date-time-selection-container', WAIT_TIMES.NORMAL);
      await expectElementVisible('date-time-selection-title');
    });

    it('should allow new address creation and selection', async () => {
      // Tap add new address
      await expectElementVisible('btn-add-new-address');
      await tapId('btn-add-new-address');

      // Verify new address screen
      await waitForElement('new-address-container', WAIT_TIMES.FAST);
      await expectElementVisible('address-input-field');
      await expectElementVisible('contact-name-input');
      await expectElementVisible('phone-number-input');

      // Fill address details (Note: This would require actual input in real test)
      // For E2E test structure, we'll simulate the flow
      await expectElementVisible('btn-confirm-address');
      await tapId('btn-confirm-address');

      // Verify navigation to date/time selection
      await waitForElement('date-time-selection-container', WAIT_TIMES.NORMAL);
    });
  });

  describe('Step 4: Date and Time Selection', () => {
    beforeEach(async () => {
      // Complete Steps 1-3: Service, Customer, Style, and Address Selection
      await expectElementVisible('makeup-hairstyling-card');
      await tapId('makeup-hairstyling-card');
      await waitForElement('btn-one-customer', WAIT_TIMES.FAST);
      await tapId('btn-one-customer');
      await waitForElement(
        'makeup-style-selection-container',
        WAIT_TIMES.NORMAL,
      );

      // Select makeup style
      await expectElementVisible('makeup-style-item-natural');
      await tapId('makeup-style-item-natural');
      await expectElementVisible('btn-continue-style-selection');
      await tapId('btn-continue-style-selection');
      await waitForElement('address-selection-container', WAIT_TIMES.NORMAL);

      // Select address
      await expectElementVisible('saved-address-item-0');
      await tapId('saved-address-item-0');
      await waitForElement('date-time-selection-container', WAIT_TIMES.NORMAL);
    });

    it('should display date and time selection screen correctly', async () => {
      // Verify main container
      await expectElementVisible('scrollDetailStep3');
      await expectElementVisible('date-time-selection-title');

      // Verify date/time picker elements are accessible
      await expectElementVisible('date-picker-component');
      await expectElementVisible('time-picker-component');

      // Scroll to reveal additional options and continue button
      await swipe('scrollDetailStep3', 'up');
      await expectElementVisible('btn-continue-date-time');
    });

    it('should allow date and time selection with proper navigation', async () => {
      // Test date picker interaction - select a specific weekday
      await expectElementVisible('date-picker-component');
      await tapId('date-picker-weekday-2');

      // Test time picker interaction
      await expectElementVisible('time-picker-component');

      // Scroll to reveal task notes and other options
      await swipe('scrollDetailStep3', 'up');

      // Navigate to confirmation step
      await expectElementVisible('btn-continue-date-time');
      await tapId('btn-continue-date-time');

      // Verify navigation to confirmation step
      await waitForElement('scrollViewStep4', WAIT_TIMES.NORMAL);
      await expectElementVisible('confirmation-payment-title');
    });

    it('should handle notes input and earliest available time selection', async () => {
      // Test earliest available time selection
      await expectElementVisible('btn-earliest-available');
      await tapId('btn-earliest-available');

      // Test notes input functionality
      await expectElementVisible('btn-add-notes');
      await tapId('btn-add-notes');
      await waitForElement('notes-input-modal', WAIT_TIMES.FAST);
      await expectElementVisible('notes-text-input');
      await expectElementVisible('btn-save-notes');
      await tapId('btn-save-notes');

      // Continue to confirmation
      await expectElementVisible('btn-continue-date-time');
      await tapId('btn-continue-date-time');
      await waitForElement('scrollViewStep4', WAIT_TIMES.NORMAL);
    });
  });

  describe('Step 5: Payment and Confirmation', () => {
    beforeEach(async () => {
      // Complete Steps 1-4: Full flow to confirmation
      await expectElementVisible('makeup-hairstyling-card');
      await tapId('makeup-hairstyling-card');
      await waitForElement('btn-one-customer', WAIT_TIMES.FAST);
      await tapId('btn-one-customer');
      await waitForElement(
        'makeup-style-selection-container',
        WAIT_TIMES.NORMAL,
      );

      // Select makeup style
      await expectElementVisible('makeup-style-item-natural');
      await tapId('makeup-style-item-natural');
      await expectElementVisible('btn-continue-style-selection');
      await tapId('btn-continue-style-selection');
      await waitForElement('address-selection-container', WAIT_TIMES.NORMAL);

      // Select address
      await expectElementVisible('saved-address-item-0');
      await tapId('saved-address-item-0');
      await waitForElement('date-time-selection-container', WAIT_TIMES.NORMAL);

      // Select date/time
      await expectElementVisible('date-picker-weekday-2');
      await tapId('date-picker-weekday-2');
      await expectElementVisible('btn-continue-date-time');
      await tapId('btn-continue-date-time');
      await waitForElement('scrollViewStep4', WAIT_TIMES.NORMAL);
    });

    it('should display payment and confirmation screen correctly', async () => {
      // Verify main container and booking summary
      await expectElementVisible('scrollViewStep4');
      await expectElementVisible('booking-summary-container');
      await expectElementVisible('service-details-display');
      await expectElementVisible('address-details-display');
      await expectElementVisible('date-time-details-display');

      // Test scrolling to reveal payment options
      await swipe('scrollViewStep4', 'up');
      await expectElementVisible('payment-method-section');
      await expectElementVisible('terms-conditions-checkbox');
      await expectElementVisible('btn-submit-booking');
    });

    it('should handle payment method selection and terms acceptance', async () => {
      // Scroll to payment section
      await swipe('scrollViewStep4', 'up');

      // Test payment method selection
      await expectElementVisible('payment-method-cash');
      await tapId('payment-method-cash');

      // Test promotion code input
      await expectElementVisible('btn-add-promotion');
      await tapId('btn-add-promotion');
      await waitForElement('promotion-input-modal', WAIT_TIMES.FAST);
      await expectElementVisible('promotion-code-input');
      await expectElementVisible('btn-apply-promotion');
      await tapId('btn-apply-promotion');

      // Accept terms and conditions
      await expectElementVisible('terms-conditions-checkbox');
      await tapId('terms-conditions-checkbox');

      // Verify submit button is enabled
      await expectElementVisible('btn-submit-booking');

      // Note: In real test, would proceed with booking submission
      // await tapId('btn-submit-booking');
    });

    it('should handle contact information editing', async () => {
      // Test contact info editing
      await expectElementVisible('btn-edit-contact-info');
      await tapId('btn-edit-contact-info');

      // Verify edit contact modal
      await waitForElement('edit-contact-modal', WAIT_TIMES.FAST);
      await expectElementVisible('contact-name-input');
      await expectElementVisible('contact-phone-input');
      await expectElementVisible('btn-save-contact-changes');
      await tapId('btn-save-contact-changes');

      // Verify changes are reflected
      await expectElementVisible('updated-contact-display');
    });
  });

  describe('Complete Sequential Flow Tests', () => {
    it('should complete entire single customer makeup booking flow successfully', async () => {
      performanceTracker.startTest('single-customer-complete-flow');

      // Step 1: Service and Customer Selection
      await expectElementVisible('makeup-hairstyling-card');
      await tapId('makeup-hairstyling-card');
      await waitForElement('btn-one-customer', WAIT_TIMES.FAST);
      await tapId('btn-one-customer');
      await waitForElement(
        'makeup-style-selection-container',
        WAIT_TIMES.NORMAL,
      );

      // Step 2: Makeup Style Selection
      await expectElementVisible('makeup-style-item-natural');
      await tapId('makeup-style-item-natural');
      await expectElementVisible('btn-continue-style-selection');
      await tapId('btn-continue-style-selection');
      await waitForElement('address-selection-container', WAIT_TIMES.NORMAL);

      // Step 3: Address Selection
      await expectElementVisible('saved-address-item-0');
      await tapId('saved-address-item-0');
      await waitForElement('date-time-selection-container', WAIT_TIMES.NORMAL);

      // Step 4: Date/Time Selection
      await expectElementVisible('date-picker-weekday-2');
      await tapId('date-picker-weekday-2');
      await expectElementVisible('btn-continue-date-time');
      await tapId('btn-continue-date-time');
      await waitForElement('scrollViewStep4', WAIT_TIMES.NORMAL);

      // Step 5: Payment and Confirmation
      await expectElementVisible('booking-summary-container');
      await swipe('scrollViewStep4', 'up');
      await expectElementVisible('terms-conditions-checkbox');
      await tapId('terms-conditions-checkbox');
      await expectElementVisible('btn-submit-booking');

      // Single customer makeup booking flow completed successfully
      performanceTracker.endTest('single-customer-complete-flow');
      // Target: Complete flow should take less than 3 minutes (180000ms)
    });

    it('should complete entire double customer makeup booking flow with duplicate option', async () => {
      performanceTracker.startTest('double-customer-duplicate-flow');

      // Step 1: Service and Customer Selection (Double)
      await expectElementVisible('makeup-hairstyling-card');
      await tapId('makeup-hairstyling-card');
      await waitForElement('btn-two-customers', WAIT_TIMES.FAST);
      await tapId('btn-two-customers');
      await waitForElement(
        'makeup-style-selection-container',
        WAIT_TIMES.NORMAL,
      );

      // Step 2: Makeup Style Selection for Customer 1
      await expectElementVisible('customer-label-indicator');
      await expectElementVisible('makeup-style-item-natural');
      await tapId('makeup-style-item-natural');

      // Apply details to customer 2
      await expectElementVisible('apply-to-customer-2-checkbox');
      await tapId('apply-to-customer-2-checkbox');

      await expectElementVisible('btn-continue-style-selection');
      await tapId('btn-continue-style-selection');
      await waitForElement('address-selection-container', WAIT_TIMES.NORMAL);

      // Step 3: Address Selection
      await expectElementVisible('saved-address-item-0');
      await tapId('saved-address-item-0');
      await waitForElement('date-time-selection-container', WAIT_TIMES.NORMAL);

      // Step 4: Date/Time Selection with execution order
      await expectElementVisible('date-picker-weekday-3');
      await tapId('date-picker-weekday-3');
      await expectElementVisible('btn-continue-date-time');
      await tapId('btn-continue-date-time');

      // Handle execution order modal for 2 customers
      await waitForElement('execution-order-modal', WAIT_TIMES.FAST);
      await expectElementVisible('btn-same-time-execution');
      await tapId('btn-same-time-execution');
      await waitForElement('scrollViewStep4', WAIT_TIMES.NORMAL);

      // Step 5: Payment and Confirmation
      await expectElementVisible('booking-summary-container');
      await swipe('scrollViewStep4', 'up');
      await expectElementVisible('terms-conditions-checkbox');
      await tapId('terms-conditions-checkbox');
      await expectElementVisible('btn-submit-booking');

      // Double customer makeup booking flow completed successfully
      performanceTracker.endTest('double-customer-duplicate-flow');
      // Target: Complete flow should take less than 4 minutes (240000ms)
    });
  });
});
