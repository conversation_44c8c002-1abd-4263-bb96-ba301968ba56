**Business Design Document - bBeauty Makeup Service**

**1. Purpose** Enable users to book professional makeup and hairstyling services via a mobile app, allowing them to specify the service type, makeup style, desired location, schedule, and payment method for certified makeup artists (Taskers) to provide comprehensive beauty services for various events and occasions.

**2. Scope / Features**

* **Choose Service Type**
  * **Service Category Selection:** Users can choose from primary beauty service categories such as "Makeup & Hairstyling" and "Hair Relaxing".
  * **Customer Count:** Users specify the number of customers for the service (currently limited to 1 or 2 per task).
* **Choose Task Details**
  * **Occasion-Based Packages:** Support for various occasion-specific makeup packages (e.g., Graduation / Fashion Makeup, Party / Event Makeup, Bridesmaid Makeup).
  * **Style Selection:** Users can select from predefined makeup styles (e.g., Natural, Neutral, Dramatic).
  * **Optional Hairstyling Add-on:** For each selected makeup style, users have the option to add a complementary hairstyling service.
  * **Multi-Customer Detail Application:** When booking for 2 customers, users have the option to apply the makeup style and add-on details of the first customer to the second customer.
  * **Style Details & Examples:** Users can access detailed descriptions and view multiple photo examples for each makeup style.
  * **Dynamic Pricing:** Real-time pricing calculation based on the selected makeup style and package.
* **Choose an Address**
  * **Location Selection:** Users can choose their service location from existing saved addresses or create new addresses for booking.
  * **Address and Contact Management:** Handle contact details associated with each location including name and phone number for service coordination, allowing users to save frequently used addresses and set preferences for future bookings.
* **Choose Working Time**
  * **Date and Time Selection:** Flexible scheduling system allowing users to choose preferred appointment dates and times.
  * **Notes Pop-up:** Text-based communication system allowing users to provide specific instructions and requests to service providers.
  * **Choose Execution Order Pop-up (for 2 Customers)**
    * **Simultaneous/Sequential Service:** Users can specify if two customers receive service "At the same time" (by two Taskers) or "One after the other" (by one Tasker).
* **Confirm and Payment**
  * **Booking Confirmation:** Comprehensive booking summary and confirmation system displaying all service details before final commitment.
  * **Payment Processing:** Multi-channel payment system supporting various payment methods including digital wallets, cards, and alternative payment options.
  * **Promotion Management:** Discount and promotion code system allowing users to apply available offers and see immediate savings.
  * **Contact Updates:** Real-time contact information editing capabilities during the booking confirmation process.

**3. Stakeholders**

* **End Users:** Individuals booking makeup and hairstyling services for personal events, professional needs, or special occasions.
* **Makeup Artists / Hairstylists (Taskers):** Certified beauty professionals who will execute the services according to user specifications and professional standards.
* **Admin:** Platform administrators responsible for service quality oversight, scheduling coordination, pricing management, promotion oversight, and overall system maintenance.

**4. Functional Requirements**

**4.1 Choose Service Type**

* **Service Category Display:** The system shall display clear cards for "Makeup & Hairstyling" and "Hair Relaxing" as primary service categories.
* **Customer Count Selection:** Upon selecting "Makeup & Hairstyling," the system shall prompt the user to select the number of customers (1 or 2).
* **Booking Limitation Notification:** The system shall display a warning stating that only a maximum of 02 service packages are supported per task and advise booking an additional task for more customers.

**4.2 Choose Task Details**

* **Makeup Style Carousel:** The system shall present makeup styles in a horizontal, scrollable carousel format.
* **Makeup package Options:** Users can select from:
  * "Graduation / Fashion Makeup" (Price Range: 450,000₫ \~ 800,000₫)
  * "Party / Event Makeup" (Price Range: 450,000₫ \~ 800,000₫)
  * "Bridesmaid Makeup" (Price Range: 450,000₫ \~ 800,000₫
* **Style Options:** Users can select from:
  * "Natural Makeup" (Price: 450,000₫)
  * "Neutral Makeup" (Price: 650,000₫)
  * "Dramatic Makeup" (Price: 800,000₫)
* **Hairstyling Add-on:**

  For each makeup style selected:
  * The system shall present a clear option (e.g., a toggle or checkbox) to "Add Hairstyling" or "Include Hairstyling."
  * If "Add Hairstyling" is selected, the system shall dynamically update the total price to reflect the combined makeup and hairstyling service cost.
  * The system shall display the duration for the combined service (e.g., makeup + hairstyling duration).
* **Multi-Customer Detail Application:** If 2 customers are selected, the system shall provide an option (e.g., checkbox "Apply details to customer 2") that, when activated, automatically applies the makeup style and add-on selections from customer 1 to customer 2, and disables/hides independent selection options for customer 2.
* **Style Details Access:**
  * "View more photos" option shall be available for each style to display a full-screen image gallery.
  * "Learn More" option shall display a modal with a detailed description of the makeup style (e.g., "Natural Makeup: Light, dewy base with skincare or cushion; Soft, natural eyebrows; Minimal eyeshadow; Soft blush; Tinted lip balm. Best for: Daily wear, school, work, casual dates, natural look.")
* **Dynamic Pricing Display:** The total price and estimated duration (e.g., "90 minutes or 2 hours") shall be dynamically updated and displayed at the bottom of the screen based on the selected makeup style.

**4.3 Choose an Address**

* **Address Selection Options:** Users can select from previously saved addresses or add new addresses through multiple intuitive input methods.
* **Multi-Input Address System:** Users can input addresses through three distinct methods:
  * GPS location detection for automatic current location identification and nearby address suggestion.
  * Manual text entry with auto-complete and address suggestion functionality for precise address specification.
  * Interactive map selection with pin-dropping capability for visual location picking and address confirmation.
* **Address Validation and Formatting:** The system validates the address. Users receive feedback if the address is invalid and are prevented from proceeding with booking.
* **Address and Contact Information Management:** Users can store and manage both address details and associated contact information including contact name and phone number with validation, save addresses, edit existing addresses, and set a default address for streamlined future booking experiences.

**4.4 Choose Working Time**

* **Calendar Date Selection:** Users can interact with an interactive calendar interface with the following specifications:
  * Users see current date clearly highlighted and marked as starting reference point.
  * Users can select from next 7 consecutive days displayed as available booking options.
  * The system shall indicate "Peak days" with a distinct visual marker (e.g., orange dot).
* **Time Selection Interface:** Users can utilize an intuitive time picker with comprehensive options:
  * Users see 12-hour format with clear AM/PM designation.
  * Users can select 5-minute interval options for flexible scheduling.
  * An "Earliest available" button shall be present to quickly select the next open slot.
* **Notes Pop-up:** Users can utilize a robust text input system for special instructions:
  * Users can leave the optional text field empty without preventing booking completion.
  * The system enforces a maximum character limit (e.g., 400 characters) with a real-time character counter and remaining count display.
  * Users can tick a checkbox labeled "Use for next booking" to save the entered notes as a default for future bookings.
* **Choose Execution Order Pop-up (for 2 Customers)**
  * **Execution Order Selection:** If 2 customers are selected, the system shall present a modal allowing the user to choose between:
    * "At the same time": Service provided simultaneously by two different Taskers.
    * "One after the other": Service provided sequentially by the same Tasker.
  * **Order Confirmation:** The system shall register the user's selected execution order.

**4.5 Confirm and Payment**

* **Complete Booking Summary Display:** Users can review comprehensive booking information:
  * Users see scheduled date and time
  * Users see detailed service type (e.g., "Makeup"), specific makeup style (e.g., "Graduation / Fashion Makeup", "Natural Makeup") and payment details (Service Cost/ Discount)
  * Users see complete address display with contact information (name and phone number).
  * Users can edit contact name and phone number directly in the booking summary via an "Edit" or pencil button.
  * The system displays Notes for Tasker notes if provided.
  * Users see estimated service duration (e.g., "90 minutes").
* **Advanced Promotion Code Management:** Users can access comprehensive promotion functionality:
  * Users can enter promotion codes in dedicated text input field with format validation.
  * Users see automatic discount calculation and application to total pricing with immediate visual feedback.
  * Users can remove promotion codes with pricing recalculation.
* **Comprehensive Payment Method Support:** Users can select from multiple payment channels with robust processing:
  * Users can choose "Cash" payment option with clear instructions for payment upon service completion.
  * Users can use digital wallet integration including bPay, Momo, ZaloPay, ShopeePay with secure connections.
  * Users can process credit card payments for Visa and MasterCard with secure 3D authentication.
  * Users can access buy-now-pay-later option through Kredivo with installment calculation and approval workflow.
  * The system provides payment method validation and error handling for failed transactions.
* **Terms and Conditions Acceptance:** A checkbox for "I have read and understood the terms and conditions of bTaskee" must be present. The "terms and conditions" text shall be a tappable link to the full terms.
* **Confirmation Process:** The system ensures robust booking processing:
  * A "Continue" button shall be prominently displayed.
  * The button shall be enabled only when all required information is complete (e.g., terms and conditions accepted, payment method selected).
  * The system automatically navigates users to a "Success Booking" screen upon successful transaction completion.

**5. Non-Functional Requirements**

* **Mobile-first Responsive Design:** Optimized for makeup service booking on various mobile devices.
* **Vietnamese Language Support:** Clear, professional interface in Vietnamese.
* **Real-time Validation and User Feedback:** Immediate feedback for invalid inputs or selections.
* **Secure Handling:** Secure handling of personal and payment information.
* **Reliable Tasker Matching:** Efficient and reliable matching and scheduling system for makeup artists.
* **Performance Optimization:** Fast pricing calculations and highly responsive UI.
* **Accessibility Features:** Designed with accessibility considerations for all users.

**6. Acceptance Criteria (Testable Steps)**

**6.1 Choose Service Type**

* **Select Makeup & Hairstyling:** User taps the "Makeup & Hairstyling" card from the main service selection screen.
* **Select Customer Count (1):** User selects "1 customer" from the "Number of customers" modal. The system registers 1 customer for the service and navigates to the **Choose Makeup Style page**
* **Select Customer Count (2):** User selects "2 customers" from the "Number of customers" modal. The system registers 2 customer for the service and navigates to the **Choose Makeup Style page**
* **Attempt Invalid Customer Count:** User cans see the "Only supports a maximum of 02 service packages per task..." warning.

**6.2 Choose Task Details**

* **Browse Styles:** User swipes horizontally through the makeup style carousel to view all available options.
* **Select a Price-Range Makeup Package:**
  * **Options:**
    * Graduation / Fashion Makeup
    * Party / Event Makeup
    * Bridesmaid Makeup
  * User taps on one of the price-range makeup packages. The system visually highlights the selected package
  * The system accurately updates the "Total" price displayed at the bottom of the screen to reflect the base price of the selected makeup package
  * The system displays the "Total" duration (e.g., "90 minutes") for the makeup-only service.
  * **Information Access (Working Process, Tools/Equipment/Product):**
    * **View Working Process:** User taps the "Working Process" section/button. The system displays a modal or navigates to a new screen detailing the step-by-step working process. The system allows the user to close this view.
    * **View Tools, Equipment, and Product:** User taps the "Tools, Equipment, and Product" section/button. The system displays a modal or navigates to a new screen detailing the tools, equipment, and products used for the service. The system allows the user to close this view.
* **Select a Fixed-Price Makeup Style:**
  * **Options:**
    * Natural Makeup
    * Neutral Makeup
    * Dramatic Makeup
  * User taps on **one of the fixed-price makeup styles**
  * The system visually highlights the selected style. The "Total" price displayed at the bottom of the screen accurately reflects the base price of the selected makeup style. The "Total" duration (e.g., "90 minutes") for the makeup-only service is displayed.
  * **Optional: Add Hairstyling:**
    * User taps the option to "Add Hairstyling" for the currently selected makeup style.
    * The system dynamically updates the "Total" price at the bottom of the screen to reflect the combined cost of makeup + hairstyling.
    * The system updates the "Total" duration to reflect the estimated time for both services.
    * The system visually marks the hairstyling option as selected.
  * **View Photos:** User taps "View more photos" for the selected makeup style.
    * The system displays a detailed description modal, outlining the characteristics and suitable occasions for the style. The system closes the modal upon tapping "Close".
  * **Information Access (Working Process, Tools/Equipment/Product):**
    * **View Working Process:** User taps the "Working Process" section/button. The system displays a modal or navigates to a new screen detailing the step-by-step working process. The system allows the user to close this view.
    * **View Tools, Equipment, and Product:** User taps the "Tools, Equipment, and Product" section/button. The system displays a modal or navigates to a new screen detailing the tools, equipment, and products used for the service. The system allows the user to close this view.
    * **Learn More:** User taps "Learn More" for the selected makeup style. A detailed description modal appears, outlining the characteristics and suitable occasions for the style. User taps "Close" to dismiss the modal.
* **Multi-customer Details Application (for 2 Customers):**
  * **Scenario Setup:** After the user has selected "2 customers" for the service, when navigating to the hair relaxing service selection screen, the system displays distinct sections for "Customer 1's Details" and "Customer 2's Details". Additionally, the system presents a checkbox (e.g., "Apply Customer 1's details to Customer 2") usually near the customer detail sections.
  * **Apply Details to Customer 2 (Ticked):**
    * When the user ticks the "Apply Customer 1's details to Customer 2" checkbox (after selecting options for Customer 1), the system automatically copies the selected hair service (e.g., Relaxing hair wash), add-ons (e.g., Heat Styling), and specific details (e.g., Hair Color) from Customer 1 to Customer 2.
    * Concurrently, the independent selection options (e.g., service cards, add-on toggles, color selection) within "Customer 2's Details" section become disabled or hidden, preventing the user from making separate choices for Customer 2. The total price and duration are updated to reflect the duplicated service.
  * **Independent Selection for Customer 2 (Not Ticked):**
    * If the user *does not* tick the "Apply Customer 1's details to Customer 2" checkbox, the system keeps the selection options within "Customer 2's Details" active.
    * The user can then proceed to independently select hair services and add-ons for Customer 2, which will update the total price and duration accordingly.
* **Proceed to Address Selection:** User taps "Continue" button after selecting a makeup style (and optionally hairstyling).

**6.3 Choose an Address**

* **Existing Location Selection:** Users can select an already saved address from the "Your saved address" if available.
* **Choose Location Screen (New Address Entry):** Tapping "Choose new address" section navigates to the "Choose new address" screen, where users can add a new location by:
  * Using GPS to automatically detect their current location.
  * Manually entering full address details (e.g., "T" input in screenshot, then full address).
  * Tapping on the map and confirming with "Pick this location."
* **Location Detail Input & Confirmation:** After selecting or adding a location, the system displays a popup for users to input complete address details:
  * "Selected address" (pre-filled from map/search) can be edit.
  * "Phone number" (validated for Vietnamese format, pre-filled if from existing contact).
  * "Contact name" (pre-filled if from existing contact).
  * "House number" (free text input).
  * Users can tap "Confirm" to save these address and contact details.
  * Users may edit all address and contact fields before final confirmation.
* **Proceed to Working Time Selection:**

  User selects one of the existing addresses or adds a new one. The system navigates to the 'Choose working time' page with the selected address.

**6.4 Choose Working Time**

**Calendar Date Selection:** Upon navigating to the "Choose working time" screen, users can interact with an interactive calendar interface with the following specifications:

* Users see current date clearly highlighted and marked as starting reference point
* The system displays an orange dot on peak days (if any) within this 7-day window.
* Users see holiday and peak demand period indicators where applicable

**Time Selection Interface:** Users can utilize an intuitive time picker with comprehensive options:

* Users see 12-hour format with clear AM/PM designation
* Users can select 5-minute interval options for flexible scheduling

**Select Earliest Available Time:**

* When the user taps 'Earliest available', the system selects the nearest open time slot and updates the total price and duration accordingly.

**Add Notes at Notes Pop-up:** User taps to open the 'Notes' modal. The user enters text within the 400-character limit and can optionally check 'Use for next booking.' The user can tap outside to dismiss the modal or tap 'Continue' to navigate to the Confirm and Pay page.

**Choose Execution Order (for 2 Customers)**

* **Access Execution Order Modal:** If 2 customers are selected, after choosing service details for both, the system prompts the user with the "Choose the execution order" modal (e.g., before or after time selection).
* **Select "At the same time":** User taps the "At the same time" option. The system selects this option and user taps "Confirm". The booking process continues, implying two Taskers for simultaneous service.
* **Select "One after the other":** User taps the "One after the other" option. The system selects this option and user taps "Confirm". The booking process continues, implying one Tasker for sequential service.

**6.5 Confirmation and Payment**

**6.5.1 Booking Summary & Confirmation**

* **Review Details:** User navigates to the "Payment details" screen.
  * Verifies "Working time" (e.g., "EEEE, hh:mm AM/PM - MM/dd/yyyy").
  * Verifies "Task details"
  * Verifies "Payment details" (Service Cost, Discount, Total Payment).
  * Verifies "Address" (full address and address details, contact details as name and phone number).
  * Verifies "Note" if provided
* **Edit Contact Information:** User taps the pencil icon the Address section. The "Change info" modal appears. User changes name/phone number and taps "Confirm." The updated details are reflected in the summary.

**6.5.2 Payment Method Selection**

* **Select Cash:** User taps on "Cash" under "Payment method". The system stores this selection.
* **Select Electronic Payment:** User taps on "Cash" and then selects a digital wallet (e.g., Momo, ZaloPay, ShopeePay,…). The system stores this selection and navigates back to the summary.
* **Select Card Payment:** User taps on "Cash" and selects "Visa/Master". If no card is saved, the system navigates to "Add Card" screen. User inputs card details and saves. The system navigates back to the summary.
* Users can process credit card payments for Visa and MasterCard with secure 3D authentication
* Users can access buy-now-pay-later option through Kredivo with installment calculation and approval workflow
* The system provides payment method validation and error handling for failed transactions

**6.5.3 Promotion Code Handling:**

* **Apply Valid Code:** User taps "Add voucher", enters a valid promotion code (e.g., "BEAUTY10"), and taps apply.
  * The "Discount" amount updates (e.g., from "0₫" to "-45,000₫").
  * The "Total Payment" recalculates
* **Apply Invalid Code:** User enters an invalid promotion code.
  * A notification "The promotion code is invalid. Please check again." is displayed.
  * The discount and total price remain unchanged.

**6.5.4 Terms and Conditions:**

* **Accept Terms:** User checks the "I have read and understood the terms and conditions of bTaskee" checkbox.
  * The "Continue" (or "Book") button changes from greyed out to active.
* **View Terms:** User taps on the underlined "terms and conditions" text.
  * A new screen/modal displaying the full terms and conditions content appears. User closes this screen/modal.

**6.5.5 Final Confirmation:**

* **Successful Booking:** User taps the active "Continue"  button.
  * The system processes the booking.
  * The user is automatically navigated to the "Success Booking" screen.

**7. Error Handling & Validation**

* **Past Date/Time Selection:** Attempting to select a past date or time shows an appropriate error message and prevents selection.
* **Incomplete Address Fields:** Incomplete required fields in the address input prevent progression to the next step, with clear error indicators.
* **Address Validation:** The system validates addresses with intelligent suggestions. If an invalid address is typed, no suggestions are shown, and the user is prevented from proceeding until a valid address is confirmed.
* **Invalid Contact Information:** Inputting an invalid phone number or leaving required contact name empty displays validation errors.
* **Notes Character Limit:** Attempting to type beyond 400 characters in the "Notes for Tasker" field is prevented, and the character counter accurately reflects remaining characters.
* **Network Errors:** During booking submission or payment processing, network errors trigger a message with retry options.
* **Payment Method Required:** The "Continue" (or "Book") button remains disabled until a payment method is selected.
* **Terms Acceptance Required:** The "Continue" (or "Book") button remains disabled until the "Terms and Conditions" checkbox is ticked.

**8. Open Questions / Assumptions**

* What is the specific process for a "matching hairstyling" that is included in the makeup packages?
* Are there specific protocols for different skin types or sensitivities?
* What is the policy for rescheduling makeup appointments?
* How do we handle instances where a Tasker is unavailable for a chosen time slot?
* Are there additional charges for services outside normal operating hours?
* What is the cancellation policy for users and Taskers?
* How are special requests (e.g., specific brands, complex styles) handled if not covered by standard options?
* What is the maximum number of people that can be serviced by a single Tasker in a single booking?
* How do "View more photos" and "Learn More" links behave if no content is available?
* Is there a review or rating system for Taskers after a service?

**9. Visual Reference**

* **Service Category Selection:** Screen showing "Makeup & Hairstyling" and "Hair Relaxing" cards.
* **Customer Count Modal:** Pop-up for selecting "1 customer" or "2 customers" with warning message.
* **Makeup Style Carousel:** Horizontal scrollable view of "Natural Makeup", "Neutral Makeup", "Dramatic Makeup", "Graduation / Fashion Makeup", "Party / Event Makeup", "Bridesmaid Makeup" (with prices/ranges).
* **Makeup Style Details Modal:** Pop-up displaying description and "Best for" for a specific style (e.g., Natural Makeup).
* **Makeup Style Photo Gallery:** Full-screen photo viewer (e.g., "1/2" indicator).
* **Address Selection Screens:** List of saved addresses, new address input via search/map, and detailed address/contact form.
* **Date and Time Picker:** Interactive calendar for date selection with peak day indicators and scrollable time picker.
* **Notes for Tasker Modal:** Overlay for entering special instructions with character limit.
* **Booking Summary/Payment Details:** Screen showing chosen service, date, time, address, service cost, discount, total, "Add voucher" field, "Payment method" section (Cash, Electronic payments, Cards, BNPL), and "Terms and Conditions" checkbox.
* **Edit Contact Details Modal:** Overlay for changing contact name and phone number.

