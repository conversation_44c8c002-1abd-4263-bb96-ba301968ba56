# Makeup Service E2E Testing Quick Reference Guide

## 🚀 Quick Start

### Prerequisites Check
```bash
# Verify installations
node --version    # Should be >= 18
yarn --version    # Latest
detox --version   # Should be >= 20.20.1
xcode-select -p   # Should show Xcode path (iOS)
echo $ANDROID_HOME # Should show Android SDK path (Android)
```

### One-Command Setup
```bash
cd apps/host
yarn install
yarn e2e:build:ios

# Navigate to project
cd remotes/service-beauty-care

# Install and run (iOS)
yarn e2e:test:ios

# Install and run (Android)
yarn e2e:test:android
```

## 📋 Essential Commands

### Build Commands
```bash
yarn e2e:build              # Build for default platform
yarn e2e:build:ios          # Build iOS app
yarn e2e:build:android      # Build Android app
```

### Test Commands
```bash
yarn e2e:test               # Run all tests
yarn e2e:test:ios           # Run on iOS simulator
yarn e2e:test:android       # Run on Android emulator
yarn e2e:test:verbose       # Run with detailed logging
yarn e2e:test:debug         # Run with full debugging
```

### Maintenance Commands
```bash
yarn e2e:clean              # Clean framework cache
yarn e2e:rebuild            # Rebuild framework cache
yarn e2e:ci                 # CI pipeline (build + test + cleanup)
```

### Debug Commands
```bash
# Maximum logging
detox test --loglevel trace

# Record everything
detox test --record-logs all --take-screenshots all --record-videos all

# Run specific test
detox test e2e/MakeupServiceSequentialFlow.e2e.js

# Run with specific configuration
detox test -c ios
detox test -c android
```

## 🔧 Configuration Files

### Key Files
- `.detoxrc.js` - Main Detox configuration
- `e2e/jest.config.js` - Jest test runner configuration
- `e2e/setup.ts` - Test environment setup
- `e2e/step-definition.js` - Helper functions

### Device Configuration
```javascript
// iOS: iPhone 16 Pro Max Simulator
// Android: Pixel 6 API 34 Emulator
// Ports: 8081 (Metro), 9003 (service-beauty-care)
```

## 🐛 Quick Troubleshooting

### Common Fixes
```bash
# Build issues
yarn e2e:clean && yarn install

# Simulator issues (iOS)
xcrun simctl erase all

# Emulator issues (Android)
adb kill-server && adb start-server

# Port conflicts
kill -9 $(lsof -ti:8081) && kill -9 $(lsof -ti:9003)

# Cache issues
rm -rf node_modules && yarn install
```

### Error Patterns
| Error | Quick Fix |
|-------|-----------|
| "App not found" | `yarn e2e:build` |
| "Device not found" | Check simulator/emulator |
| "Port in use" | Kill port processes |
| "Element not found" | Check testID exists |
| "Timeout" | Increase timeout values |

## 📊 Test Structure

### Test Flow
1. **Service Selection** → Choose Makeup & Hairstyling + Customer Count
2. **Style Selection** → Select makeup style and options
3. **Address Selection** → Choose or create service address
4. **Date/Time Selection** → Pick appointment time and add notes
5. **Payment/Confirmation** → Review details and submit booking

### Key TestIDs
```javascript
// Navigation
'makeup-hairstyling-card', 'btn-one-customer', 'btn-continue-style-selection', 'btn-submit-booking'

// Scrolling
'service-selection-container', 'makeup-style-selection-container', 'scrollDetailStep3', 'scrollViewStep4'

// Selection
'makeup-style-item-natural', 'saved-address-item-0', 'date-picker-weekday-2'
```

## 🎯 Best Practices

### Do's
- ✅ Use testID-only selectors
- ✅ Follow sequential flow (no step skipping)
- ✅ Clean app state between tests
- ✅ Use progressive scrolling
- ✅ Handle timeouts gracefully

### Don'ts
- ❌ Use text-based selectors
- ❌ Skip mandatory flow steps
- ❌ Hardcode wait times
- ❌ Ignore error handling
- ❌ Run tests in parallel

## 📈 Performance Tips

### Optimization
- Use single worker (`maxWorkers: 1`)
- Optimize timeouts (balance speed vs reliability)
- Clean cache regularly
- Monitor system resources
- Use efficient scrolling patterns

### Timing Guidelines
- **App Launch**: ~30 seconds
- **Single Test**: 8-20 seconds
- **Complete Flow**: ~65 seconds
- **Full Suite**: ~5 minutes

## 🔄 CI/CD Integration

### GitLab CI Example
```yaml
e2e_makeup_tests:
  stage: test
  script:
    - cd remotes/service-beauty-care
    - yarn install
    - yarn e2e:ci
  artifacts:
    when: always
    paths:
      - artifacts/
```

### Artifact Locations
- Screenshots: `./artifacts/screenshots/`
- Videos: `./artifacts/videos/`
- Logs: `./artifacts/logs/`

## 📞 Support Resources

### Documentation
- [Test Implementation Guide](./TEST_IMPLEMENTATION_GUIDE.md) - Detailed implementation instructions
- [Missing TestIDs](./MISSING_TESTIDS.md) - Required testID attributes
- [BDD Document](../docs/bBeauty_Makeup_BDD.md) - Business requirements

### External Resources
- [Detox Documentation](https://wix.github.io/Detox/)
- [Jest Documentation](https://jestjs.io/docs/getting-started)
- [React Native Testing](https://reactnative.dev/docs/testing-overview)

### Team Contacts
- QA Team: Test-specific issues
- Development Team: App-related problems
- DevOps Team: CI/CD pipeline issues

---

## 🎯 Quick Checklist

### Before Running Tests
- [ ] Node.js >= 18 installed
- [ ] Yarn installed and updated
- [ ] Detox CLI installed globally
- [ ] iOS Simulator / Android Emulator available
- [ ] Project dependencies installed (`yarn install`)
- [ ] No port conflicts (8081, 9003)

### During Test Execution
- [ ] Monitor system resources
- [ ] Check for error messages
- [ ] Verify app launches correctly
- [ ] Ensure stable network connection

### After Test Completion
- [ ] Review test results
- [ ] Check artifacts for failures
- [ ] Clean up resources if needed
- [ ] Update documentation if issues found

### Emergency Commands
```bash
# Complete reset
yarn e2e:clean
rm -rf node_modules
yarn install
xcrun simctl erase all  # iOS
adb kill-server && adb start-server  # Android

# Quick restart
kill -9 $(lsof -ti:8081)
yarn start &
yarn e2e:test
```

## 🔍 Makeup Service Specific Notes

### Sequential Flow Requirements
The makeup service has a strict sequential flow that cannot be bypassed:
1. Must select service type before customer count
2. Must complete style selection before address
3. Must set address before date/time
4. Must complete all steps before payment

### Customer Count Handling
- Single customer: Direct flow to confirmation
- Double customer: Additional options for detail application and execution order

### Style Selection Complexity
- Package-based styles: Graduation, Party, Bridesmaid (price ranges)
- Fixed-price styles: Natural, Neutral, Dramatic (fixed prices)
- Add-on options: Hairstyling can be added to any style

### Address Requirements
- Supports both existing and new address creation
- Requires contact information (name, phone)
- Validates address format and completeness

### Date/Time Features
- 7-day booking window
- 5-minute interval time selection
- "Earliest available" quick option
- Notes with 400 character limit
- Execution order for double bookings

### Payment Options
- Multiple payment methods supported
- Promotion code functionality
- Terms and conditions acceptance required
- Contact information editing available
