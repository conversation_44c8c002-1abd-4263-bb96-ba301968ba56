#!/bin/bash

# Water Heater Service E2E Test Execution Script
# This script provides comprehensive test execution options for the water heater service

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
E2E_DIR="$PROJECT_ROOT/e2e"
REPORTS_DIR="$E2E_DIR/reports"
SCREENSHOTS_DIR="$E2E_DIR/screenshots"

# Create directories if they don't exist
mkdir -p "$REPORTS_DIR"
mkdir -p "$SCREENSHOTS_DIR"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check if Node.js is installed
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js first."
        exit 1
    fi
    
    # Check if npm is installed
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed. Please install npm first."
        exit 1
    fi
    
    # Check if Detox CLI is installed
    if ! command -v detox &> /dev/null; then
        print_warning "Detox CLI not found globally. Installing locally..."
        npm install -g detox-cli
    fi
    
    print_success "Prerequisites check completed"
}

# Function to setup test environment
setup_environment() {
    print_status "Setting up test environment..."
    
    cd "$PROJECT_ROOT"
    
    # Install dependencies if node_modules doesn't exist
    if [ ! -d "node_modules" ]; then
        print_status "Installing dependencies..."
        npm install
    fi
    
    # Build the app for testing
    print_status "Building app for testing..."
    npm run build:e2e || {
        print_warning "Build command not found, skipping build step"
    }
    
    print_success "Environment setup completed"
}

# Function to run specific test categories
run_test_category() {
    local category="$1"
    local test_file="$E2E_DIR/WaterHeaterServiceSequentialFlow.e2e.js"
    
    print_status "Running $category tests..."
    
    case "$category" in
        "address")
            npx jest "$test_file" --testNamePattern="Step 1: Address Selection" --verbose
            ;;
        "service")
            npx jest "$test_file" --testNamePattern="Step 2: Water Heater Service Configuration" --verbose
            ;;
        "datetime")
            npx jest "$test_file" --testNamePattern="Step 3: Date and Time Selection" --verbose
            ;;
        "payment")
            npx jest "$test_file" --testNamePattern="Step 4: Payment and Confirmation" --verbose
            ;;
        "errors")
            npx jest "$test_file" --testNamePattern="Error Handling and Edge Cases" --verbose
            ;;
        "navigation")
            npx jest "$test_file" --testNamePattern="Navigation and Flow Validation" --verbose
            ;;
        "performance")
            npx jest "$test_file" --testNamePattern="Performance and Reliability" --verbose
            ;;
        *)
            print_error "Unknown test category: $category"
            print_status "Available categories: address, service, datetime, payment, errors, navigation, performance"
            exit 1
            ;;
    esac
}

# Function to run all tests
run_all_tests() {
    print_status "Running all Water Heater Service E2E tests..."
    
    local start_time=$(date +%s)
    
    cd "$PROJECT_ROOT"
    npx jest "$E2E_DIR/WaterHeaterServiceSequentialFlow.e2e.js" --verbose --detectOpenHandles
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    print_success "All tests completed in ${duration} seconds"
}

# Function to run tests with performance monitoring
run_performance_tests() {
    print_status "Running performance-focused test execution..."
    
    cd "$PROJECT_ROOT"
    
    # Set performance environment variables
    export DETOX_PERFORMANCE_MONITORING=true
    export DETOX_SCREENSHOT_ON_FAILURE=true
    
    npx jest "$E2E_DIR/WaterHeaterServiceSequentialFlow.e2e.js" \
        --testNamePattern="Performance and Reliability" \
        --verbose \
        --detectOpenHandles \
        --maxWorkers=1
    
    unset DETOX_PERFORMANCE_MONITORING
    unset DETOX_SCREENSHOT_ON_FAILURE
}

# Function to generate test report
generate_report() {
    print_status "Generating test report..."
    
    local report_file="$REPORTS_DIR/water-heater-test-report-$(date +%Y%m%d-%H%M%S).html"
    
    # Create a simple HTML report
    cat > "$report_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>Water Heater Service E2E Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Water Heater Service E2E Test Report</h1>
        <p>Generated on: $(date)</p>
    </div>
    
    <div class="section">
        <h2>Test Summary</h2>
        <p>Test execution completed. Check console output for detailed results.</p>
    </div>
    
    <div class="section">
        <h2>Test Categories</h2>
        <ul>
            <li>Step 1: Address Selection</li>
            <li>Step 2: Water Heater Service Configuration</li>
            <li>Step 3: Date and Time Selection</li>
            <li>Step 4: Payment and Confirmation</li>
            <li>Error Handling and Edge Cases</li>
            <li>Navigation and Flow Validation</li>
            <li>Performance and Reliability</li>
        </ul>
    </div>
</body>
</html>
EOF
    
    print_success "Test report generated: $report_file"
}

# Function to clean up test artifacts
cleanup() {
    print_status "Cleaning up test artifacts..."
    
    # Remove old screenshots (keep last 10)
    if [ -d "$SCREENSHOTS_DIR" ]; then
        find "$SCREENSHOTS_DIR" -name "*.png" -type f | sort -r | tail -n +11 | xargs rm -f
    fi
    
    # Remove old reports (keep last 5)
    if [ -d "$REPORTS_DIR" ]; then
        find "$REPORTS_DIR" -name "*.html" -type f | sort -r | tail -n +6 | xargs rm -f
    fi
    
    print_success "Cleanup completed"
}

# Function to show usage
show_usage() {
    echo "Water Heater Service E2E Test Runner"
    echo ""
    echo "Usage: $0 [OPTION]"
    echo ""
    echo "Options:"
    echo "  all                 Run all test categories"
    echo "  address             Run address selection tests"
    echo "  service             Run service configuration tests"
    echo "  datetime            Run date/time selection tests"
    echo "  payment             Run payment/confirmation tests"
    echo "  errors              Run error handling tests"
    echo "  navigation          Run navigation validation tests"
    echo "  performance         Run performance tests"
    echo "  report              Generate test report"
    echo "  cleanup             Clean up test artifacts"
    echo "  help                Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 all              # Run all tests"
    echo "  $0 service          # Run only service configuration tests"
    echo "  $0 performance      # Run only performance tests"
}

# Main execution logic
main() {
    local command="${1:-help}"
    
    case "$command" in
        "all")
            check_prerequisites
            setup_environment
            run_all_tests
            generate_report
            cleanup
            ;;
        "address"|"service"|"datetime"|"payment"|"errors"|"navigation")
            check_prerequisites
            setup_environment
            run_test_category "$command"
            ;;
        "performance")
            check_prerequisites
            setup_environment
            run_performance_tests
            generate_report
            ;;
        "report")
            generate_report
            ;;
        "cleanup")
            cleanup
            ;;
        "help"|*)
            show_usage
            ;;
    esac
}

# Execute main function with all arguments
main "$@"
