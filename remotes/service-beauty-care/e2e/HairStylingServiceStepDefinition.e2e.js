/**
 * Hair Styling Service E2E Test Suite - Step Definition Pattern
 *
 * Comprehensive end-to-end testing for the hair styling service booking flow
 * using step-definition functions for better maintainability and consistency
 *
 * Test Coverage:
 * - Service selection (Hair Relaxing)
 * - Customer count selection (1 or 2 customers)
 * - Hair styling service selection (Relaxing hair wash, Root Touch-Up)
 * - Hair color selection for Root Touch-Up
 * - Heat styling add-on selection
 * - Address selection and management
 * - Date and time selection
 * - Payment method selection
 * - Terms and conditions acceptance
 * - Booking confirmation
 *
 * Based on BDD: remotes/service-beauty-care/docs/bBeauty_HairStyling_BDD.md
 *
 * @requires Detox E2E testing framework
 * @requires TestID-only selectors (no text-based selectors)
 * @requires Sequential flow compliance (no step skipping)
 */
const { device } = require('detox');

const {
  reloadApp,
  tapId,
  tapIdAtIndex,
  tapText,
  waitForElement,
  expectElementVisible,
  expectElementNotVisible,
  checkElementVisible,
  sleep,
  tapIdService,
  postTaskHairStyling,
  waitForLoading,
  swipe,
} = require('./step-definition');

// Optimized wait times for better performance
const WAIT_TIMES = {
  FAST: 2000, // For quick transitions
  NORMAL: 5000, // For standard navigation
  SLOW: 8000, // For complex operations
};

describe('Hair Styling Service Sequential Booking Flow - Step Definition', () => {
  const STEP_TIMEOUT = 15000; // 15 seconds per step

  beforeEach(async () => {
    // Reset app state and data for consistent testing
    await reloadApp();
    await device.reloadReactNative();

    try {
      // Use optimized wait time for faster execution
      await waitForElement('btnSubmitIntroService', WAIT_TIMES.SLOW);
      // Handle intro service screen if present
      await tapId('btnSubmitIntroService');
    } catch (error) {}

    // Verify hair relaxing service card is visible and tap it
    await waitForElement('item-0', 1000);
    await tapId('item-0');
    await waitForElement('btn-one-customer', 1000);
  });

  /**
   * Test Group 1: Service Selection and Customer Count
   * Covers BDD sections 6.1 - Choose Service Type
   */
  describe('1. Service Selection and Customer Count', () => {
    it('should display hair relaxing service card and allow selection', async () => {
      // Verify customer count modal appears
      await tapId('btn-one-customer');
    });

    it('should allow selection of 1 customer and navigate to hair styling service selection', async () => {
      await tapId('btn-one-customer');
      await waitForElement('relaxingShampoo', 3000);
      await tapId('relaxingShampoo');
    });

    it('should allow selection of 2 customers and navigate to hair styling service selection', async () => {
      // Select 2 customers
      await expectElementVisible('btn-two-customers');
      await tapId('btn-two-customers');

      // Verify navigation to hair styling service selection
      await waitForElement('relaxingShampoo', STEP_TIMEOUT);
      await expectElementVisible('relaxingShampoo');
    });
  });

  /**
   * Test Group 2: Hair Styling Service Selection
   * Covers BDD sections 6.2 - Choose Task Details (Hair Relaxing)
   */
  describe('2. Hair Styling Service Selection', () => {
    beforeEach(async () => {
      // Navigate to hair styling service selection
      await tapId('btn-one-customer');
      await waitForElement('rootTouchup', STEP_TIMEOUT);
    });

    it('should display hair styling service carousel and allow service selection', async () => {
      // Verify relaxing hair wash service item
      await tapId('rootTouchup');
      await tapId('rootTouchup');
      await tapId('btnNextStep2');
      await tapId('btnNextStep3');
      await swipe('scrollViewStep4', 'up');
      await tapId('btnSubmitPostTask');
    });

    it('should allow selection of relaxing hair wash service', async () => {
      console.log('🧪 Testing relaxing hair wash service selection');

      // Select relaxing hair wash service
      await tapId('hair-service-item-relaxing-wash');

      // Verify continue button is enabled
      await expectElementVisible('btn-continue-hair-styling-selection');

      console.log('✅ Relaxing hair wash service selection successful');
    });

    it('should allow selection of root touch-up service and display color selection', async () => {
      console.log('🧪 Testing root touch-up service with color selection');

      // Select root touch-up service
      await tapId('hair-service-item-root-touchup');

      // Verify hair color selection appears
      await expectElementVisible('hair-color-selection-container');
      await expectElementVisible('btn-choose-hair-color');

      console.log(
        '✅ Root touch-up service and color selection display successful',
      );
    });

    it('should allow hair color selection for root touch-up service', async () => {
      console.log('🧪 Testing hair color selection process');

      // Select root touch-up service
      await tapId('hair-service-item-root-touchup');

      // Open color selection modal
      await tapId('btn-choose-hair-color');

      // Note: Color selection modal testIDs would be added based on actual implementation
      // This test validates the color selection trigger

      console.log('✅ Hair color selection process initiated successfully');
    });

    it('should display heat styling add-on option for relaxing hair wash', async () => {
      console.log('🧪 Testing heat styling add-on option');

      // Select relaxing hair wash service
      await tapId('hair-service-item-relaxing-wash');

      // Verify heat styling add-on is available
      await expectElementVisible('hair-service-item-heat-styling');

      console.log('✅ Heat styling add-on option display successful');
    });

    it('should display working process and tools information', async () => {
      console.log('🧪 Testing working process and tools information access');

      // Select a service first
      await tapId('hair-service-item-relaxing-wash');

      // Verify options container is visible
      await expectElementVisible('hair-styling-options-container');

      console.log('✅ Working process and tools information access successful');
    });
  });

  /**
   * Test Group 3: Two Customer Flow with Apply Details
   * Covers BDD sections 6.2 - Multi-customer Details Application
   */
  describe('3. Two Customer Flow with Apply Details', () => {
    beforeEach(async () => {
      // Navigate to hair styling service selection with 2 customers
      await tapId('hair-relaxing-card');
      await tapId('btn-two-customers');
      await waitForElement('hair-styling-selection-container', STEP_TIMEOUT);
    });

    it('should display apply details to customer 2 checkbox for two customers', async () => {
      console.log('🧪 Testing apply details to customer 2 checkbox');

      // Select a service for customer 1
      await tapId('hair-service-item-relaxing-wash');

      // Verify apply details checkbox is visible
      await expectElementVisible('apply-to-customer-2-checkbox');

      console.log('✅ Apply details to customer 2 checkbox display successful');
    });

    it('should allow applying customer 1 details to customer 2', async () => {
      console.log('🧪 Testing apply customer 1 details to customer 2');

      // Select a service for customer 1
      await tapId('hair-service-item-relaxing-wash');

      // Check apply details to customer 2
      await tapId('apply-to-customer-2-checkbox');

      // Continue to next step
      await tapId('btn-continue-hair-styling-selection');

      // Verify navigation to address selection (skipping customer 2 selection)
      await waitForElement('address-selection-container', STEP_TIMEOUT);

      console.log('✅ Apply customer 1 details to customer 2 successful');
    });
  });

  /**
   * Test Group 4: Complete Hair Styling Booking Flow
   * Covers the full end-to-end booking process using step-definition functions
   */
  describe('4. Complete Hair Styling Booking Flow', () => {
    it('should complete full hair styling booking flow using step-definition', async () => {
      console.log(
        '🧪 Testing complete hair styling booking flow with step-definition',
      );
      console.time('Full Hair Styling Booking Flow');

      // Use the existing postTaskHairStyling function from step-definition
      await postTaskHairStyling();

      console.timeEnd('Full Hair Styling Booking Flow');
      console.log('✅ Complete hair styling booking flow successful');
    });

    it('should handle hair styling booking with existing step-definition patterns', async () => {
      console.log(
        '🧪 Testing hair styling booking with step-definition patterns',
      );

      // Navigate to beauty care service
      await tapIdService('postTaskServiceBEAUTY_CARE');

      // Select hair styling service
      await waitForElement('Làm tóc', 1000, 'text');
      await tapText('Làm tóc');

      // Select 1 customer
      await tapId('1-people');

      // Select relaxing shampoo service
      await tapId('relaxingShampoo');

      // Continue to next step
      await waitForElement('Tiếp tục', 500, 'text');
      await tapText('Tiếp tục');

      console.log(
        '✅ Hair styling booking with step-definition patterns successful',
      );
    });
  });

  /**
   * Performance and Stability Tests
   */
  describe('5. Performance and Stability', () => {
    it('should complete full booking flow within performance target', async () => {
      console.log('🧪 Testing full booking flow performance');
      console.time('Full Hair Styling Booking Flow Performance');

      // Execute complete booking flow using step-definition
      await postTaskHairStyling();

      console.timeEnd('Full Hair Styling Booking Flow Performance');
      console.log('✅ Full booking flow performance test completed');
    });

    it('should handle navigation stability across hair styling screens', async () => {
      console.log('🧪 Testing navigation stability');

      // Test navigation through each screen
      await tapId('hair-relaxing-card');
      await tapId('btn-one-customer');
      await waitForElement('hair-styling-selection-container', STEP_TIMEOUT);

      await tapId('hair-service-item-relaxing-wash');
      await tapId('btn-continue-hair-styling-selection');
      await waitForElement('address-selection-container', STEP_TIMEOUT);

      console.log('✅ Navigation stability test completed');
    });
  });
});
