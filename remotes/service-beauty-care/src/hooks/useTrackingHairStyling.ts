import {
  DateTimeHelpers,
  IEventTaskAbandoned,
  IEventTaskPostSuccess,
  SERVICES,
  TrackingActions,
  TrackingScreenNames,
  TrackingServices,
  TypeFormatDate,
} from '@btaskee/design-system';

import { usePostTaskStore } from '@stores';

/**
 * Beauty Care Service Tracking Hook
 *
 * Provides comprehensive tracking functionality for beauty-care service following
 * the multi-provider tracking architecture with provider-agnostic interface.
 *
 * Features:
 * - Screen view tracking
 * - User action tracking (back, next, changes)
 * - Task abandonment tracking
 * - Task success tracking
 * - Beauty care specific data tracking (hair styling packages, services, customer type)
 * - App state change tracking
 * - Navigation tracking patterns
 */
export const useTrackingHairStyling = () => {
  const { setStepPostTask, setIsBookedTask } = usePostTaskStore();

  /**
   * Track back action from hair styling service selection screen
   * Includes comprehensive beauty care service data
   */
  const trackingBackChooseHairStylingService = () => {
    const currentState = usePostTaskStore.getState();
    const { service, detailHairStyling, rootTouchup } = currentState;

    TrackingServices.trackingServiceClick({
      screenName: TrackingScreenNames.DetailInformation,
      serviceName: SERVICES.HAIR_STYLING,
      action: TrackingActions.Back,
      isTetBooking: service?.isTet,
      additionalInfo: {
        packages: detailHairStyling?.packages || [],
        rootTouchup: rootTouchup?.name || null,
      },
    });
  };

  /**
   * Track next step action from hair styling service selection
   * Includes comprehensive beauty care service data
   */
  const trackingChooseHairStylingServiceNextStep = () => {
    const currentState = usePostTaskStore.getState();
    const { service, detailHairStyling, rootTouchup } = currentState;

    TrackingServices.trackingServiceClick({
      screenName: TrackingScreenNames.DetailInformation,
      serviceName: SERVICES.HAIR_STYLING,
      action: TrackingActions.Next,
      isTetBooking: service?.isTet,
      additionalInfo: {
        packages: detailHairStyling?.packages || [],
        rootTouchup: rootTouchup?.name || null,
      },
    });
  };

  /**
   * Track screen view for hair styling service selection
   */
  const trackingChooseHairStylingServiceScreenView = () => {
    TrackingServices.trackingScreenView({
      screenName: TrackingScreenNames.DetailInformation,
      serviceName: SERVICES.HAIR_STYLING,
    });
  };

  /**
   * Track next step action from date time selection
   * Includes comprehensive beauty care service data
   */
  const trackingActionChooseDateTime = (action: TrackingActions) => {
    const currentState = usePostTaskStore.getState();
    const { service, date, address, detailHairStyling } = currentState;

    const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

    TrackingServices.trackingServiceClick({
      screenName: TrackingScreenNames.ChooseWorkingTime,
      serviceName: SERVICES.HAIR_STYLING,
      action,
      isTetBooking: service?.isTet,
      additionalInfo: {
        packages: detailHairStyling?.packages || [],
        workingTime: {
          date: date
            ? DateTimeHelpers.formatToString({
                timezone,
                date,
                typeFormat: TypeFormatDate.DateShort,
              })
            : null,
          time: date
            ? DateTimeHelpers.formatToString({
                timezone,
                date,
                typeFormat: TypeFormatDate.TimeHourMinute,
              })
            : null,
        },
      },
    });
  };

  /**
   * Track screen view for date time selection
   */
  const trackingChooseDateTimeScreenView = () => {
    TrackingServices.trackingScreenView({
      screenName: TrackingScreenNames.ChooseWorkingTime,
      serviceName: SERVICES.HAIR_STYLING,
    });
  };

  /**
   * Track back action from confirm and payment screen
   * Includes comprehensive beauty care service data
   */
  const trackingActionConfirmPayment = (action: TrackingActions) => {
    const currentState = usePostTaskStore.getState();
    const {
      service,
      date,
      address,
      detailHairStyling,
      paymentMethod,
      promotion,
      price,
    } = currentState;

    const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

    TrackingServices.trackingServiceClick({
      screenName: TrackingScreenNames.ConfirmPayment,
      serviceName: SERVICES.HAIR_STYLING,
      action,
      isTetBooking: service?.isTet,
      additionalInfo: {
        packages: detailHairStyling?.packages || [],
        workingTime: {
          date: date
            ? DateTimeHelpers.formatToString({
                timezone,
                date,
                typeFormat: TypeFormatDate.DateShort,
              })
            : null,
          time: date
            ? DateTimeHelpers.formatToString({
                timezone,
                date,
                typeFormat: TypeFormatDate.TimeHourMinute,
              })
            : null,
        },
        paymentMethod: paymentMethod?.name || null,
        promotionCode: promotion?.code || null,
        taskValue: price?.finalCost || null,
      },
    });
  };

  /**
   * Track screen view for confirm and payment
   */
  const trackingConfirmPaymentScreenView = () => {
    TrackingServices.trackingScreenView({
      screenName: TrackingScreenNames.ConfirmPayment,
      serviceName: SERVICES.HAIR_STYLING,
    });
  };

  /**
   * Track task abandonment with comprehensive beauty care data
   * Called when user exits the flow without completing
   */
  const trackingPostTaskAbandoned = async (action: TrackingActions) => {
    const currentState = usePostTaskStore.getState();
    const { service, date, address, promotion, stepPostTask } = currentState;

    const params: IEventTaskAbandoned = {
      serviceId: service?._id,
      serviceName: SERVICES.HAIR_STYLING,
      step: stepPostTask,
      action,
      address: address?.address,
      district: address?.district,
      city: address?.city,
      country: address?.country,
      date: date || undefined,
      promotionCode: promotion?.code,
    };

    TrackingServices.trackingTaskAbandoned(params);
  };

  /**
   * Track successful task posting
   * Called after task is successfully created
   */
  const trackingPostTaskSuccess = () => {
    const currentState = usePostTaskStore.getState();
    const { service, date, address, detailHairStyling, promotion, price } =
      currentState;

    const params: IEventTaskPostSuccess = {
      serviceId: service?._id,
      serviceName: SERVICES.HAIR_STYLING,
      address: address?.address,
      district: address?.district,
      city: address?.city,
      country: address?.country,
      date: date || undefined,
      promotionCode: promotion?.code,
      taskValue: price?.finalCost,
      additionalInfo: {
        packages: detailHairStyling?.packages || [],
      },
    };

    TrackingServices.trackingTaskPostSuccess(params);
  };

  return {
    // Screen view tracking
    trackingChooseHairStylingServiceScreenView,
    trackingChooseDateTimeScreenView,
    trackingConfirmPaymentScreenView,

    // User action tracking
    trackingBackChooseHairStylingService,
    trackingChooseHairStylingServiceNextStep,
    trackingActionChooseDateTime,
    trackingActionConfirmPayment,

    // Task lifecycle tracking
    trackingPostTaskAbandoned,
    trackingPostTaskSuccess,

    // Store actions
    setStepPostTask,
    setIsBookedTask,
  };
};
