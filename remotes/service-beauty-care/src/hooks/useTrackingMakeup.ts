import {
  DateTimeHelpers,
  IEventTaskAbandoned,
  IEventTaskPostSuccess,
  SERVICES,
  TrackingActions,
  TrackingScreenNames,
  TrackingServices,
  TypeFormatDate,
} from '@btaskee/design-system';

import { usePostTaskStore } from '@stores';

/**
 * Beauty Care Makeup Service Tracking Hook
 *
 * Provides comprehensive tracking functionality for beauty-care makeup service following
 * the multi-provider tracking architecture with provider-agnostic interface.
 *
 * Features:
 * - Screen view tracking
 * - User action tracking (back, next, changes)
 * - Task abandonment tracking
 * - Task success tracking
 * - Beauty care makeup specific data tracking (packages, services, customer type)
 * - App state change tracking
 * - Navigation tracking patterns
 */
export const useTrackingMakeup = () => {
  const { setStepPostTask, setIsBookedTask } = usePostTaskStore();

  /**
   * Track back action from makeup service selection screen
   * Includes comprehensive beauty care makeup service data
   */
  const trackingBackChooseMakeupService = () => {
    const currentState = usePostTaskStore.getState();
    const { detailMakeup } = currentState;

    TrackingServices.trackingServiceClick({
      screenName: TrackingScreenNames.DetailInformation,
      serviceName: SERVICES.MAKEUP,
      action: TrackingActions.Back,
      additionalInfo: {
        packages: detailMakeup?.packages || [],
      },
    });
  };

  /**
   * Track next step action from makeup service selection
   * Includes comprehensive beauty care makeup service data
   */
  const trackingChooseMakeupServiceNextStep = () => {
    const currentState = usePostTaskStore.getState();
    const { detailMakeup } = currentState;

    TrackingServices.trackingServiceClick({
      screenName: TrackingScreenNames.DetailInformation,
      serviceName: SERVICES.MAKEUP,
      action: TrackingActions.Next,
      additionalInfo: {
        packages: detailMakeup?.packages || [],
      },
    });
  };

  /**
   * Track screen view for makeup service selection
   */
  const trackingChooseMakeupServiceScreenView = () => {
    TrackingServices.trackingScreenView({
      screenName: TrackingScreenNames.DetailInformation,
      serviceName: SERVICES.MAKEUP,
    });
  };

  /**
   * Track back action from makeup detail catalogue screen
   * Includes comprehensive beauty care makeup service data
   */
  const trackingBackChooseDetailCatalogue = () => {
    const currentState = usePostTaskStore.getState();
    const { detailMakeup } = currentState;

    TrackingServices.trackingServiceClick({
      screenName: TrackingScreenNames.DetailInformation,
      serviceName: SERVICES.MAKEUP,
      action: TrackingActions.Back,
      additionalInfo: {
        packages: detailMakeup?.packages || [],
      },
    });
  };

  /**
   * Track next step action from makeup detail catalogue
   * Includes comprehensive beauty care makeup service data
   */
  const trackingChooseDetailCatalogueNextStep = () => {
    const currentState = usePostTaskStore.getState();
    const { detailMakeup } = currentState;

    TrackingServices.trackingServiceClick({
      screenName: TrackingScreenNames.DetailInformation,
      serviceName: SERVICES.MAKEUP,
      action: TrackingActions.Next,
      additionalInfo: {
        packages: detailMakeup?.packages || [],
      },
    });
  };

  /**
   * Track screen view for makeup detail catalogue
   */
  const trackingChooseDetailCatalogueScreenView = () => {
    TrackingServices.trackingScreenView({
      screenName: TrackingScreenNames.DetailInformation,
      serviceName: SERVICES.MAKEUP,
    });
  };

  /**
   * Track next step action from date time selection
   * Includes comprehensive beauty care makeup service data
   */
  const trackingActionChooseDateTime = (action: TrackingActions) => {
    const currentState = usePostTaskStore.getState();
    const { date, address, detailMakeup } = currentState;

    const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

    TrackingServices.trackingServiceClick({
      screenName: TrackingScreenNames.ChooseWorkingTime,
      serviceName: SERVICES.MAKEUP,
      action,
      additionalInfo: {
        packages: detailMakeup?.packages || [],
        workingTime: {
          date: date
            ? DateTimeHelpers.formatToString({
                timezone,
                date,
                typeFormat: TypeFormatDate.DateShort,
              })
            : null,
          time: date
            ? DateTimeHelpers.formatToString({
                timezone,
                date,
                typeFormat: TypeFormatDate.TimeHourMinute,
              })
            : null,
        },
      },
    });
  };

  /**
   * Track screen view for date time selection
   */
  const trackingChooseDateTimeScreenView = () => {
    TrackingServices.trackingScreenView({
      screenName: TrackingScreenNames.ChooseWorkingTime,
      serviceName: SERVICES.MAKEUP,
    });
  };

  /**
   * Track back action from confirm and payment screen
   * Includes comprehensive beauty care makeup service data
   */
  const trackingActionConfirmPayment = (action: TrackingActions) => {
    const currentState = usePostTaskStore.getState();
    const { date, address, detailMakeup, paymentMethod, promotion, price } =
      currentState;

    const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

    TrackingServices.trackingServiceClick({
      screenName: TrackingScreenNames.ConfirmPayment,
      serviceName: SERVICES.MAKEUP,
      action,
      additionalInfo: {
        packages: detailMakeup?.packages || [],
        workingTime: {
          date: date
            ? DateTimeHelpers.formatToString({
                timezone,
                date,
                typeFormat: TypeFormatDate.DateShort,
              })
            : null,
          time: date
            ? DateTimeHelpers.formatToString({
                timezone,
                date,
                typeFormat: TypeFormatDate.TimeHourMinute,
              })
            : null,
        },
        paymentMethod: paymentMethod?.name || null,
        promotionCode: promotion?.code || null,
        taskValue: price?.finalCost || null,
      },
    });
  };

  /**
   * Track screen view for confirm and payment
   */
  const trackingConfirmPaymentScreenView = () => {
    TrackingServices.trackingScreenView({
      screenName: TrackingScreenNames.ConfirmPayment,
      serviceName: SERVICES.MAKEUP,
    });
  };

  /**
   * Track task abandonment with comprehensive beauty care makeup data
   * Called when user exits the flow without completing
   */
  const trackingPostTaskAbandoned = async (action: TrackingActions) => {
    const currentState = usePostTaskStore.getState();
    const { service, date, address, promotion, stepPostTask } = currentState;

    const params: IEventTaskAbandoned = {
      serviceId: service?._id,
      serviceName: SERVICES.MAKEUP,
      step: stepPostTask,
      action,
      address: address?.address,
      district: address?.district,
      city: address?.city,
      country: address?.country,
      date: date || undefined,
      promotionCode: promotion?.code,
    };

    TrackingServices.trackingTaskAbandoned(params);
  };

  /**
   * Track successful task posting
   * Called after task is successfully created
   */
  const trackingPostTaskSuccess = () => {
    const currentState = usePostTaskStore.getState();
    const { service, date, address, detailMakeup, promotion, price } =
      currentState;

    const params: IEventTaskPostSuccess = {
      serviceId: service?._id,
      serviceName: SERVICES.MAKEUP,
      address: address?.address,
      district: address?.district,
      city: address?.city,
      country: address?.country,
      date: date || undefined,
      promotionCode: promotion?.code,
      taskValue: price?.finalCost,
      additionalInfo: {
        packages: detailMakeup?.packages || [],
      },
    };

    TrackingServices.trackingTaskPostSuccess(params);
  };

  return {
    // Screen view tracking
    trackingChooseMakeupServiceScreenView,
    trackingChooseDetailCatalogueScreenView,
    trackingChooseDateTimeScreenView,
    trackingConfirmPaymentScreenView,

    // User action tracking
    trackingBackChooseMakeupService,
    trackingChooseMakeupServiceNextStep,
    trackingBackChooseDetailCatalogue,
    trackingChooseDetailCatalogueNextStep,
    trackingActionChooseDateTime,
    trackingActionConfirmPayment,

    // Task lifecycle tracking
    trackingPostTaskAbandoned,
    trackingPostTaskSuccess,

    // Store actions
    setStepPostTask,
    setIsBookedTask,
  };
};
