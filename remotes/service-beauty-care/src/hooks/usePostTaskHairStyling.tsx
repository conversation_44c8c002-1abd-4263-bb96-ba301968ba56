import { useCallback, useMemo } from 'react';
import {
  <PERSON><PERSON>,
  DateT<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Endpoint<PERSON>ey<PERSON>,
  getPhoneNumber,
  handleError,
  ISO_CODE,
  PaymentService,
  PostTaskHelpers,
  useApiMutation,
  useAppLoading,
  usePostTaskAction,
  useUserStore,
} from '@btaskee/design-system';

import { useI18n, useTrackingHairStyling } from '@hooks';
import { usePostTaskStore } from '@stores';

/**
 * Hook for managing beauty care Hair task posting functionality
 * Provides methods for getting current package info and posting Hair tasks
 * @returns {Object} Object containing getCurrentPackage and postTask functions
 */
export const usePostTaskHairStyling = () => {
  const { t } = useI18n();
  const { handlePostTaskError } = usePostTaskAction();
  const { showAppLoading, hideAppLoading } = useAppLoading();
  const { user } = useUserStore();
  const { trackingPostTaskSuccess, setIsBookedTask } = useTrackingHairStyling();

  // Get all state from Zustand store
  const {
    service,
    address,
    date,
    isApplyNoteForAllTask,
    note,
    detailHairStyling,
    promotion,
    paymentMethod,
    isAutoChooseTasker,
    homeNumber,
    homeType,
    isFavouriteTasker,
  } = usePostTaskStore();

  // API mutations for beauty care operations
  const { mutate: checkTaskSameTimeAPI } = useApiMutation({
    key: EndpointKeys.checkTaskSameTime,
    options: {
      onMutate: () => {
        showAppLoading();
      },
      onSettled: () => {
        hideAppLoading();
      },
    },
  });

  // For now, we'll use a placeholder that needs to be implemented
  const { mutate: postTaskHairStylingAPI } = useApiMutation({
    key: EndpointKeys.postTaskHairStyling,
    options: {
      onMutate: () => {
        showAppLoading();
      },
      onSettled: () => {
        hideAppLoading();
      },
    },
  });

  const packages = useMemo(() => {
    return detailHairStyling?.packages || [];
  }, [detailHairStyling]);

  const listPackagesToSuccess = useMemo(() => {
    return service?.services?.filter(
      (e) => !e.isComingSoon && e?._id !== service._id,
    );
  }, [service?._id, service?.services]);

  /**
   * Gets the current package information for a specific customer
   * @param currentCustomer - The customer number (1-based index)
   * @returns {Object} Package information for the specified customer
   */
  const getCurrentPackage = useCallback(
    (currentCustomer?: number) => {
      if (!currentCustomer) return {};
      return packages[currentCustomer - 1] || {}; // Convert to 0-based index
    },
    [packages],
  );

  /**
   * Adds a new beauty care task using the API
   * @param dataTask - Task data to be submitted
   * @returns {Promise<any>} API response or error handling result
   */
  const addTask = async (dataTask: any) => {
    postTaskHairStylingAPI(dataTask.task, {
      onSuccess: async (_result: any) => {
        const bookingId = _result?.bookingId;
        if (bookingId) return;
        // Track successful task posting
        trackingPostTaskSuccess();

        // Mark task as booked for tracking
        setIsBookedTask(true);

        // Success handling
        // NavigationService.popToTop();
        // NavigationService.navigate(RouteName.BeautyCareService, {
        //   screen: BeautyCareRouteName.PostTaskSuccess,
        //   params: {
        //     listPackages: listPackagesToSuccess,
        //     serviceName: service?.name,
        //   },
        // });

        // TODO: re-handle post task success
        await PaymentService.onPostTaskSuccess({
          bookingId,
          isPrepayment: _result.isPrepayment,
        });
      },
      onError: (error: any) => {
        handlePostTaskError(error);
      },
    });
  };

  /**
   * Refactors task data according to beauty care service requirements
   * @param userData - Optional user data override
   * @returns {Object} Formatted task and service data for API submission
   */
  const refactorDataTaskByService = () => {
    const isAddressMaybeWrong = Boolean(address?.isAddressMaybeWrong);
    const city = address?.city;
    const timezone = DateTimeHelpers.getTimezoneByCity(city);
    const currentUser = user;

    // Base task information
    const task = {
      detailHairStyling,
      address: address?.address,
      contactName: address?.contact || user?.name,
      lat: address?.lat,
      lng: address?.lng,
      phone: address?.phoneNumber || user?.phone,
      countryCode:
        user?.countryCode || address?.countryCode || currentUser?.countryCode,
      description: homeNumber,
      askerId: user?._id,
      autoChooseTasker: isAutoChooseTasker,
      date: DateTimeHelpers.formatToString({ date: date, timezone }),
      timezone,
      deviceInfo: DeviceHelper.getDeviceInfo(),
      homeType: homeType,
      houseNumber: homeNumber,
      isSendToFavTaskers: Boolean(isFavouriteTasker),
      isoCode: ISO_CODE.VN, // TODO: Get from global context or settings
      serviceId: service?._id,
      taskPlace: {
        country: address?.country,
        city,
        district: address?.district,
      },
      updateTaskNoteToUser: isApplyNoteForAllTask,
      shortAddress: address?.shortAddress,
      ...PostTaskHelpers.formatDataToParams({ paymentMethod, promotion }),
    };

    // Refactor phone number - add country code prefix if needed
    task.phone = getPhoneNumber(task.phone, task.countryCode);

    // Handle potentially incorrect address
    if (isAddressMaybeWrong) {
      (task.taskPlace as any).isAddressMaybeWrong = isAddressMaybeWrong;
    }

    // Add task note if provided
    if (note && note?.trim()) {
      (task as any).taskNote = note?.trim();
    }

    return {
      task,
      service: {
        _id: service?._id,
        name: service?.name,
      },
    };
  };

  /**
   * Main function for booking beauty care Hair tasks
   * @param callback - Callback function to execute on successful booking
   * @param userData - Optional user data override
   * @returns {Promise<any>} Promise resolving to booking result
   */
  const postTaskHairStyling = async () => {
    showAppLoading();

    // Get formatted task data
    const dataTask = refactorDataTaskByService();

    // Check for conflicting task times
    checkTaskSameTimeAPI(
      {
        serviceId: dataTask.service?._id || '',
        taskDate: dataTask.task?.date || '',
      },
      {
        onSuccess: (timeCheckResult: any) => {
          // Time slot is available
          if (!timeCheckResult) {
            // Proceed with booking
            addTask(dataTask);
          } else {
            // Time conflict detected - show confirmation dialog
            hideAppLoading();
            Alert.alert.open({
              title: t('DIALOG_TITLE_INFORMATION'),
              message: t('TASK_SAME_TIME_MESSAGE'),
              actions: [
                { text: t('CLOSE'), style: 'cancel' },
                {
                  text: t('OK'),
                  onPress: () => {
                    addTask(dataTask);
                  },
                },
              ],
            });
          }
        },
        onError: (error: any) => {
          handleError(error);
        },
      },
    );
  };

  return {
    getCurrentPackage,
    postTaskHairStyling,
  };
};
