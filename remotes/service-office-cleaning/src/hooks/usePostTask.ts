import {
  <PERSON><PERSON>,
  DateT<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>per,
  EndpointKeys,
  getPhoneNumber,
  handleError,
  HomeType,
  IApiError,
  IDate,
  PaymentService,
  PostTaskHelpers,
  TrackingPostTaskStep,
  useApiMutation,
  useAppLoadingStore,
  useAppStore,
  usePostTaskAction,
  useUserStore,
} from '@btaskee/design-system';
import { debounce, isEmpty } from 'lodash-es';

import { usePostTaskStore } from '@stores';

import { useI18n } from './useI18n';
import { useTracking } from './useTracking';

export const usePostTask = () => {
  const { t } = useI18n();
  const { isoCode } = useAppStore();
  const {
    setPrice,
    setLoadingPrice,
    resetState,
    setIsBookedTask,
    setStepPostTask,
  } = usePostTaskStore();
  const { showLoading, hideLoading } = useAppLoadingStore();
  const { user } = useUserStore();
  const { trackingPostTaskSuccess } = useTracking();
  const { handlePostTaskError } = usePostTaskAction();

  const { mutate: getPriceOfficeCleaning } = useApiMutation({
    key: EndpointKeys.getPriceOfficeCleaning,
    options: {
      onMutate: () => {
        setLoadingPrice(true);
        showLoading();
      },
      onSettled: () => {
        setLoadingPrice(false);
        hideLoading();
      },
    },
  });

  const { mutate: postTaskOfficeCleaning } = useApiMutation({
    key: EndpointKeys.postTaskOfficeCleaning,
    options: {
      onSuccess: async (data) => {
        // success
        if (data?.bookingId) {
          trackingPostTaskSuccess();
          setIsBookedTask(true);
          resetState();

          // Payment processing (includes navigation)
          await PaymentService.onPostTaskSuccess({
            bookingId: data.bookingId,
            isPrepayment: data.isPrepayment,
          });
        }
      },
      onError: (error: IApiError) => {
        handlePostTaskError(error);
      },
      onMutate: () => {
        showLoading();
      },
      onSettled() {
        hideLoading();
      },
    },
  });

  const { mutate: checkTaskSameTime } = useApiMutation({
    key: EndpointKeys.checkTaskSameTime,
    options: {
      onMutate: () => {
        showLoading();
      },
      onSettled() {
        hideLoading();
      },
    },
  });

  const getDataPricing = () => {
    // Get the latest state directly from the store
    const currentState = usePostTaskStore.getState();
    const {
      address,
      date,
      isAutoChooseTasker,
      service,
      paymentMethod,
      detailOfficeCleaning,
      duration,
      promotion,
      requirements,
    } = currentState;

    if (!address || !date || !duration || isEmpty(detailOfficeCleaning)) {
      return null;
    }

    const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);
    // base info
    const task = {
      timezone,
      date: DateTimeHelpers.formatToString({ timezone, date }),
      autoChooseTasker: isAutoChooseTasker,
      taskPlace: {
        country: address?.country,
        city: address?.city,
        district: address?.district,
      },
      homeType: address?.homeType,
      duration: duration,
      // Add payment method and promotion using PostTaskHelpers
      ...PostTaskHelpers.formatDataToParams({ paymentMethod, promotion }),
    };
    if (!isEmpty(requirements)) {
      task.requirements = requirements;
    }

    if (detailOfficeCleaning) {
      task.detailOfficeCleaning = detailOfficeCleaning;
    }
    return { task, service: { _id: service?._id }, isoCode };
  };

  const getPrice = debounce(async () => {
    // refactor data after call get price
    const data = getDataPricing();

    // data is null, no get price
    if (!data) {
      // set price is nul --> hide price button.
      return setPrice(null);
    }

    // call get price API
    getPriceOfficeCleaning(data, {
      onSuccess: (result) => {
        setPrice(result);
      },
      onError: (error) => {
        handleError(error);
        setPrice(null);
      },
    });
  }, 150);

  const _refactorDataPostTask = () => {
    // Get the latest state directly from the store
    const currentState = usePostTaskStore.getState();
    const {
      address,
      date,
      duration,
      isAutoChooseTasker,
      service,
      paymentMethod,
      requirements,
      isPremium,
      isFavouriteTasker,
      isApplyNoteForAllTask,
      note,
      schedule,
      isEnabledSchedule,
      detailOfficeCleaning,
      vatInfo,
      promotion,
    } = currentState;

    const isAddressMaybeWrong = Boolean(address?.isAddressMaybeWrong);
    const isTet = service?.isTet || false;
    const city = address?.city;
    const timezone = DateTimeHelpers.getTimezoneByCity(city);

    // base task info
    const task = {
      address: address?.address,
      contactName: address?.contact || user?.name,
      lat: address?.lat,
      lng: address?.lng,
      phone: address?.phoneNumber || user?.phone,
      countryCode: address?.countryCode || user?.countryCode,
      description: address?.description,
      askerId: user?._id,
      autoChooseTasker: isAutoChooseTasker,
      date: DateTimeHelpers.formatToString({
        date: date as IDate,
        timezone,
      }),
      timezone,
      deviceInfo: DeviceHelper.getDeviceInfo(),
      duration: duration,
      homeType: address?.homeType || HomeType.Home,
      houseNumber: address?.description,
      isSendToFavTaskers: Boolean(isFavouriteTasker),
      isoCode,
      serviceId: service?._id,
      taskPlace: {
        country: address?.country,
        city,
        district: address?.district,
      },
      updateTaskNoteToUser: isApplyNoteForAllTask,
      shortAddress: address?.shortAddress,
      // Add payment method and promotion using PostTaskHelpers
      ...PostTaskHelpers.formatDataToParams({ paymentMethod, promotion }),
    };

    // Refactor phone number - refill 0 at first
    task.phone = getPhoneNumber(task.phone || '', task.countryCode || '');

    // Địa chỉ có thể bị sai => khi book task, send to slack cho team vận hành xử lý
    if (isAddressMaybeWrong) {
      task.taskPlace = {
        ...task.taskPlace,
        isAddressMaybeWrong: true,
      };
    }

    if (isTet) {
      task.isTetBooking = true;
    }

    if (note && note?.trim()) {
      task.taskNote = note?.trim();
    }

    // Payment and promotion are handled by PostTaskHelpers.formatDataToParams
    // addOnService
    if (!isEmpty(requirements)) {
      task.requirements = requirements.map((req) => ({ type: req.type })); // send type only
    }
    // add schedule
    if (isEnabledSchedule && !isEmpty(schedule)) {
      task.weekday = schedule;
    }

    // premium service
    if (isPremium) {
      task.isPremium = isPremium;
    }

    if (detailOfficeCleaning) {
      task.detailOfficeCleaning = detailOfficeCleaning;
    }

    if (vatInfo) {
      task.vatInfo = vatInfo;
    }

    return task;
  };

  const postTask = async () => {
    // Set tracking step for final booking step (matches service-cleaning pattern)
    setStepPostTask(TrackingPostTaskStep.STEP_4);

    const currentState = usePostTaskStore.getState();
    const { date, service, address } = currentState;
    const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);
    // check task same time
    checkTaskSameTime(
      {
        taskDate: DateTimeHelpers.formatToString({
          date: date as IDate,
          timezone,
        }),
        serviceId: service?._id || '',
      },
      {
        onSuccess: (data) => {
          _addTask({ isExistTask: !data });
        },
      },
    );
  };

  /**
   * @description function booking task
   * @param payload {{object}} data of task
   */
  const _addTask = debounce(
    async ({ isExistTask }: { isExistTask: boolean }) => {
      const dataTask = _refactorDataPostTask();

      // time ok
      if (isExistTask) {
        // call api book task
        postTaskOfficeCleaning(dataTask);
        return;
      }

      // same time, alert for user
      return Alert.alert.open({
        title: t('DIALOG_TITLE_INFORMATION'),
        message: t('TASK_SAME_TIME_MESSAGE'),
        actions: [
          { text: t('CLOSE'), style: 'cancel' },
          {
            text: t('OK'),
            onPress: async () => {
              // wait modal close
              setTimeout(async () => {
                postTaskOfficeCleaning(dataTask);
              }, 300);
            },
          },
        ],
      });
    },
    300,
  );

  return { getPrice, postTask };
};
