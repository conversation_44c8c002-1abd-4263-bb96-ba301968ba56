import {
  Alert,
  DateTimeHelpers,
  EndpointKeys,
  handleError,
  ITaskDetail,
  Maybe,
  PaymentService,
  PostTaskHelpers,
  useApiMutation,
  useAppLoading,
  usePostTaskAction,
  useSettingsStore,
} from '@btaskee/design-system';

import { useTaskUpdateStore } from '@stores';

import { useI18n } from './useI18n';

type IUseRepayTask = {
  dataTask: Maybe<ITaskDetail>;
};

export const useRepayTask = ({ dataTask }: IUseRepayTask) => {
  const { t } = useI18n();
  const { settings } = useSettingsStore();
  const { paymentMethod } = useTaskUpdateStore();
  const { handlePostTaskError } = usePostTaskAction();

  const { showAppLoading, hideAppLoading } = useAppLoading();

  const { mutateAsync: updatePaymentMethodAPI } = useApiMutation({
    key: EndpointKeys.updatePaymentMethod,
    options: {
      onMutate: () => {
        showAppLoading();
      },
      onSettled: () => {
        hideAppLoading();
      },
      onError: (error) => {
        handleError(error);
      },
    },
  });

  const service = settings?.services?.find(
    (ser) => ser?.name === dataTask?.serviceName,
  );
  const timezone = DateTimeHelpers.getTimezoneByCity(dataTask?.taskPlace?.city);

  const handleRepay = async () => {
    // const _id = dataTask._id;
    // const askerId = dataTask.askerId;
    // const payment = isBodyPayment(paymentMethod);
    // check time before 60min
    if (
      !PostTaskHelpers.validateDateTime(
        timezone,
        dataTask?.date,
        settings?.settingSystem?.minPostTaskTime,
      )
    ) {
      return Alert?.alert?.open({
        title: t('DIALOG_TITLE_INFORMATION'),
        message: t('POSTTASK_STEP2_ERROR_TIME', {
          t: settings?.settingSystem?.minPostTaskTime,
        }),
        actions: [{ text: t('PT2_POPUP_ERROR_TIME_CLOSE') }],
      });
    }

    // check posting limit, ex 6AM - 10PM
    const postingLimits = service?.postingLimits;
    if (
      !PostTaskHelpers.checkTimeValidFromService(
        timezone,
        dataTask?.date,
        dataTask?.duration,
        postingLimits,
      )
    ) {
      const postingLimitsFormat = PostTaskHelpers.formatPostingLimits({
        timezone,
        postingLimits,
      });
      return Alert?.alert?.open({
        title: t('DIALOG_TITLE_INFORMATION'),
        message: t('PT2_POPUP_ERROR_TIME_INVALID_CONTENT', {
          from: postingLimitsFormat.from,
          to: postingLimitsFormat.to,
        }),
        actions: [{ text: t('PT2_POPUP_ERROR_TIME_INVALID_CLOSE') }],
      });
    }
    try {
      const bookingId = dataTask?._id;
      if (!bookingId) return;

      const dataResponse = await updatePaymentMethodAPI({
        _id: bookingId,
        askerId: dataTask?.askerId || '',
        payment: PaymentService.formatPaymentMethodInfoToParams(paymentMethod),
      });

      await PaymentService.onPostTaskSuccess({
        bookingId,
        isPrepayment: dataResponse.isPrepayment,
      });
    } catch (error: any) {
      handlePostTaskError(error);
    }
  };

  return {
    handleRepay,
    paymentMethod,
  };
};
