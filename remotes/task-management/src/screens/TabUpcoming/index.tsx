import React, { useCallback } from 'react';
import { ListRenderItemInfo } from 'react-native';
import {
  BlockView,
  Colors,
  EndpointKeys,
  NavigationService,
  queryKeys,
  RouteName,
  TaskManagementRouteName,
  useAppStore,
  useFreshApiFocus,
  useUserStore,
} from '@btaskee/design-system';
import { useFocusEffect } from '@react-navigation/native';

import { EmptyTask, ListTask, TaskItem, TaskSkeletonList } from '@components';
import { useI18n } from '@hooks';

export function TabUpcoming() {
  const { user } = useUserStore();

  const { isoCode } = useAppStore();
  const { t } = useI18n();

  const { data, refetch, isLoading } = useFreshApiFocus({
    key: EndpointKeys.getUpComingTasks,
    queryKey: queryKeys.tasks.upcoming(user?._id!),
    params: {
      isoCode: isoCode!,
    },
    options: {
      enabled: !!user?._id && !!isoCode,
    },
  });

  useFocusEffect(
    useCallback(() => {
      if (user?._id && isoCode) {
        refetch();
      }
    }, [refetch, user?._id, isoCode]),
  );

  const renderItem = useCallback(({ item, index }: ListRenderItemInfo<any>) => {
    const newData = Object.assign({}, item);

    return (
      <TaskItem
        onPress={() =>
          NavigationService.navigate(RouteName.TaskManagement, {
            screen: TaskManagementRouteName.TaskDetail,
            params: {
              taskId: newData._id,
            },
          })
        }
        fromScreen={''}
        isHistoryTask={false}
        user={null}
        handlePrepayment={() =>
          NavigationService.navigate(RouteName.TaskManagement, {
            screen: TaskManagementRouteName.RepayTaskDetail,
            params: {
              taskId: newData._id,
            },
          })
        }
        isOutstandingPayment={false}
        showLoading={false}
        index={index}
        dataTask={newData}
      />
    );
  }, []);

  return (
    <BlockView
      flex
      backgroundColor={Colors.neutralBackground}
    >
      {isLoading ? (
        <TaskSkeletonList count={5} />
      ) : (
        <ListTask
          testID={'scrollUpcoming'}
          data={data || []}
          renderItem={renderItem}
          refetch={refetch}
          ListEmptyComponent={<EmptyTask />}
          contentNotLoggedIn={t('NO_USER_ACTIVITY_UPCOMING_TITLE')}
        />
      )}
    </BlockView>
  );
}
