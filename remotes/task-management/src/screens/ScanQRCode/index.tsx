/**
 * @Filename: ScanQRCode/index.tsx
 * @Description: Main QR Code scanning screen container
 * @CreatedAt: Migrated from legacy codebase
 * @Author: Migration from legacy qr-code/layout/index.js
 */

import React, { useCallback, useEffect, useMemo } from 'react';
import {
  IAcceptedTasker,
  TaskManagementRouteName,
  TaskManagementStackScreenProps,
} from '@btaskee/design-system';

import {
  useQRCodeActions,
  useQRCodeDoNotShowAgain,
  useQRCodeSeen,
} from '@stores';

import { Intro } from './components/Intro';
import { ScanQRCode } from './components/ScanQRCode';

type ScanQRCodeScreenRouteProp =
  TaskManagementStackScreenProps<TaskManagementRouteName.ScanQRCode>;

interface ScanQRCodeScreenParams {
  taskId?: string;
  acceptedTasker?: Array<IAcceptedTasker>;
}

/**
 * Main QR Code scanning screen container
 * Purpose: Orchestrates between intro screen and QR scanning functionality
 * @param route - Navigation route with task parameters
 * @returns {JSX.Element} Either intro screen or QR scanning interface
 */
export const ScanQRCodeScreen: React.FC<ScanQRCodeScreenRouteProp> = ({
  route,
}) => {
  const params = route?.params as ScanQRCodeScreenParams;

  // Use individual selectors to prevent object recreation
  const seen = useQRCodeSeen();
  const doNotShowAgain = useQRCodeDoNotShowAgain();
  const { setSeenQR, setDoNotShowAgain, resetState } = useQRCodeActions();

  useEffect(() => {
    return () => {
      // Reset state when component unmounts
      resetState();
    };
  }, [resetState]);

  /**
   * Handles marking intro as seen
   * Purpose: Updates store state when user completes intro
   */
  const handleSetSeenQR = useCallback(() => {
    setSeenQR(true);
  }, [setSeenQR]);

  /**
   * Handles "do not show again" preference
   * Purpose: Updates store state with user preference
   * @param value - Whether to show intro again
   */
  const handleSetDoNotShowAgain = useCallback(
    (value: boolean) => {
      setDoNotShowAgain(value);
    },
    [setDoNotShowAgain],
  );

  // Memoize the decision to prevent rapid switching between components
  const shouldShowScanQRCode = useMemo(() => {
    return seen || doNotShowAgain;
  }, [seen, doNotShowAgain]);

  // Show QR scanning interface if intro was seen or user chose not to show again
  if (shouldShowScanQRCode) {
    return (
      <ScanQRCode
        taskId={params?.taskId}
        acceptedTasker={params?.acceptedTasker}
      />
    );
  }

  // Show intro screen
  return (
    <Intro
      setSeenQR={handleSetSeenQR}
      setDoNotShowAgain={handleSetDoNotShowAgain}
    />
  );
};
