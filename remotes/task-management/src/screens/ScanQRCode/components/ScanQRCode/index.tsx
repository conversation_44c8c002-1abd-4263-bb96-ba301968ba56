/**
 * @Filename: ScanQRCode/components/ScanQRCode/index.tsx
 * @Description: QR Code scanning component using react-native-vision-camera v4 native code scanner
 * @CreatedAt: Migrated from legacy codebase
 * @Author: Migration from legacy scan-qr-code/index.tsx
 * @Updated: Migrated from vision-camera-code-scanner plugin to native useCodeScanner
 */

import React, { useEffect, useRef, useState } from 'react';
import { Vibration } from 'react-native';
import {
  Camera,
  CameraDevice,
  CameraPosition,
  Code,
  useCodeScanner,
} from 'react-native-vision-camera';
import {
  Alert,
  BlockView,
  Colors,
  ConditionView,
  DeviceHelper,
  IAcceptedTasker,
  PermissionsService,
  SizedBox,
} from '@btaskee/design-system';
import { parseQRcodeToTask, QRCodeTaskData } from '@helper';
import { useIsFocused } from '@react-navigation/native';
import { debounce } from 'lodash-es';
import AnimatedLottieView from 'lottie-react-native';

import { useAppNavigation, useI18n } from '@hooks';
import { scanningLottie } from '@lottie';

import { TaskerInfo } from '../TaskerInfo';
import { styles } from './styles';

interface ScanQRCodeProps {
  taskId?: string;
  acceptedTasker?: Array<IAcceptedTasker>;
}

interface UseCameraDeviceResult {
  device: CameraDevice | undefined;
}

const { WIDTH } = DeviceHelper.WINDOW;
const PADDING_HORIZONTAL = WIDTH * 0.15;
const PADDING_TOP_MASK = 100;
const WIDTH_MASK = WIDTH - PADDING_HORIZONTAL * 2;

/**
 * Custom hook for camera device management
 * Purpose: Manages camera device selection and initialization
 * @param position - Camera position (front/back)
 * @returns Camera device object
 */
const useCameraDevice = (
  position: CameraPosition = 'back',
): UseCameraDeviceResult => {
  const [device, setDevice] = useState<CameraDevice>();

  const getDevices = async () => {
    try {
      const devices = await Camera.getAvailableCameraDevices();
      const matchDevice = devices.find((d) => d.position === position);
      setDevice(matchDevice);
    } catch (error) {
      console.error('Error getting camera devices:', error);
    }
  };

  useEffect(() => {
    getDevices();
  }, [position]);

  return { device };
};

/**
 * QR Code scanning component with camera functionality
 * Purpose: Scans QR codes to verify tasker identity for task confirmation
 * @param taskId - ID of the task to verify
 * @param acceptedTasker - Array of accepted taskers for the task
 * @returns {JSX.Element} Camera view with QR scanning overlay or verification result
 */
export const ScanQRCode: React.FC<ScanQRCodeProps> = ({
  taskId,
  acceptedTasker,
}) => {
  const navigation = useAppNavigation();
  const { t } = useI18n();

  const { device } = useCameraDevice();
  const isFocus = useIsFocused();

  const [isActiveCamera, setIsActiveCamera] = useState(false);
  const [isHasPermission, setIsHasPermission] = useState(false);
  const [correctTasker, setCorrectTasker] = useState(0); // 0: scanning, 1: correct, 2: incorrect

  const maskRef = useRef<AnimatedLottieView>(null);
  const HEIGHT_MASK = WIDTH_MASK / 1;

  /**
   * Handles React state updates when QR code is detected
   * @param qrValue - The QR code value
   */
  const handleQRDetected = (qrValue: string) => {
    setIsActiveCamera(false);
    Vibration.vibrate(300);
    maskRef.current?.pause();
    verifyTasker(qrValue);
  };

  /**
   * Handles QR code scan results from native code scanner
   * @param codes - Array of detected codes
   */
  const onCodeScanned = (codes: Code[]) => {
    if (codes.length > 0) {
      const firstCode = codes[0];

      // Check if it's a QR code with valid value
      if (firstCode.type === 'qr' && firstCode.value) {
        handleQRDetected(firstCode.value);
      }
    }
  };

  /**
   * Native code scanner configuration using react-native-vision-camera v4
   */
  const codeScanner = useCodeScanner({
    codeTypes: ['qr'],
    onCodeScanned: onCodeScanned,
  });

  useEffect(() => {
    checkPermission();
  }, []);

  useEffect(() => {
    setIsActiveCamera(isFocus);
  }, [isFocus]);

  /**
   * Resumes camera scanning
   * Purpose: Reactivates camera and animation after error or pause
   */
  const onResumeCamera = () => {
    setIsActiveCamera(true);
    maskRef.current?.resume();
  };

  /**
   * Verifies tasker from QR code data
   * @param QRCodeData - Raw QR code string
   */
  const verifyTasker = debounce((QRCodeData: string) => {
    const dataParse: QRCodeTaskData = parseQRcodeToTask(QRCodeData);

    if (dataParse?.taskId) {
      const { taskIdFlag, taskerIdFlag } = _verifyTasker(
        dataParse?.taskId,
        dataParse?.taskerId,
      );

      const isCorrect = taskIdFlag && taskerIdFlag;
      setCorrectTasker(isCorrect ? 1 : 2);
    } else {
      // Wrong format QR code from Tasker
      Alert.alert.open({
        title: t('DIALOG_TITLE_INFORMATION'),
        message: t('PLEASE_SCAN_BTASKEE_QR'),
        actions: [{ text: t('CLOSE'), onPress: onResumeCamera }],
      });
    }
  }, 300);

  /**
   * Internal verification logic
   * Purpose: Checks if QR code matches task ID and accepted tasker
   * @param qrTaskId - Task ID from QR code
   * @param qrTaskerId - Tasker ID from QR code
   * @returns Verification flags for task and tasker
   */
  const _verifyTasker = (qrTaskId?: string, qrTaskerId?: string) => {
    try {
      let taskIdFlag = false;
      let taskerIdFlag = false;

      if (taskId === qrTaskId) {
        taskIdFlag = true;
      }

      if (acceptedTasker?.find((e) => e.taskerId === qrTaskerId)) {
        taskerIdFlag = true;
      }

      return { taskIdFlag, taskerIdFlag };
    } catch (error) {
      return { taskIdFlag: false, taskerIdFlag: false };
    }
  };

  /**
   * Checks and requests camera permission
   * Purpose: Ensures camera access before starting scanning
   */
  const checkPermission = () => {
    PermissionsService.checkRequestCamera({
      onGranted: () => {
        setIsHasPermission(true);
      },
      onCloseNotGranted: () => {
        if (navigation.canGoBack?.()) {
          navigation.goBack();
        }
      },
    });
  };

  // Show verification result screens
  if (correctTasker === 1) {
    return (
      <TaskerInfo
        isSuccess={true}
        navigation={navigation}
      />
    );
  }

  if (correctTasker === 2) {
    return (
      <TaskerInfo
        isSuccess={false}
        navigation={navigation}
      />
    );
  }

  return (
    <BlockView style={styles.container}>
      <ConditionView
        condition={Boolean(device && isHasPermission)}
        viewTrue={
          <BlockView style={styles.full}>
            {device && (
              <Camera
                style={styles.full}
                device={device}
                isActive={isActiveCamera}
                codeScanner={codeScanner}
              />
            )}
            <BlockView style={styles.maskContainer}>
              {/* Mask top */}
              <SizedBox
                height={PADDING_TOP_MASK}
                color={Colors.neutral800}
                opacity={0.7}
              />
              <BlockView row>
                {/* Mask left */}
                <SizedBox
                  width={PADDING_HORIZONTAL}
                  color={Colors.neutral800}
                  opacity={0.7}
                />
                <BlockView style={{ width: WIDTH_MASK, height: HEIGHT_MASK }}>
                  <AnimatedLottieView
                    ref={maskRef}
                    resizeMode="cover"
                    style={styles.full}
                    autoPlay={true}
                    loop={true}
                    source={scanningLottie}
                  />
                </BlockView>
                {/* Mask right */}
                <SizedBox
                  width={PADDING_HORIZONTAL}
                  color={Colors.neutral800}
                  opacity={0.7}
                />
              </BlockView>
              {/* Mask bottom */}
              <SizedBox
                height={'100%'}
                color={Colors.neutral800}
                opacity={0.7}
              />
            </BlockView>
          </BlockView>
        }
      />
    </BlockView>
  );
};
