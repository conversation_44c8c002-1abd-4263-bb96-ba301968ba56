/**
 * @Filename: ScanQRCode/components/TaskerInfo/index.tsx
 * @Description: QR Code verification result screen
 * @CreatedAt: Migrated from legacy codebase
 * @Author: Migration from legacy tasker-info.js
 */

import React from 'react';
import {
  BlockView,
  BottomView,
  Colors,
  CText,
  CUSTOMER_SUPPORT_PHONE,
  DeviceHelper,
  FontSizes,
  IconAssets,
  IconImage,
  ISO_CODE,
  NavigationService,
  openUrl,
  PrimaryButton,
  useAppStore,
  useSettingsStore,
} from '@btaskee/design-system';

import { useI18n } from '@hooks';

import { styles } from './styles';

const width = DeviceHelper.WINDOW.WIDTH;

interface TaskerInfoProps {
  isSuccess: boolean;
  navigation?: any;
}

/**
 * QR Code verification result component
 * Purpose: Shows verification result (success/failure) and appropriate actions
 * @param isSuccess - Whether the QR code verification was successful
 * @param navigation - Navigation object for going back
 * @returns {JSX.Element} Verification result screen with appropriate icon and action
 */
export const TaskerInfo: React.FC<TaskerInfoProps> = ({
  isSuccess,
  navigation,
}) => {
  const { t } = useI18n();
  const { isoCode } = useAppStore();
  const { settings } = useSettingsStore();

  const supports = settings?.askerSetting?.supports;

  /**
   * Handles support contact
   * Purpose: Opens support contact when verification fails
   */
  const handleContactSupport = () => {
    const hotline =
      supports?.hotline ||
      (CUSTOMER_SUPPORT_PHONE[isoCode as ISO_CODE] as string);
    // Use the openUrl helper from design system to call support
    openUrl(`tel:${hotline}`); // Default support number, should be configurable
  };

  /**
   * Handles confirmation and navigation back
   * Purpose: Navigates back when verification is successful
   */
  const handleConfirm = () => {
    if (navigation?.canGoBack?.()) {
      navigation.goBack();
    } else {
      NavigationService.goBack();
    }
  };

  return (
    <BlockView flex>
      <BlockView
        center
        flex
      >
        <BlockView
          center
          style={styles.iconContainer}
        >
          <IconImage
            source={isSuccess ? IconAssets.icCheck : IconAssets.icCloseFill}
            size={Math.round(width / 3)}
            color={isSuccess ? Colors.green500 : Colors.red500}
          />
        </BlockView>
        <BlockView
          center
          style={styles.textContainer}
        >
          <CText
            size={FontSizes.SIZE_20}
            center
          >
            {isSuccess ? t('VERIFIED_WORKER') : t('INCORRECT_TASKER')}
          </CText>
          {!isSuccess && (
            <CText
              size={FontSizes.SIZE_16}
              center
              style={styles.descriptionText}
            >
              {t('UNVERIFIED_WORKER')}
            </CText>
          )}
        </BlockView>
      </BlockView>
      <BottomView>
        <PrimaryButton
          title={isSuccess ? t('CONFIRM') : t('ACCOUNT_SUPPORT_ACTION')}
          onPress={isSuccess ? handleConfirm : handleContactSupport}
        />
      </BottomView>
    </BlockView>
  );
};
