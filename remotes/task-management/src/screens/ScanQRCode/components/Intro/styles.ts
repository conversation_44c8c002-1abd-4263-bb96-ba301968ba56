import { StyleSheet } from 'react-native';
import {
  Colors,
  DeviceHelper,
  FontSizes,
  Spacing,
} from '@btaskee/design-system';

const SIZE_IMAGE = Math.round(DeviceHelper.WINDOW.WIDTH / 1.5);

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.neutralWhite,
  },
  wrapContent: {
    paddingHorizontal: Spacing.SPACE_16,
    paddingTop: Spacing.SPACE_16,
  },
  wrapCheckbox: {
    paddingTop: Spacing.SPACE_24,
    marginBottom: '20%',
  },
  txtInstruction: {
    marginVertical: Spacing.SPACE_24,
  },
  txtContent: {
    fontSize: FontSizes.SIZE_14,
  },
  imageAvatar: {
    width: SIZE_IMAGE,
    height: SIZE_IMAGE,
    borderColor: Colors.neutralWhite,
    borderWidth: 1,
  },
  wrapButton: {
    paddingBottom: Spacing.SPACE_16,
  },
});
