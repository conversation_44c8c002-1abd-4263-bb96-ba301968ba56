/**
 * @Filename: ScanQRCode/components/Intro/index.tsx
 * @Description: QR Code intro screen component
 * @CreatedAt: Migrated from legacy codebase
 * @Author: Migration from legacy intro.js
 */

import React, { useState } from 'react';
import {
  BlockView,
  BottomView,
  CheckBox,
  CText,
  FastImage,
  FontSizes,
  PrimaryButton,
  ScrollView,
} from '@btaskee/design-system';

import { useI18n } from '@hooks';
import { bgIntroScanQR } from '@images';

import { styles } from './styles';

interface IntroProps {
  setSeenQR: () => void;
  setDoNotShowAgain: (value: boolean) => void;
}

/**
 * QR Code intro screen component
 * Purpose: Shows introduction and instructions for QR code scanning feature
 * @param setSeenQR - Function to mark intro as seen
 * @param setDoNotShowAgain - Function to set "do not show again" preference
 * @returns {JSX.Element} Intro screen with instructions and checkbox
 */
export const Intro: React.FC<IntroProps> = ({
  setSeenQR,
  setDoNotShowAgain,
}) => {
  const { t } = useI18n();
  const [checked, setChecked] = useState(false);

  /**
   * Handles intro completion
   * Purpose: Marks intro as seen and applies user preference
   */
  const handleContinue = () => {
    setSeenQR();
    setDoNotShowAgain(checked);
  };

  return (
    <BlockView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <BlockView center>
          <FastImage
            style={styles.imageAvatar}
            resizeMode="cover"
            source={bgIntroScanQR}
          />
        </BlockView>
        <BlockView style={styles.wrapContent}>
          <CText style={styles.txtContent}>{t('QR_CODE_1')}</CText>
          <CText
            bold
            size={FontSizes.SIZE_16}
            style={styles.txtInstruction}
          >
            {t('HOW_TO_USE')}
          </CText>
          <CText style={styles.txtContent}>{t('QR_CODE_2')}</CText>
          <BlockView style={styles.wrapCheckbox}>
            <CheckBox
              onChecked={setChecked}
              title={t('DO_NOT_SHOW_AGAIN')}
            />
          </BlockView>
        </BlockView>
      </ScrollView>
      <BottomView>
        <PrimaryButton
          onPress={handleContinue}
          title={t('UNDERSTOOD')}
        />
      </BottomView>
    </BlockView>
  );
};
