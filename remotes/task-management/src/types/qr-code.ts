/**
 * TypeScript types and interfaces for QR Code functionality
 */

/**
 * QR Code task data extracted from scanned code
 */
export interface QRCodeTaskData {
  taskId?: string;
  taskerId?: string;
}

/**
 * Accepted tasker information for verification
 */
export interface AcceptedTasker {
  taskerId: string;
  [key: string]: any; // Allow additional properties
}

/**
 * QR Code verification result flags
 */
export interface VerificationFlags {
  taskIdFlag: boolean;
  taskerIdFlag: boolean;
}

/**
 * QR Code scanning screen route parameters
 */
export interface ScanQRCodeScreenParams {
  taskId?: string;
  acceptedTasker?: AcceptedTasker[];
}

/**
 * QR Code intro component props
 */
export interface IntroProps {
  setSeenQR: () => void;
  setDoNotShowAgain: (value: boolean) => void;
}

/**
 * QR Code scanning component props
 */
export interface ScanQRCodeProps {
  taskId?: string;
  acceptedTasker?: AcceptedTasker[];
}

/**
 * Tasker info verification result component props
 */
export interface TaskerInfoProps {
  isSuccess: boolean;
  navigation?: any;
}

/**
 * QR Code store state interface
 */
export interface QRCodeState {
  seen: boolean;
  doNotShowAgain: boolean;
}

/**
 * QR Code store actions interface
 */
export interface QRCodeActions {
  setSeenQR: (seen?: boolean) => void;
  setDoNotShowAgain: (doNotShow: boolean) => void;
  resetState: () => void;
}

/**
 * Combined QR Code store interface
 */
export type QRCodeStore = QRCodeState & QRCodeActions;

/**
 * Camera device hook result
 */
export interface UseCameraDeviceResult {
  device: any; // CameraDevice from react-native-vision-camera
}

/**
 * QR Code verification status enum
 */
export enum QRCodeVerificationStatus {
  SCANNING = 0,
  VERIFIED = 1,
  INCORRECT = 2,
}

/**
 * Permission check callback functions
 */
export interface PermissionCallbacks {
  onGranted?: () => void;
  onCloseNotGranted?: () => void;
}
