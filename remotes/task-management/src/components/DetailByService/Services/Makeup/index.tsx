import React, { useMemo } from 'react';
import {
  BlockView,
  Colors,
  ConditionView,
  CText,
  DateTimeHelpers,
  DateWithGMT,
  FontSizes,
  getTextWithLocale,
  icDateFill,
  IconImage,
  ITaskDetail,
  TypeFormatDate,
} from '@btaskee/design-system';
import { isEmpty } from 'lodash-es';

import { useI18n } from '@hooks';
import { icMakeupFill } from '@images';

import { TaskNote } from '../../Components';
import { styles } from './styles';

// Types
interface MakeupDetailProps {
  dataTask: ITaskDetail;
  onPressUpdate?: () => void;
}

/**
 * Makeup Detail Component for Task Management
 * Purpose: Shows detailed information about makeup tasks including services, packages, and options
 * @param dataTask - Task detail data containing makeup information
 * @param onPressUpdate - Optional callback for update actions
 * @returns {JSX.Element} Makeup detail component
 */
export const MakeupDetail: React.FC<MakeupDetailProps> = ({ dataTask }) => {
  const { t } = useI18n();

  // Extract data from task
  const {
    date,
    taskNote,
    taskPlace,
    serviceName,
    _id: taskId,
    status: taskStatus,
    service,
  } = dataTask;
  const makeupData = (dataTask as any)?.detailMakeup || {};
  const { packages = [] } = makeupData;

  // Get timezone from task location
  const timezone = useMemo(() => {
    return DateTimeHelpers.getTimezoneByCity(taskPlace?.city);
  }, [taskPlace?.city]);

  return (
    <BlockView style={styles.container}>
      <BlockView flex>
        {/* Work Time Section */}
        <BlockView
          border={{ bottom: { width: 1, color: Colors.neutral100 } }}
          padding={{ bottom: 16 }}
        >
          <CText
            bold
            color={Colors.neutral500}
            size={FontSizes.SIZE_14}
          >
            {t('WORK_TIME')}
          </CText>
          <BlockView
            row
            margin={{ top: 12 }}
          >
            <IconImage
              source={icDateFill}
              color={Colors.orange500}
              size={22}
              style={styles.icon}
            />
            <DateWithGMT
              testID="workingDay"
              timezone={timezone}
              date={date}
              color={Colors.neutral500}
              style={styles.txtValue}
              typeFormat={TypeFormatDate.DateTimeFullWithDayAndTimeFirst}
            />
          </BlockView>
        </BlockView>

        {/* Task Detail Section */}
        <BlockView
          padding={{ bottom: 16 }}
          margin={{ top: 16 }}
        >
          <BlockView>
            <CText
              color={Colors.orange500}
              bold
              size={FontSizes.SIZE_14}
            >
              {getTextWithLocale(service?.text)}
            </CText>

            {/* Packages Section */}
            <BlockView margin={{ top: 8 }}>
              {packages?.map((item: any, index: number) => {
                return (
                  <BlockView
                    margin={{ bottom: 12 }}
                    key={`item-${index}`}
                  >
                    <ConditionView
                      condition={packages.length > 1}
                      viewTrue={
                        <CText
                          bold
                          color={Colors.neutral500}
                          size={FontSizes.SIZE_14}
                          style={styles.txtCustomer}
                        >
                          {t('NUMBER_CUSTOMER', {
                            count: index + 1,
                          })}
                        </CText>
                      }
                    />
                    <BlockView row>
                      <BlockView flex>
                        {item?.mainServices &&
                          item?.mainServices?.map((mainService: any) => {
                            return (
                              <BlockView
                                flex
                                key={mainService?.name}
                                row
                                style={styles.wrapMainService}
                              >
                                <IconImage
                                  source={icMakeupFill}
                                  color={Colors.orange500}
                                  size={22}
                                  style={styles.icon}
                                />
                                <BlockView flex>
                                  <CText
                                    color={Colors.neutral500}
                                    size={FontSizes.SIZE_14}
                                  >
                                    {getTextWithLocale(mainService?.title)}
                                  </CText>

                                  {/* Main Service Options */}
                                  <ConditionView
                                    condition={!isEmpty(mainService?.options)}
                                    viewTrue={
                                      <BlockView>
                                        {mainService?.options &&
                                          mainService?.options?.map(
                                            (option: any) => {
                                              return (
                                                <BlockView
                                                  key={option?.name}
                                                  row
                                                  style={styles.wrapStyle}
                                                >
                                                  <CText
                                                    color={Colors.neutral400}
                                                    size={FontSizes.SIZE_14}
                                                  >
                                                    {`• ${getTextWithLocale(
                                                      option?.title,
                                                    )}`}
                                                  </CText>
                                                </BlockView>
                                              );
                                            },
                                          )}
                                      </BlockView>
                                    }
                                  />

                                  {/* Style Options */}
                                  <ConditionView
                                    condition={!isEmpty(mainService?.style)}
                                    viewTrue={
                                      <BlockView style={styles.wrapStyle}>
                                        <CText
                                          color={Colors.neutral300}
                                          size={FontSizes.SIZE_14}
                                        >
                                          {getTextWithLocale(
                                            mainService?.style?.title,
                                          )}
                                        </CText>
                                        <ConditionView
                                          condition={
                                            !isEmpty(
                                              mainService?.style?.options,
                                            )
                                          }
                                          viewTrue={
                                            <BlockView>
                                              {mainService?.style?.options?.map(
                                                (option: any) => {
                                                  return (
                                                    <BlockView
                                                      key={option?.name}
                                                      row
                                                      style={styles.wrapStyle}
                                                    >
                                                      <CText
                                                        color={
                                                          Colors.neutral400
                                                        }
                                                        size={FontSizes.SIZE_14}
                                                      >
                                                        {`• ${getTextWithLocale(
                                                          option?.title,
                                                        )}`}
                                                      </CText>
                                                    </BlockView>
                                                  );
                                                },
                                              )}
                                            </BlockView>
                                          }
                                        />
                                      </BlockView>
                                    }
                                  />
                                </BlockView>
                              </BlockView>
                            );
                          })}
                      </BlockView>
                    </BlockView>
                  </BlockView>
                );
              })}
            </BlockView>
          </BlockView>
        </BlockView>

        {/* Task Note */}
        <TaskNote
          taskNote={taskNote}
          serviceName={serviceName}
          taskId={taskId}
          taskStatus={taskStatus}
        />
      </BlockView>
    </BlockView>
  );
};
