import React, { useMemo } from 'react';
import {
  BlockView,
  Card,
  Colors,
  ConditionView,
  CText,
  DateTimeHelpers,
  DateWithGMT,
  FontSizes,
  getTextWithLocale,
  icDateFill,
  IconImage,
  ITaskDetail,
  Spacing,
  TypeFormatDate,
} from '@btaskee/design-system';
import { isEmpty } from 'lodash-es';

import { useI18n } from '@hooks';
import { icScissors } from '@images';

import { TaskNote } from '../../Components';
import { styles } from './styles';

// Types
interface HairStylingDetailProps {
  dataTask: ITaskDetail;
  onPressUpdate?: () => void;
}

/**
 * Component for displaying hair styling task details
 * Purpose: Shows detailed information about hair styling tasks including services, packages, and options
 * @param dataTask - Task detail data containing hair styling information
 * @param onPressUpdate - Optional callback for update actions
 * @returns {JSX.Element} Hair styling detail component
 */
export const HairStylingDetail: React.FC<HairStylingDetailProps> = ({
  dataTask,
}) => {
  const { t } = useI18n();

  // Extract data from task
  const {
    date,
    taskNote,
    taskPlace,
    _id: taskId,
    status: taskStatus,
    service,
    serviceName,
  } = dataTask;

  // Get hair styling specific data from task detail
  // Note: This assumes the task detail contains hair styling packages data
  // The actual structure may need to be adjusted based on the ITaskDetail interface
  const hairStylingData = (dataTask as any)?.detailHairStyling || {};
  const { packages = [] } = hairStylingData;

  const timezone = useMemo(
    () => DateTimeHelpers.getTimezoneByCity(taskPlace?.city),
    [taskPlace?.city],
  );

  return (
    <BlockView margin={{ top: Spacing.SPACE_24 }}>
      <CText
        bold
        testID="txtTaskDetail"
        size={FontSizes.SIZE_16}
        margin={{ bottom: Spacing.SPACE_12 }}
      >
        {t('TASK_DETAIL')}
      </CText>

      <BlockView style={styles.container}>
        <BlockView flex>
          {/* Work Time Section */}
          <BlockView
            border={{ bottom: { width: 1, color: Colors.neutral100 } }}
            padding={{ bottom: 16 }}
          >
            <CText
              bold
              color={Colors.neutral500}
              size={FontSizes.SIZE_14}
            >
              {t('WORK_TIME')}
            </CText>
            <BlockView
              row
              margin={{ top: 12 }}
            >
              <IconImage
                source={icDateFill}
                color={Colors.orange500}
                size={22}
                style={styles.icon}
              />
              <DateWithGMT
                testID="workingDay"
                timezone={timezone}
                date={date}
                color={Colors.neutral500}
                style={styles.txtValue}
                typeFormat={TypeFormatDate.DateTimeFullWithDayAndTimeFirst}
              />
            </BlockView>
          </BlockView>

          {/* Task Detail Section */}
          <BlockView
            padding={{ bottom: 16 }}
            margin={{ top: 16 }}
          >
            <BlockView>
              <CText
                color={Colors.orange500}
                bold
                size={FontSizes.SIZE_14}
              >
                {getTextWithLocale(service?.text)}
              </CText>

              {/* Hair Styling Packages */}
              <ConditionView
                condition={!isEmpty(packages)}
                viewTrue={
                  <BlockView margin={{ top: 8 }}>
                    {packages?.map((item: any, index: number) => {
                      return (
                        <BlockView
                          margin={{ bottom: 12 }}
                          key={index}
                        >
                          <ConditionView
                            condition={packages.length > 1}
                            viewTrue={
                              <CText
                                bold
                                color={Colors.neutral500}
                                size={FontSizes.SIZE_14}
                                style={styles.txtCustomer}
                              >
                                {t('NUMBER_CUSTOMER', {
                                  count: index + 1,
                                })}
                              </CText>
                            }
                          />
                          <BlockView row>
                            <BlockView flex>
                              {item?.mainServices &&
                                item?.mainServices?.map((mainService: any) => {
                                  return (
                                    <BlockView
                                      flex
                                      key={mainService?.name}
                                      row
                                      style={styles.wrapMainService}
                                    >
                                      <IconImage
                                        source={icScissors}
                                        color={Colors.orange500}
                                        size={22}
                                        style={styles.icon}
                                      />
                                      <BlockView flex>
                                        <CText
                                          color={Colors.neutral500}
                                          size={FontSizes.SIZE_14}
                                        >
                                          {getTextWithLocale(
                                            mainService?.title,
                                          )}
                                        </CText>

                                        {/* Service Options */}
                                        <ConditionView
                                          condition={
                                            !isEmpty(mainService?.options)
                                          }
                                          viewTrue={
                                            <BlockView>
                                              {mainService?.options &&
                                                mainService?.options?.map(
                                                  (option: any) => {
                                                    return (
                                                      <BlockView
                                                        key={option?.name}
                                                        row
                                                        style={styles.wrapStyle}
                                                      >
                                                        <CText
                                                          color={
                                                            Colors.neutral400
                                                          }
                                                          size={
                                                            FontSizes.SIZE_14
                                                          }
                                                        >
                                                          {`• ${getTextWithLocale(
                                                            option?.title,
                                                          )}`}
                                                        </CText>
                                                      </BlockView>
                                                    );
                                                  },
                                                )}
                                            </BlockView>
                                          }
                                        />

                                        {/* Color Options */}
                                        <ConditionView
                                          condition={
                                            !isEmpty(mainService?.color)
                                          }
                                          viewTrue={
                                            <BlockView
                                              row
                                              style={styles.wrapStyle}
                                            >
                                              <CText
                                                color={Colors.neutral400}
                                                size={FontSizes.SIZE_14}
                                              >
                                                {`• ${getTextWithLocale(
                                                  mainService?.color?.title,
                                                )}`}
                                              </CText>
                                            </BlockView>
                                          }
                                        />
                                      </BlockView>
                                    </BlockView>
                                  );
                                })}
                            </BlockView>
                          </BlockView>
                        </BlockView>
                      );
                    })}
                  </BlockView>
                }
              />
            </BlockView>
          </BlockView>
        </BlockView>

        {/* Task Note */}
        <TaskNote
          taskNote={taskNote}
          serviceName={serviceName}
          taskId={taskId}
          taskStatus={taskStatus}
        />
      </BlockView>
    </BlockView>
  );
};
