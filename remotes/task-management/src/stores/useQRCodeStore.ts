/**
 * Zustand store for QR Code state management
 * Replaces Redux usage from legacy codebase for QR code functionality
 */
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

interface QRCodeState {
  // QR Code intro state
  seen: boolean;
  doNotShowAgain: boolean;
}

interface QRCodeActions {
  // State setters
  setSeenQR: (seen?: boolean) => void;
  setDoNotShowAgain: (doNotShow: boolean) => void;
  resetState: () => void;
}

type QRCodeStore = QRCodeState & QRCodeActions;

const initialState: QRCodeState = {
  seen: false,
  doNotShowAgain: false,
};

/**
 * QR Code store for managing intro screen visibility and user preferences
 * Purpose: Manages whether to show QR code intro screen and user's "do not show again" preference
 * @returns QR code state and actions for intro flow management
 */
export const useQRCodeStore = create<QRCodeStore>()(
  devtools(
    (set, get) => ({
      // Initial state
      ...initialState,

      // Actions
      setSeenQR: (seen = true) => {
        set({ seen }, false, 'setSeenQR');
      },

      setDoNotShowAgain: (doNotShow: boolean) => {
        set({ doNotShowAgain: doNotShow }, false, 'setDoNotShowAgain');
      },

      resetState: () => {
        const currentState = get();
        set(
          {
            ...initialState,
            doNotShowAgain: currentState.doNotShowAgain, // Preserve doNotShowAgain setting
          },
          false,
          'resetState',
        );
      },
    }),
    {
      name: 'qr-code-store',
    },
  ),
);

// Individual state selectors to prevent object recreation
export const useQRCodeSeen = () => useQRCodeStore((state) => state.seen);
export const useQRCodeDoNotShowAgain = () =>
  useQRCodeStore((state) => state.doNotShowAgain);

// Combined state selector with shallow comparison (use sparingly)
export const useQRCodeState = () =>
  useQRCodeStore((state) => ({
    seen: state.seen,
    doNotShowAgain: state.doNotShowAgain,
  }));

// Individual action selectors to prevent infinite re-renders
export const useQRCodeActions = () => ({
  setSeenQR: useQRCodeStore((state) => state.setSeenQR),
  setDoNotShowAgain: useQRCodeStore((state) => state.setDoNotShowAgain),
  resetState: useQRCodeStore((state) => state.resetState),
});
