import {
  DateTimeHelpers,
  getTextWithLocale,
  IEventTaskAbandoned,
  IEventTaskPostSuccess,
  SERVICES,
  TrackingActions,
  TrackingPostTaskStep,
  TrackingScreenNames,
  TrackingServices,
  TypeFormatDate,
} from '@btaskee/design-system';
import { isEmpty } from 'lodash-es';

import { usePostTaskStore } from '@stores';

/**
 * Laundry Service Tracking Hook
 *
 * Provides comprehensive tracking functionality for laundry service following
 * the multi-provider tracking architecture with provider-agnostic interface.
 *
 * Features:
 * - Screen view tracking
 * - User action tracking (back, next, changes)
 * - Task abandonment tracking
 * - Task success tracking
 * - Laundry-specific data tracking (washing, dryClean, others, city)
 * - App state change tracking
 * - Navigation tracking patterns
 */
export const useTracking = () => {
  const { setStepPostTask, setIsBookedTask } = usePostTaskStore();

  /**
   * Track back action from service selection screen
   * Includes comprehensive laundry service data
   */
  const trackingBackChooseService = () => {
    const currentState = usePostTaskStore.getState();
    const { service, washing, dryClean, others, otherText } = currentState;

    TrackingServices.trackingServiceClick({
      screenName: TrackingScreenNames.DetailInformation,
      serviceName: SERVICES.LAUNDRY,
      action: TrackingActions.Back,
      isTetBooking: service?.isTet,
      additionalInfo: {
        laundryServices: {
          washing: washing,
          dryClean: dryClean,
          others: others,
          otherText: otherText,
        },
      },
    });
  };

  /**
   * Track next step from service selection
   */
  const trackingChooseServiceNextStep = () => {
    const currentState = usePostTaskStore.getState();
    const { service, washing, dryClean, others, otherText, city } =
      currentState;

    TrackingServices.trackingServiceClick({
      screenName: TrackingScreenNames.DetailInformation,
      serviceName: SERVICES.LAUNDRY,
      action: TrackingActions.Next,
      isTetBooking: service?.isTet,
      additionalInfo: {
        laundryServices: {
          washing: washing ? getTextWithLocale(washing.text) : null,
          dryClean: dryClean ? getTextWithLocale(dryClean.text) : null,
          others: others ? getTextWithLocale(others.text) : null,
          otherText: otherText || null,
          city: city ? city.map((c: any) => getTextWithLocale(c.text)) : null,
        },
      },
    });
  };

  /**
   * Track back/next actions from date-time selection screen
   */
  const trackingNextBackActionChooseDateTime = ({
    action,
  }: {
    action: TrackingActions;
  }) => {
    const currentState = usePostTaskStore.getState();
    const { service, date, schedule, address, note, collectionDate } =
      currentState;
    const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

    TrackingServices.trackingServiceClick({
      screenName: TrackingScreenNames.ChooseWorkingTime,
      serviceName: SERVICES.LAUNDRY,
      action,
      isTetBooking: service?.isTet,
      additionalInfo: {
        workingTime: {
          date: DateTimeHelpers.formatToString({
            timezone,
            date,
            typeFormat: TypeFormatDate.DateShort,
          }),
          time: DateTimeHelpers.formatToString({
            timezone,
            date,
            typeFormat: TypeFormatDate.TimeHourMinute,
          }),
          weeklySchedule: isEmpty(schedule) ? null : schedule,
        },
        collectionDate: collectionDate
          ? DateTimeHelpers.formatToString({
              timezone,
              date: collectionDate,
              typeFormat: TypeFormatDate.DateShort,
            })
          : null,
        note,
      },
    });
  };

  /**
   * Track screen view for date-time selection
   */
  const trackingChooseDateTimeScreenView = () => {
    TrackingServices.trackingServiceView({
      screenName: TrackingScreenNames.ChooseWorkingTime,
      serviceName: SERVICES.LAUNDRY,
      entryPoint: TrackingScreenNames.DetailInformation,
    });
  };

  /**
   * Track screen view for service selection
   */
  const trackingChooseServiceScreenView = (entryPoint: string) => {
    TrackingServices.trackingServiceView({
      serviceName: SERVICES.LAUNDRY,
      screenName: TrackingScreenNames.DetailInformation,
      entryPoint: entryPoint,
    });
  };

  /**
   * Track task abandonment with laundry-specific data
   */
  const trackingPostTaskAbandoned = async (action: TrackingActions) => {
    const currentState = usePostTaskStore.getState();
    const {
      service,
      isBookedTask,
      price,
      address,
      duration,
      date,
      promotion,
      stepPostTask,
    } = currentState;

    setStepPostTask(TrackingPostTaskStep.STEP_2);

    // If the task has been booked, the event will not be recorded
    if (isBookedTask) {
      return setIsBookedTask(false);
    }

    const params: IEventTaskAbandoned = {
      action: action,
      step: stepPostTask,
      serviceId: service?._id,
      serviceName: SERVICES.LAUNDRY,
      price: price?.finalCost,
      duration: duration,
      address: address?.address,
      district: address?.district,
      city: address?.city,
      country: address?.country,
      date: date,
      promotionCode: promotion?.code,
      isTetBooking: service?.isTet,
    };
    TrackingServices.trackingTaskAbandoned(params);
  };

  /**
   * Track confirm payment screen view
   */
  const trackingConfirmPaymentScreenView = () => {
    TrackingServices.trackingServiceView({
      screenName: TrackingScreenNames.ConfirmPayment,
      serviceName: SERVICES.LAUNDRY,
      entryPoint: TrackingScreenNames.ChooseWorkingTime,
    });
  };

  /**
   * Track back/next actions from confirm payment screen
   */
  const trackingNextBackActionConfirmPayment = ({
    action,
  }: {
    action: TrackingActions;
  }) => {
    const currentState = usePostTaskStore.getState();
    const {
      service,
      forceTasker,
      paymentMethod,
      address,
      promotion,
      isBookedTask,
    } = currentState;

    if (isBookedTask) {
      return null;
    }

    TrackingServices.trackingServiceClick({
      screenName: TrackingScreenNames.ConfirmPayment,
      serviceName: SERVICES.LAUNDRY,
      action,
      isTetBooking: service?.isTet,
      featureName: isEmpty(forceTasker) ? '' : 'BookWithFavTasker',
      taskerID: forceTasker?._id,
      additionalInfo: {
        phoneNumber: address?.phoneNumber,
        contactName: address?.contact,
        paymentMethod: {
          method: paymentMethod?.name,
          promotion: promotion?.code,
        },
      },
    });
  };

  /**
   * Track successful task posting with laundry-specific data
   */
  const trackingPostTaskSuccess = () => {
    const currentState = usePostTaskStore.getState();
    const {
      service,
      duration,
      date,
      address,
      promotion,
      price,
      schedule,
      isPremium,
      forceTasker,
      washing,
      dryClean,
      others,
      city,
      collectionDate,
    } = currentState;

    const params: IEventTaskPostSuccess = {
      serviceId: service?._id,
      serviceName: SERVICES.LAUNDRY,
      duration: duration,
      address: address?.address,
      district: address?.district,
      city: address?.city,
      country: address?.country,
      date: date,
      promotionCode: promotion?.code,
      taskValue: price?.finalCost,
      schedule: schedule?.map(String),
      isPremium: isPremium,
      forceTaskerId: forceTasker?._id,
      // Laundry-specific tracking data
      additionalInfo: {
        laundryServices: {
          washing: washing ? getTextWithLocale(washing.text) : null,
          dryClean: dryClean ? getTextWithLocale(dryClean.text) : null,
          others: others ? getTextWithLocale(others.text) : null,
          city: city ? city.map((c: any) => getTextWithLocale(c.text)) : null,
        },
        collectionDate: collectionDate,
      },
    };
    TrackingServices.trackingTaskPostSuccess(params);
  };

  /**
   * Track address selection and navigation to service selection
   * Called when user selects an address and moves to ChooseService screen
   */
  const trackingChooseAddressNextStep = () => {
    const currentState = usePostTaskStore.getState();
    const { address } = currentState;

    TrackingServices.trackingServiceClick({
      screenName: TrackingScreenNames.ChooseAddress,
      serviceName: SERVICES.LAUNDRY,
      action: TrackingActions.Next,
      additionalInfo: {
        address: address?.address,
        district: address?.district,
        city: address?.city,
        country: address?.country,
      },
    });
  };

  return {
    trackingBackChooseService,
    trackingChooseServiceNextStep,
    trackingNextBackActionChooseDateTime,
    trackingChooseDateTimeScreenView,
    trackingChooseServiceScreenView,
    trackingPostTaskAbandoned,
    trackingConfirmPaymentScreenView,
    trackingNextBackActionConfirmPayment,
    trackingPostTaskSuccess,
    trackingChooseAddressNextStep,
  };
};
