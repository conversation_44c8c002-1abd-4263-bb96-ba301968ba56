import {
  Alert,
  DateTimeHelpers,
  EndpointKeys,
  handleError,
  IApiError,
  PaymentService,
  PostTaskHelpers,
  TrackingPostTaskStep,
  useApiMutation,
  useAppLoadingStore,
  useAppStore,
  usePostTaskAction,
  useUserStore,
} from '@btaskee/design-system';
import { debounce, isEmpty } from 'lodash-es';

import { useI18n, useTracking } from '@hooks';
import { usePostTaskStore } from '@stores';

import {
  buildDetailLaundryData,
  validateLaundryData,
} from '..//utils/taskDataBuilders';

export const usePostTask = () => {
  const { t } = useI18n();

  const {
    setPrice,
    setLoadingPrice,
    resetState,
    setIsBookedTask,
    setStepPostTask,
    setDuration,
  } = usePostTaskStore();
  const { showLoading, hideLoading } = useAppLoadingStore();
  const { trackingPostTaskSuccess } = useTracking();
  const { handlePostTaskError } = usePostTaskAction();

  const { user } = useUserStore();
  const { isoCode } = useAppStore();

  const { mutate: getPriceLaundry } = useApiMutation({
    key: EndpointKeys.getPriceLaundry,
    options: {
      onMutate: () => {
        setLoadingPrice(true);
        showLoading();
      },
      onSettled: () => {
        setLoadingPrice(false);
        hideLoading();
      },
    },
  });

  const { mutate: postTaskLaundry } = useApiMutation({
    key: EndpointKeys.postTaskLaundry,
    options: {
      onSuccess: async (data: any) => {
        const bookingId = data?.bookingId;
        // success
        if (bookingId) {
          trackingPostTaskSuccess();
          resetState();
          setIsBookedTask(true);
          // Payment processing (includes navigation)
          await PaymentService.onPostTaskSuccess({
            bookingId,
            isPrepayment: data.isPrepayment,
          });
        }
      },
      onError: (error: IApiError) => {
        handlePostTaskError(error);
      },
      onMutate: () => {
        showLoading();
      },
      onSettled() {
        hideLoading();
      },
    },
  });

  const { mutate: checkTaskSameTime } = useApiMutation({
    key: EndpointKeys.checkTaskSameTime,
    options: {
      onMutate: () => {
        showLoading();
      },
      onSettled() {
        hideLoading();
      },
    },
  });

  const getDataPricing = () => {
    // Get the latest state directly from the store
    const currentState = usePostTaskStore.getState();
    const {
      address,
      date,
      duration,
      service,
      paymentMethod,
      washing,
      dryClean,
      others,
      otherText,
      city,
      promotion,
    } = currentState;

    if (!address || !date) {
      return null;
    }

    const timezone = DateTimeHelpers.getTimezoneByCity(address.city);
    const laundryData = {
      washing,
      dryClean,
      others,
      otherText,
      city,
    };

    // Validate laundry data before processing
    if (!validateLaundryData(laundryData)) {
      return null;
    }

    const detailLaundryData = buildDetailLaundryData(laundryData);

    // base info
    const task: any = {
      timezone,
      date: DateTimeHelpers.formatToString({ timezone, date }),
      autoChooseTasker: true,
      taskPlace: {
        country: address?.country,
        city: address?.city,
        district: address?.district,
      },
      homeType: address?.homeType,
      duration: duration,
      detailLaundry: detailLaundryData,
      // Add payment method and promotion using PostTaskHelpers
      ...PostTaskHelpers.formatDataToParams({ paymentMethod, promotion }),
    };

    return { task, service: { _id: service?._id }, isoCode };
  };

  const getPrice = debounce(async () => {
    // refactor data after call get price
    const data = getDataPricing();

    // data is null, no get price
    if (!data) {
      // set price is null --> hide price button.
      return setPrice(null);
    }

    // call get price API
    getPriceLaundry(data, {
      onSuccess: (result) => {
        setPrice(result);
        result?.duration && setDuration(result?.duration);
      },
      onError: (error) => {
        handleError(error);
        setPrice(null);
      },
    });
  }, 150);

  const postTask = async () => {
    // Set step tracking for final booking step (matches service-cleaning pattern)
    setStepPostTask(TrackingPostTaskStep.STEP_4);

    const currentState = usePostTaskStore.getState();
    const {
      address,
      date,
      duration,
      service,
      paymentMethod,
      isApplyNoteForAllTask,
      note,
      isFavouriteTasker,
      relatedTask,
      washing,
      dryClean,
      others,
      otherText,
      city,
      collectionDate,
      promotion,
    } = currentState;

    if (!address || !date || !service) {
      return;
    }

    const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);
    const dateString = DateTimeHelpers.formatToString({ date: date, timezone });
    const detailLaundryData = buildDetailLaundryData({
      washing,
      dryClean,
      others,
      otherText,
      city,
    });

    // base task info
    const task: any = {
      address: address.address,
      contactName: address.contact || user?.name,
      lat: address.lat,
      lng: address.lng,
      phone: address.phoneNumber || user?.phone,
      countryCode: address.countryCode || user?.countryCode,
      description: address.description,
      askerId: user?._id,
      autoChooseTasker: true,
      date: date ? DateTimeHelpers.formatToString({ date, timezone }) : '',
      duration,
      homeType: address.homeType,
      houseNumber: address.description,
      isoCode: isoCode,
      serviceId: service?._id,
      taskPlace: {
        country: address?.country,
        city: address?.city,
        district: address?.district,
      },
      shortAddress: address.shortAddress,
      isSendToFavTaskers: isFavouriteTasker,
      updateTaskNoteToUser: isApplyNoteForAllTask,
      detailLaundry: detailLaundryData,
      // Add payment method and promotion using PostTaskHelpers
      ...PostTaskHelpers.formatDataToParams({ paymentMethod, promotion }),
    };

    if (note && note?.trim()) {
      task.taskNote = note?.trim();
    }

    if (!isEmpty(relatedTask?.relatedTaskId)) {
      task.source = {
        from: relatedTask?.service?.name,
        taskId: relatedTask?.relatedTaskId,
      };
    }

    // Add collection date for laundry service
    if (collectionDate) {
      (task as any).collectionDate = DateTimeHelpers.formatToString({
        timezone,
        date: collectionDate,
      });
    }

    // check task same time
    checkTaskSameTime(
      {
        taskDate: dateString,
        serviceId: service?._id,
      },
      {
        onSuccess: (isExistTask) => {
          if (!isExistTask) {
            // call api book task
            postTaskLaundry(task);
            return;
          }

          // same time, alert for user
          Alert.alert.open({
            title: t('DIALOG_TITLE_INFORMATION'),
            message: t('TASK_SAME_TIME_MESSAGE'),
            actions: [
              { text: t('CLOSE'), style: 'cancel' },
              {
                text: t('OK'),
                onPress: async () => {
                  // wait modal close
                  setTimeout(async () => {
                    postTaskLaundry(task);
                  }, 300);
                },
              },
            ],
          });
        },
      },
    );
  };

  return { getPrice, postTask };
};
