import React, { useCallback, useEffect, useState } from 'react';
import {
  BlockView,
  checkSupportCity,
  Colors,
  ConditionView,
  CText,
  DateTimeHelpers,
  IAddress,
  IconAssets,
  IconImage,
  KeyboardAware,
  NotSupportCity,
  PostTaskHelpers,
  PriceButton,
  SERVICES,
  Spacing,
  TrackingPostTaskStep,
} from '@btaskee/design-system';
import { useIsFocused } from '@react-navigation/native';
import { find, get, isEmpty } from 'lodash-es';

import { WorkingProcess } from '@components';
import { useAppNavigation, useI18n, useTracking } from '@hooks';
import { RouteName } from '@navigation/RouteName';
import { usePostTaskStore } from '@stores';

import { DryCleaning } from './components/DryCleaning';
import { OtherService } from './components/OtherService';
import { WashingItem } from './components/WashingItem';
import { styles } from './styles';

// Types
interface ChooseServiceProps {
  previousServiceId?: string;
  resetStateSofaCleaning?: () => void;
}

// Components
const HeaderTitle: React.FC<{ address: IAddress }> = ({ address }) => (
  <BlockView
    flex
    row
    horizontal
    jBetween>
    <BlockView
      flex
      row>
      <IconImage
        source={IconAssets.icLocation}
        size={24}
        color={Colors.red500}
      />
      <BlockView
        flex
        margin={{ left: Spacing.SPACE_08 }}>
        <CText>{address?.shortAddress}</CText>
        <CText
          bold
          numberOfLines={1}
          margin={{ right: Spacing.SPACE_16 }}>
          {address?.address}
        </CText>
      </BlockView>
    </BlockView>
  </BlockView>
);

const laundryTypes = {
  WASHING: 'washing',
  DRY_CLEAN: 'dryClean',
  OTHER: 'other',
};

// Main Component
export const ChooseService: React.FC<ChooseServiceProps> = ({ route }) => {
  const entryPoint = route?.params?.entryPoint;
  const navigation = useAppNavigation();
  const { t } = useI18n();
  const {
    price,
    address,
    service,
    date,
    setDateTime,
    setCollectionDate,
    setStepPostTask,
    // Laundry-specific state
    washing,
    dryClean,
    others,
    otherText,
  } = usePostTaskStore();
  const isFocused = useIsFocused();

  const {
    trackingChooseServiceNextStep,
    trackingChooseServiceScreenView,
    trackingBackChooseService,
  } = useTracking();
  const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);
  const [dataDetail, setDataDetail] = useState([]);

  // Handlers
  const handleConfirm = useCallback(() => {
    // Set step tracking for service selection (Step 2 → Step 3)
    setStepPostTask(TrackingPostTaskStep.STEP_2);

    // Track service selection next step
    trackingChooseServiceNextStep();

    navigation.navigate(RouteName.ChooseDateTime);
  }, []);

  // check city change, set laundry data again
  React.useEffect(() => {
    _setLaundryDetail();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [address?.city]);

  const _setLaundryDetail = () => {
    const listSupportCity = get(service, 'detailLaundry.city', []);
    const cityFromAddress = get(address, 'city', null);
    const dataType = find(listSupportCity, { name: cityFromAddress });
    if (dataType) {
      const dataDetailLaundry = get(dataType, 'detail', []);
      setDataDetail(dataDetailLaundry);
    }
  };

  // Effects
  useEffect(() => {
    navigation.setOptions({
      headerTitle: () => <HeaderTitle address={address} />,
    });
  }, [navigation, address]);

  const handleTrackingBack = useCallback(() => {
    if (!isFocused) {
      return null;
    }
    trackingBackChooseService();
  }, [isFocused]);

  useEffect(() => {
    const unsubscribe = navigation.addListener('beforeRemove', () => {
      handleTrackingBack();
    });

    return unsubscribe; // Cleanup listener khi component unmount
  }, []);

  // Screen view tracking
  useEffect(() => {
    trackingChooseServiceScreenView(entryPoint);
  }, [entryPoint]);

  useEffect(() => {
    // Initialize default date time if not set
    if (!date) {
      const defaultDateTime = PostTaskHelpers.getDefaultDateTime(
        {
          serviceName: SERVICES.LAUNDRY,
          defaultTaskTime: service?.defaultTaskTime,
        },
        service?.defaultTaskTime,
        address?.city,
      );
      setDateTime(defaultDateTime);
      setCollectionDate(
        DateTimeHelpers.toDateTz({ timezone }).add(1, 'day').toDate(),
      );
    }
  }, [
    date,
    setDateTime,
    service?.defaultTaskTime,
    address?.city,
    setCollectionDate,
    timezone,
  ]);

  // Check city support
  if (!checkSupportCity(service?.city, address?.city)) {
    return <NotSupportCity />;
  }

  return (
    <BlockView style={styles.container}>
      <KeyboardAware
        keyboardShouldPersistTaps="handled"
        testID="scrollChooseService"
        showsVerticalScrollIndicator={false}>
        <BlockView style={styles.containerContent}>
          {dataDetail.map((laundryDetail: any, index: number) => {
            if (laundryDetail.type === laundryTypes.WASHING) {
              return (
                <WashingItem
                  key={`washing_${index}`}
                  laundryDetail={laundryDetail}
                  washing={washing}
                  type={laundryTypes.WASHING}
                />
              );
            }

            if (laundryDetail.type === laundryTypes.DRY_CLEAN) {
              return (
                <DryCleaning
                  key={`dry_${index}`}
                  dryClean={dryClean}
                  dryCleaningDetail={laundryDetail}
                />
              );
            }
            if (laundryDetail.type === laundryTypes.OTHER) {
              return (
                <OtherService
                  key={`other_${index}`}
                  laundryDetail={laundryDetail}
                  others={others}
                  type={laundryTypes.OTHER}
                  otherText={otherText}
                />
              );
            }
          })}

          <CText style={styles.noteText}>
            {t('LAUNDRY_COMPENSATION_TEXT')}
          </CText>
          <ConditionView
            condition={!isEmpty(service?.workingProcessV2?.detail)}
            viewTrue={
              <WorkingProcess
                workingProcess={service?.workingProcessV2?.detail || []}
              />
            }
          />
        </BlockView>
      </KeyboardAware>
      <PriceButton
        pricePostTask={price}
        testID="btnNextStep2"
        onPress={handleConfirm}
        fromScreen={service?.name}
      />
    </BlockView>
  );
};
