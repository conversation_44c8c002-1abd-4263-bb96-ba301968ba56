import React from 'react';
import {
  ApiType,
  DateT<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>per,
  EndpointKeys,
  handleError,
  IApiError,
  IDate,
  PaymentService,
  PostTaskHelpers,
  useApiMutation,
  useAppLoadingStore,
  useAppStore,
  usePostTaskAction,
  useUserStore,
} from '@btaskee/design-system';
import { debounce, isEmpty } from 'lodash-es';

import { usePostTaskStore } from '@stores';

import { useTracking } from './useTracking';

export const usePostTask = () => {
  const { isoCode } = useAppStore();
  const { user } = useUserStore();
  const { showLoading, hideLoading } = useAppLoadingStore();
  const { handlePostTaskError } = usePostTaskAction();

  const { trackingSubscriptionSuccess } = useTracking();

  const { service, setPrice, setLoadingPrice, resetState, setIsBookedTask } =
    usePostTaskStore();
  const { mutate: getPriceCleaningSubscription } =
    useApiMutation<EndpointKeys.getPriceCleaningSubscription>({
      key: EndpointKeys.getPriceCleaningSubscription,
      options: {
        onMutate: () => {
          setLoadingPrice(true);
          showLoading();
        },
        onSettled: () => {
          setLoadingPrice(false);
          hideLoading();
        },
      },
    });
  const { mutate: postTaskSubscriptionCleaning } =
    useApiMutation<EndpointKeys.postTaskSubscriptionCleaning>({
      key: EndpointKeys.postTaskSubscriptionCleaning,
      options: {
        onSuccess: async (data) => {
          const paymentMethod = usePostTaskStore.getState().paymentMethod;

          trackingSubscriptionSuccess();

          resetState();
          setIsBookedTask(true);

          await PaymentService.onPostTaskSubscriptionSuccess({
            paymentMethod,
            data: data?.data,
          });
        },
        onError: (error: IApiError) => {
          handlePostTaskError(error);
        },
        onMutate: () => {
          showLoading();
        },
        onSettled() {
          hideLoading();
        },
      },
    });

  const getDataPricing = async () => {
    // Get the latest state directly from the store
    const currentState = usePostTaskStore.getState();
    const {
      address,
      isPremium,
      duration,
      addons,
      schedule,
      month,
      promotion,
      isEco,
      paymentMethod,
    } = currentState;

    if (isEmpty(schedule) || !duration || !month) {
      return null;
    }
    const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

    const schedules = schedule.map((e) =>
      DateTimeHelpers.formatToString({ timezone, date: e }),
    );
    const params: ApiType[EndpointKeys.getPriceCleaningSubscription]['params'] =
      {
        schedule: schedules,
        timezone,
        service: {
          _id: service?._id || '',
        },
        task: {
          taskPlace: {
            country: address?.country,
            city: address?.city,
            district: address?.district,
          },
          duration: duration,
          autoChooseTasker: true,
          homeType: address?.homeType,
        },
        month,
        isoCode: isoCode || '',
        ...PostTaskHelpers.formatDataToParams({ paymentMethod, promotion }),
      };

    if (isPremium) {
      params.task.isPremium = Boolean(isPremium);
    }

    if (isEco) {
      params.task.isEco = Boolean(isEco);
    }

    if (!isEmpty(addons)) {
      params.task.addons = addons;
    }

    return params;
  };

  const getPrice = React.useMemo(
    () =>
      debounce(async () => {
        // refactor data after call get price
        const data = await getDataPricing();

        // data is null, no get price
        if (!data) {
          // set price is nul --> hide price button.
          return setPrice(null);
        }

        // call get price API
        getPriceCleaningSubscription(data, {
          onSuccess: (result) => {
            setPrice(result);
          },
          onError: (error) => {
            handleError(error);
            setPrice(null);
          },
        });
      }, 150),
    [getPriceCleaningSubscription, setPrice],
  );

  const _refactorDataPostTask = () => {
    const currentState = usePostTaskStore.getState();
    const {
      address,
      homeNumber,
      promotion,
      startDate,
      endDate,
      weekdays,
      duration,
      paymentMethod,
      note,
      schedule,
      month,
      isPremium,
      isEco,
      addons,
      pet,
    } = currentState;
    const isAddressMaybeWrong = Boolean(address?.isAddressMaybeWrong);
    const city = address?.city;
    const timezone = DateTimeHelpers.getTimezoneByCity(city);

    // base task info
    const task = {
      startDate: DateTimeHelpers.formatToString({
        date: startDate as IDate,
        timezone,
      }),
      endDate: DateTimeHelpers.formatToString({
        date: endDate as IDate,
        timezone,
      }),
      timezone,
      taskPlace: {
        country: address?.country,
        city: address?.city,
        district: address?.district,
      },
      weekday: weekdays,
      duration: duration,
      serviceId: service?._id,
      homeType: address?.homeType,
      address: address?.address,
      contactName: address?.contact,
      location: {
        lat: address?.lat,
        lng: address?.lng,
      },
      countryCode: address?.countryCode || user?.countryCode,
      description: homeNumber,
      deviceInfo: DeviceHelper.getDeviceInfo(),
      houseNumber: homeNumber,
      taskNote: note?.trim(),
      isoCode: isoCode || '',
      shortAddress: address?.shortAddress,
      month: month,
      ...PostTaskHelpers.formatDataToParams({ paymentMethod, promotion }),
    };

    if (schedule && schedule.length > 0) {
      task.schedule = schedule.map((e) =>
        DateTimeHelpers.formatToString({ date: e, timezone }),
      );
    }

    // Địa chỉ có thể bị sai => khi book task, send to slack cho team vận hành xử lý
    if (isAddressMaybeWrong) {
      task.taskPlace.isAddressMaybeWrong = isAddressMaybeWrong;
    }

    // Check premium option
    if (isPremium) {
      task.isPremium = true;
    }

    // Check isEco option
    if (isEco) {
      task.isEco = true;
    }

    // Check isEco option
    if (!isEmpty(addons)) {
      task.addons = addons;
    }

    // Check isEco option
    if (pet) {
      task.pet = pet;
    }

    return task;
  };

  /**
   * @description function booking task
   * @param payload {{object}} data of task
   */
  const postTask = async () => {
    const dataTask = _refactorDataPostTask();

    // call post task
    postTaskSubscriptionCleaning(dataTask);
  };

  return { getPrice, postTask };
};
