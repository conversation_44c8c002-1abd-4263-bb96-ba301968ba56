import {
  IAdd<PERSON>,
  IAddress,
  IDate,
  IPaymentMethodInfo,
  IPriceSub,
  IPromotion,
  IService,
  Maybe,
  Requirement,
  TrackingPostTaskStep,
} from '@btaskee/design-system';
import { create } from 'zustand';

interface IState {
  address: IAddress;
  duration: number;
  requirements: Requirement[];
  isPremium: boolean;
  isAutoChooseTasker: boolean;
  isFavouriteTasker: boolean;
  gender: string;
  pet: any;
  addons: IAddons[];
  date?: IDate;
  schedule: string[];
  isEnabledSchedule: boolean;
  note: string;
  isApplyNoteForAllTask: boolean;
  price: IPriceSub | null;
  service: Maybe<IService> | null;
  paymentMethod?: IPaymentMethodInfo;
  promotion?: IPromotion;
  isLoadingPrice: boolean;
  loadingPostTask: boolean;
  homeNumber: string;
  isEco: boolean;
  month: number;
  startDate: IDate | null;
  endDate: IDate | null;
  weekdays: number[];
  // Tracking properties
  isBookedTask: boolean;
  stepPostTask: string;
}

interface IActions {
  setAddress: (address: IAddress) => void;
  setDuration: (duration: number) => void;
  setRequirements: (requirements: Requirement[]) => void;
  setIsPremium: (isPremium: boolean) => void;
  setIsAutoChooseTasker: (isAutoChooseTasker: boolean) => void;
  setGender: (gender: string) => void;
  setAddons: (addons: IAddons[]) => void;
  setPet: (pet: any) => void;
  setDateTime: (date: IDate) => void;
  setSchedule: (schedule?: string[]) => void;
  setNote: (note: string) => void;
  setIsApplyNoteForAllTask: (isApplyNoteForAllTask: boolean) => void;
  setPrice: (price: Maybe<IPriceSub>) => void;
  setHomeNumber: (homeNumber: string) => void;
  setPaymentMethod: (paymentMethod?: IPaymentMethodInfo) => void;
  setPromotion: (promotion?: IPromotion) => void;
  setLoadingPrice: (isLoadingPrice: boolean) => void;
  setService: (service: Maybe<IService>) => void;
  setLoadingPostTask: (loadingPostTask: boolean) => void;
  setMonth: (month: number) => void;
  setStartDate: (startDate: IDate) => void;
  setEndDate: (endDate: IDate) => void;
  setWeekdays: (weekdays?: number[]) => void;
  setIsEco: (isEco: boolean) => void;
  resetState: () => void;
  // Tracking methods
  setIsBookedTask: (isBookedTask: boolean) => void;
  setStepPostTask: (stepPostTask: string) => void;
}

const INITIAL_STATE: IState = {
  address: {},
  duration: 0,
  requirements: [],
  isPremium: false,
  isAutoChooseTasker: true,
  isFavouriteTasker: false,
  gender: '',
  pet: '',
  addons: [],
  date: undefined,
  schedule: [],
  isEnabledSchedule: false,
  note: '',
  isApplyNoteForAllTask: false,
  homeNumber: '',
  price: null,
  service: null,
  paymentMethod: undefined,
  promotion: undefined,
  isLoadingPrice: false,
  loadingPostTask: false,
  isEco: false,
  month: 0,
  startDate: null,
  endDate: null,
  weekdays: [],
  // Tracking state
  isBookedTask: false,
  stepPostTask: TrackingPostTaskStep.ChooseDuration,
};

type PostTaskState = IState & IActions;

export const usePostTaskStore = create<PostTaskState>((set) => ({
  ...INITIAL_STATE,
  setLoadingPostTask: (loadingPostTask: boolean) =>
    set({ loadingPostTask: loadingPostTask }),
  setAddress: (address: IAddress) => set({ address: address }),
  setDuration: (duration: number) => set({ duration: duration }),
  setRequirements: (requirements: Requirement[]) =>
    set({ requirements: requirements }),
  setIsPremium: (isPremium: boolean) => set({ isPremium: isPremium }),
  setIsAutoChooseTasker: (isAutoChooseTasker: boolean) =>
    set({ isAutoChooseTasker: isAutoChooseTasker }),
  setGender: (gender: string) => set({ gender: gender }),
  setAddons: (addons: IAddons[]) => set({ addons: addons }),
  setPet: (pet: any) => set({ pet: pet }),
  setDateTime: (date: IDate) => set({ date: date }),
  setSchedule: (schedule?: string[]) => set({ schedule: schedule || [] }),
  setNote: (note: string) => set({ note: note }),
  setIsApplyNoteForAllTask: (isApplyNoteForAllTask: boolean) =>
    set({ isApplyNoteForAllTask: isApplyNoteForAllTask }),
  setPrice: (price: Maybe<IPriceSub>) => set({ price: price }),
  setHomeNumber: (homeNumber: string) => set({ homeNumber: homeNumber }),
  setPaymentMethod: (paymentMethod) => set({ paymentMethod: paymentMethod }),
  setPromotion: (promotion) => set({ promotion: promotion }),
  setLoadingPrice: (isLoadingPrice: boolean) =>
    set({ isLoadingPrice: isLoadingPrice }),
  setService: (service: Maybe<IService>) => set({ service: service }),
  setMonth: (month: number) => set({ month: month }),
  setStartDate: (startDate: IDate) => set({ startDate: startDate }),
  setEndDate: (endDate: IDate) => set({ endDate: endDate }),
  setWeekdays: (weekdays?: number[]) => set({ weekdays: weekdays }),
  setIsEco: (isEco: boolean) => set({ isEco: isEco }),
  // Tracking methods
  setIsBookedTask: (isBookedTask: boolean) =>
    set({ isBookedTask: isBookedTask }),
  setStepPostTask: (stepPostTask: string) =>
    set({ stepPostTask: stepPostTask }),
  resetState: () => set(INITIAL_STATE),
}));
