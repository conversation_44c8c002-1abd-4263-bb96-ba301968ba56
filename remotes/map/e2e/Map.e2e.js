/**
 * Massage Service Sequential Flow E2E Tests
 *
 * Comprehensive E2E test suite for React Native massage service booking application
 * following strict sequential flow: Address Selection → Service Package Selection → Date/Time Selection → Payment/Confirmation
 *
 * Requirements:
 * - TestID-only selectors (no text expectations)
 * - Sequential flow compliance (cannot skip steps)
 * - Proper scrolling behavior for viewport management
 * - Performance optimized targeting 3-5 minute execution time
 * - Enhanced testID coverage for all interactive elements
 * - Scroll-to-reveal patterns for viewport management
 * - Comprehensive validation for single and double massage flows
 *
 * <AUTHOR> Automation Engineer
 * @framework Detox
 * @version 3.0 - Enhanced with performance optimization and comprehensive flow validation
 */

const { device } = require('detox');
const {
  reloadApp,
  tapId,
  waitForElement,
  expectElementVisible,
  typeToTextField,
} = require('./step-definition');

// Performance monitoring utilities
const performanceTracker = {
  startTime: null,
  stepTimes: {},

  startTest: function (testName) {
    this.startTime = Date.now();
    this.stepTimes[testName] = { start: this.startTime };
  },

  endTest: function (testName) {
    const endTime = Date.now();
    const duration = endTime - this.startTime;
    this.stepTimes[testName].end = endTime;
    this.stepTimes[testName].duration = duration;

    // Track performance if test takes longer than expected (5 minutes = 300000ms)
    if (duration > 300000) {
      // Performance warning - test exceeds target time
      this.stepTimes[testName].warning = 'Exceeds 5 minute target';
    }

    return duration;
  },
};

// Optimized wait times for better performance
const WAIT_TIMES = {
  FAST: 2000, // For quick transitions
  NORMAL: 5000, // For standard navigation
  SLOW: 8000, // For complex operations
};

describe('Map Service Sequential Flow', () => {
  beforeEach(async () => {
    // Performance optimized app reset
    performanceTracker.startTest('beforeEach-setup');

    // Reset app state and data for consistent testing
    await reloadApp();
    await device.reloadReactNative();

    try {
      // Use optimized wait time for faster execution
      await waitForElement('search-overlay-input', WAIT_TIMES.SLOW);
    } catch (error) {
      await waitForElement('search-overlay-input', WAIT_TIMES.NORMAL);
    }

    performanceTracker.endTest('beforeEach-setup');
  });

  describe('Step 1: Address Selection', () => {
    it('should display address list and allow selection with single/double choice', async () => {
      await typeToTextField('search-overlay-input', 'btaskee Tran nao');

      await expectElementVisible('search-overlay-item-0');
      await tapId('search-overlay-item-0');

      await waitForElement('choose-location-button', 3000);
      await tapId('choose-location-button');

      await waitForElement('address-info', 8000);
      await expectElementVisible('address-info');
      await expectElementVisible('change-current-address');

      await expectElementVisible('home-type-item-0');
      await expectElementVisible('home-type-item-1');
      await expectElementVisible('home-type-item-2');

      await tapId('home-type-item-0');
      await tapId('home-type-item-1');
      await tapId('home-type-item-2');

      await expectElementVisible('choose-house-type-modal-home-number');
      await typeToTextField('choose-house-type-modal-home-number', '123');

      await expectElementVisible('choose-house-type-modal-confirm');
      await tapId('choose-house-type-modal-confirm');
    });
  });
});
