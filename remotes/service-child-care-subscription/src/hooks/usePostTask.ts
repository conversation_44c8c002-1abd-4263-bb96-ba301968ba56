import {
  DateT<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>elper,
  Endpoint<PERSON>eys,
  handleError,
  IApiError,
  IDate,
  IPriceSub,
  PaymentService,
  PostTaskHelpers,
  useApiMutation,
  useAppLoadingStore,
  useAppStore,
  usePostTaskAction,
  useUserStore,
} from '@btaskee/design-system';
import { isEmpty } from 'lodash-es';

import { usePostTaskStore } from '@stores';

import { useTracking } from './useTracking';

// Utility functions for building task data
const buildDetailChildCareData = (childCareData: {
  numberOfChildren: number;
  detailChildren: any[];
}) => {
  const { numberOfChildren, detailChildren } = childCareData;
  return {
    numberOfChildren,
    detailChildren,
  };
};

export const usePostTask = () => {
  const { isoCode } = useAppStore();
  const { user } = useUserStore();
  const { showLoading, hideLoading } = useAppLoadingStore();
  const { trackingSubscriptionSuccess, trackingSubscriptionBooked } =
    useTracking();
  const { handlePostTaskError } = usePostTaskAction();

  const { service, setPrice, setLoadingPrice, resetState, setIsBookedTask } =
    usePostTaskStore();

  const { mutate: getPriceChildCareSubscription } = useApiMutation({
    key: EndpointKeys.getPriceChildCareSubscription,
    options: {
      onMutate: () => {
        setLoadingPrice(true);
        showLoading();
      },
      onSettled: () => {
        setLoadingPrice(false);
        hideLoading();
      },
    },
  });

  const { mutate: postTaskSubscriptionCleaning } = useApiMutation({
    key: EndpointKeys.postTaskSubscriptionCleaning,
    options: {
      onSuccess: async (data) => {
        const paymentMethod = usePostTaskStore.getState().paymentMethod;
        resetState();

        // Track subscription success events
        trackingSubscriptionSuccess();
        trackingSubscriptionBooked();

        setIsBookedTask(true);

        await PaymentService.onPostTaskSubscriptionSuccess({
          paymentMethod,
          data: data?.data,
        });
      },
      onError: (error: IApiError) => {
        handlePostTaskError(error);
      },
      onMutate: () => {
        showLoading();
      },
      onSettled() {
        hideLoading();
      },
    },
  });

  const buildPricingData = () => {
    const currentState = usePostTaskStore.getState();
    const {
      address,
      duration,
      addons,
      schedule,
      month,
      promotion,
      paymentMethod,
      detailChildren,
      numberOfChildren,
    } = currentState;

    if (isEmpty(schedule) || !duration || !month) {
      return null;
    }

    // Check data before get price, no detail children -> no call api get price
    if (
      detailChildren?.length === 0 ||
      (numberOfChildren === 2 && detailChildren?.length === 1)
    ) {
      return null;
    }

    const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);
    const schedules = schedule.map((e: any) =>
      DateTimeHelpers.formatToString({ timezone, date: e }),
    );

    const params: any = {
      schedule: schedules,
      timezone,
      service: {
        _id: service?._id || '',
      },
      task: {
        taskPlace: {
          country: address?.country,
          city: address?.city,
          district: address?.district,
        },
        duration,
        autoChooseTasker: true,
        homeType: address?.homeType,
        detailChildCare: buildDetailChildCareData({
          numberOfChildren,
          detailChildren,
        }),
      },
      month,
      isoCode: isoCode || '',
      ...PostTaskHelpers.formatDataToParams({ paymentMethod, promotion }),
    };

    if (!isEmpty(addons)) {
      params.task.addons = addons;
    }

    return params;
  };

  const getPrice = async (): Promise<void> => {
    const pricingData = buildPricingData();

    if (!pricingData) {
      setPrice(null);
      return;
    }

    setLoadingPrice(true);

    await getPriceChildCareSubscription(pricingData, {
      onSuccess: (result: any) => {
        setPrice(result as IPriceSub);
      },
      onError: (error: IApiError) => {
        handleError(error);
        setPrice(null);
      },
    });

    setLoadingPrice(false);
  };

  const buildTaskData = () => {
    const currentState = usePostTaskStore.getState();
    const {
      address,
      homeNumber,
      promotion,
      startDate,
      endDate,
      weekdays,
      duration,
      paymentMethod,
      note,
      schedule,
      month,
      addons,
      detailChildren,
      numberOfChildren,
    } = currentState;

    const isAddressMaybeWrong = Boolean(address?.isAddressMaybeWrong);
    const city = address?.city;
    const timezone = DateTimeHelpers.getTimezoneByCity(city);

    const task: any = {
      startDate: DateTimeHelpers.formatToString({
        date: startDate as IDate,
        timezone,
      }),
      endDate: DateTimeHelpers.formatToString({
        date: endDate as IDate,
        timezone,
      }),
      timezone,
      taskPlace: {
        country: address?.country || '',
        city: address?.city || '',
        district: address?.district || '',
      },
      weekday: weekdays || [],
      duration: duration || 0,
      serviceId: service?._id || '',
      homeType: address?.homeType || '',
      address: address?.address || '',
      contactName: address?.contact || '',
      location: {
        lat: address?.lat || 0,
        lng: address?.lng || 0,
      },
      countryCode: address?.countryCode || user?.countryCode || '',
      description: homeNumber || '',
      deviceInfo: DeviceHelper.getDeviceInfo(),
      houseNumber: homeNumber || '',
      isoCode: isoCode || '',
      shortAddress: address?.shortAddress || '',
      month: month || 0,
      detailChildCare: buildDetailChildCareData({
        numberOfChildren: numberOfChildren || 0,
        detailChildren: detailChildren || [],
      }),
      ...PostTaskHelpers.formatDataToParams({ paymentMethod, promotion }),
    };

    // Add optional fields
    if (schedule && schedule.length > 0) {
      task.schedule = schedule.map((e: any) =>
        DateTimeHelpers.formatToString({ date: e, timezone }),
      );
    }

    if (isAddressMaybeWrong) {
      task.taskPlace.isAddressMaybeWrong = isAddressMaybeWrong;
    }

    if (note?.trim()) {
      task.taskNote = note.trim();
    }

    if (!isEmpty(addons)) {
      task.addons = addons;
    }

    return task;
  };

  const executeTaskPosting = async (): Promise<any> => {
    const taskData = buildTaskData();
    return postTaskSubscriptionCleaning(taskData);
  };

  const postTask = async (callback?: () => void): Promise<any> => {
    const result = await executeTaskPosting();

    if (result?.data?.bookingId) {
      callback?.();
    }

    return result;
  };

  return { getPrice, postTask };
};
