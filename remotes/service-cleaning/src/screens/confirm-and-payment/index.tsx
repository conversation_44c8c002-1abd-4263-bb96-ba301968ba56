/**
 * ConfirmBooking Screen Component
 *
 * This screen allows users to review and confirm their cleaning service booking details,
 * including location, task details, payment method, and pricing information.
 * It handles regular and special (Tet holiday) booking flows with appropriate date selection options.
 */
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  Alert,
  BlockView,
  BookingButton,
  CModal,
  CModalHandle,
  Colors,
  ConditionView,
  CText,
  DatePicker,
  DateTimeHelpers,
  <PERSON><PERSON>Helper,
  LocationPostTask,
  PaymentDetailStep4WithDateOptions,
  PaymentMethodBlock,
  ScrollView,
  SERVICES,
  TimePicker,
  TouchableOpacity,
  TrackingActions,
  TrackingScreenNames,
  TrackingServices,
  TYPE_OF_PAYMENT,
  TypeFormatDate,
  usePostTaskAction,
  useSettingsStore,
} from '@btaskee/design-system';
import { usePostTaskStore } from '@store';
import { debounce, isEmpty } from 'lodash-es';

import {
  OptionBookingWithFavTasker,
  TaskDetail,
  TermsOfUseServices,
} from '@components';
import { useAppNavigation, useI18n, usePostTask, useTracking } from '@hooks';

import styles from './styles';

const RATIO_PADDING_BOTTOM = 0.05;

/**
 * Props for the ConfirmBooking component
 */
interface ConfirmBookingProps {
  navigation: {
    navigate: (screen: string, params?: any) => void;
    popToTop: () => void;
  };
}

/**
 * ConfirmBooking component for reviewing and confirming cleaning service bookings
 * @param props - Component props
 */
export const ConfirmBooking: React.FC<ConfirmBookingProps> = () => {
  const { t } = useI18n();
  const {
    address,
    price,
    setDateTime,
    setAddress,
    date,
    service,
    paymentMethod,
    promotion,
    isBookedTask,
    forceTasker,
    setPromotion,
    setPaymentMethod,
  } = usePostTaskStore();
  const { getPrice, postTask } = usePostTask();
  const {
    trackingConfirmPaymentScreenView,
    trackingNextBackActionConfirmPayment,
  } = useTracking();
  const settings = useSettingsStore().settings;
  const navigation = useAppNavigation();

  const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

  const [isTet, setIsTet] = useState(service?.isTet);
  const [selectedDate, setSelectedDate] = useState(date);
  const modalRef = useRef<CModalHandle>(null);
  const { getPaymentMethodWhenBooking } = usePostTaskAction();

  useEffect(() => {
    trackingConfirmPaymentScreenView();
  }, []);

  const handleTrackingBack = useCallback(() => {
    if (isBookedTask) {
      return null;
    }
    trackingNextBackActionConfirmPayment({
      action: TrackingActions.Back,
    });
  }, [isBookedTask, trackingNextBackActionConfirmPayment]);

  useEffect(() => {
    const unsubscribe = navigation.addListener('beforeRemove', () => {
      handleTrackingBack();
    });

    return unsubscribe; // Cleanup listener khi component unmount
  }, [navigation, handleTrackingBack]);

  useEffect(() => {
    const paymentMethodDefault = getPaymentMethodWhenBooking({
      promotion,
      price: price,
      paymentMethod: paymentMethod,
    });
    if (!isEmpty(paymentMethodDefault)) {
      setPaymentMethod(paymentMethodDefault);
    }
  }, []);

  /**
   * Removes Tet holiday flag from service and updates state
   */
  const removeTetInService = useCallback(() => {
    if (service) {
      // Clear isTet in service
      const updatedService = { ...service };
      delete updatedService.isTet;
      // setService(service);
      setIsTet(false);
      // setPaymentMethodWhenBooking();
    }
  }, [service]);

  /**
   * Handles switching from Tet holiday booking to regular booking
   */
  const _changeToRegularBooking = useCallback(() => {
    TrackingServices.trackingServiceClick({
      // campaignName: configSpecialPreBooking?.name,
      screenName: TrackingScreenNames.ConfirmPayment,
      serviceName: SERVICES.CLEANING,
      action: TrackingActions.SwitchToRegularBooking,
    });

    if (!timezone) return;

    // Require change date if the chosen date is over regular range
    const maxDate = DateTimeHelpers.toDayTz({ timezone })
      .add(6, 'day')
      .endOf('date');
    const isAfter = date
      ? DateTimeHelpers.checkIsAfter({
          timezone,
          firstDate: date,
          secondDate: maxDate,
        })
      : false;

    if (isAfter) {
      // Set default date is 2PM tomorrow
      const tomorrow = DateTimeHelpers.toDayTz({ timezone })
        .add(1, 'day')
        .hour(14)
        .startOf('hour');
      setSelectedDate(tomorrow.toDate());

      // Show change new date modal
      modalRef?.current?.open && modalRef.current.open();
    } else {
      removeTetInService();
    }
  }, [timezone, date, removeTetInService]);

  /**
   * Updates date time when user changes it
   * @param dateObj - The new date object selected by user
   */
  const onChangeDateTime = useCallback(
    (dateObj: Date) => {
      // check spam, call api with same data
      if (!timezone || !date || !dateObj) return null;

      const isSame = DateTimeHelpers.checkIsSame({
        timezone,
        firstDate: date,
        secondDate: dateObj,
      });

      if (isSame) return null;

      setSelectedDate(dateObj);
    },
    [timezone, date],
  );

  /**
   * Applies the new date selection and recalculates price
   */
  const _changeNewDate = useCallback(async () => {
    TrackingServices.trackingServiceClick({
      // campaignName: configSpecialPreBooking?.name,
      screenName: TrackingScreenNames.UpdateDataTime,
      serviceName: SERVICES.CLEANING,
      action: TrackingActions.Update,
      additionalInfo: {
        workingTime: {
          date: DateTimeHelpers.formatToString({
            date,
            timezone,
            typeFormat: TypeFormatDate.DateShort,
          }),
          time: DateTimeHelpers.formatToString({
            date,
            timezone,
            typeFormat: TypeFormatDate.TimeHourMinute,
          }),
        },
      },
    });

    if (!service || !selectedDate) return;

    setDateTime(selectedDate);
    // recalculate duration and estimated time, only Home Cooking service
    getPrice();
    removeTetInService();
  }, [selectedDate, service, setDateTime, getPrice, removeTetInService]);

  useEffect(() => {
    getPrice();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [promotion?.code]);

  /**
   * Handles modal close event for regular booking change
   */
  const onCloseModalChangeToRegularBooking = useCallback(() => {
    TrackingServices.trackingServiceClick({
      // campaignName: configSpecialPreBooking?.name,
      screenName: TrackingScreenNames.UpdateDataTime,
      serviceName: SERVICES.CLEANING,
      action: TrackingActions.Close,
    });
  }, []);

  /**
   * Shows options for booking with favorite tasker
   */
  const showOptionBookingWithFavTasker = useMemo(
    () =>
      debounce(() => {
        Alert.alert?.open?.({
          title: 'FAV_TASKER.ADD_OPTION',
          message: <OptionBookingWithFavTasker />,
          actions: [
            {
              text: 'CONTINUE',
              style: 'cancel',
              onPress: () => {
                TrackingServices.trackingButtonClick({
                  screenName: TrackingScreenNames.AddOption,
                  featureName: 'BookWithFavTasker',
                  action: TrackingActions.Next,
                  additionalInfo: {
                    ResendForAnotherTasker: Boolean(forceTasker?.isResent),
                  },
                });
                postTask();
              },
            },
          ],
          backgroundContainer: Colors.neutralBackground2,
        });
      }, 400),
    [postTask],
  );

  /**
   * Initiates the task posting process
   */
  const _onPosTask = useCallback(async () => {
    trackingNextBackActionConfirmPayment({
      action: TrackingActions.Next,
    });

    if (!isEmpty(forceTasker)) {
      TrackingServices.trackingScreenView({
        screenName: 'AddOption',
        featureName: 'BookWithFavTasker',
        entryPoint: TrackingScreenNames.ConfirmPayment,
        taskerID: forceTasker?._id,
      });

      Alert.alert.close();
      return showOptionBookingWithFavTasker();
    }
    return postTask();
  }, [forceTasker, postTask, showOptionBookingWithFavTasker]);

  // Determine if date comparison is possible for the modal action button
  const isDateComparisonPossible = useMemo(
    () => Boolean(timezone && date && selectedDate),
    [timezone, date, selectedDate],
  );

  // Check if dates are the same for disabling the modal action button
  const areDatesTheSame = useMemo(() => {
    if (!isDateComparisonPossible) return false;

    return DateTimeHelpers.checkIsSame({
      timezone: timezone,
      firstDate: date,
      secondDate: selectedDate,
    });
  }, [isDateComparisonPossible, timezone, date, selectedDate]);

  return (
    <BlockView style={styles.container}>
      <ScrollView
        scrollIndicatorInsets={{ right: 1 }}
        testID="scrollViewStep4"
        contentContainerStyle={styles.content}
      >
        <LocationPostTask
          address={address}
          homeNumber={address?.description}
          setAddress={setAddress}
        />

        <TaskDetail />

        <PaymentDetailStep4WithDateOptions
          dateOptions={price?.dateOptions}
          timezone={timezone}
          paymentMethod={paymentMethod}
        />

        <PaymentMethodBlock
          serviceName={service?.name}
          type={TYPE_OF_PAYMENT.bookTask}
          promotionOptions={{
            currentPromotion: promotion,
            taskCost: price?.cost,
            taskDate: date,
            taskPlace: {
              country: address?.country,
              city: address?.city,
              district: address?.district,
            },
          }}
          paymentMethodOptions={{
            currentPaymentMethod: paymentMethod,
          }}
          onChangePromotion={setPromotion}
          onChangePaymentMethod={setPaymentMethod}
        />
        <ConditionView
          condition={Boolean(isTet)}
          viewTrue={
            <TouchableOpacity
              style={styles.wrapChangeToRegularBooking}
              onPress={_changeToRegularBooking}
            >
              <CText
                center
                bold
                style={styles.txtChangeToRegularBooking}
              >
                {t('TET_BOOKING_TO_NORMAL_TASK')}
              </CText>
              <CText
                center
                style={styles.txtChangeToRegularBookingDescription}
              >
                {t('TET_BOOKING_TO_NORMAL_TASK_DESCRIPTION')}
              </CText>
            </TouchableOpacity>
          }
        />
        <TermsOfUseServices />
      </ScrollView>
      <BookingButton
        testID="btnSubmitPostTask"
        onPostTask={_onPosTask}
        price={price}
      />
      {isTet ? ( // Change Tet booking to regular booking
        <CModal
          contentContainerStyle={{
            paddingBottom: DeviceHelper.WINDOW.HEIGHT * RATIO_PADDING_BOTTOM,
          }}
          hideButtonClose
          ref={modalRef}
          title={t('TET_BOOKING_TO_NOMAL_NOTE_TITLE')}
          closeModalAction={onCloseModalChangeToRegularBooking}
          actions={[
            {
              text: t('TET_BOOKING_TO_NOMAL_NOTE_DONE'),
              onPress: _changeNewDate,
              disabled: areDatesTheSame,
            },
          ]}
        >
          <CText>{t('TET_BOOKING_TO_NORMAL_TASK_CHANGE_DATE')}</CText>
          <BlockView>
            <DatePicker
              title={t('STEP_4_UPDATE_CALENDAR_TITLE')}
              value={selectedDate}
              onChange={onChangeDateTime}
              settingSystem={settings?.settingSystem}
              timezone={timezone}
            />
            <TimePicker
              title={t('STEP_4_UPDATE_TIME_TITLE')}
              value={selectedDate}
              onChange={onChangeDateTime}
              settingSystem={settings?.settingSystem}
              timezone={timezone}
            />
          </BlockView>
        </CModal>
      ) : null}
    </BlockView>
  );
};
