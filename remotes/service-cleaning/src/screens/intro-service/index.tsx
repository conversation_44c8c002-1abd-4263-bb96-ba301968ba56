import React from 'react';
import {
  bgHeader,
  BlockView,
  BottomView,
  CleaningRouteName,
  CleaningStackScreenProps,
  Colors,
  ConditionView,
  CText,
  FastImage,
  FontSizes,
  IconImage,
  IconImageProps,
  Markdown,
  PrimaryButton,
  ScrollView,
  SizedBox,
  Spacing,
} from '@btaskee/design-system';
import { usePostTaskStore } from '@store';

import { useAppNavigation, useI18n } from '@hooks';
import {
  bgIntroCleaning,
  icIntroCleaning3,
  icIntroCleaning4,
  icIntroMoving3,
  iconIntroOfficeCarpet3,
} from '@images';

import styles from './styles';

const IntroItem = ({
  icon,
  title,
}: {
  icon: IconImageProps['source'];
  title: string;
}) => {
  return (
    <BlockView
      flex
      style={styles.wrapItemNote}
    >
      <IconImage source={icon} />
      <SizedBox width={Spacing.SPACE_12} />
      <BlockView flex>
        <Markdown
          text={title}
          textStyle={styles.txtMarkdown}
          paragraphStyle={styles.txtStyle}
        />
      </BlockView>
    </BlockView>
  );
};

export type IntroServiceProps =
  CleaningStackScreenProps<CleaningRouteName.IntroService>;

export const IntroService = ({ route }: IntroServiceProps) => {
  const { t } = useI18n();
  const navigation = useAppNavigation();
  const { setIsFirstOpen } = usePostTaskStore();
  const isHideButton = route?.params?.isHideButton;

  const onSubmit = async () => {
    // Set first open, and no show this intro again
    setIsFirstOpen(false);
    return navigation.replace(CleaningRouteName.ChooseAddress);
  };

  const onBack = () => navigation.goBack();

  return (
    <BlockView style={styles.container}>
      <BlockView flex>
        <BlockView>
          <FastImage
            style={styles.imageStyle}
            resizeMode={'cover'}
            source={bgHeader}
          />
        </BlockView>

        <BlockView style={styles.content}>
          <BlockView style={styles.wrap_image}>
            <FastImage
              resizeMode="cover"
              source={bgIntroCleaning}
              style={styles.imgIntro}
            />
          </BlockView>
          <ScrollView
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.contentContainerStyle}
          >
            <CText
              bold
              color={Colors.orange500}
              size={FontSizes.SIZE_18}
              margin={{ top: Spacing.SPACE_16, bottom: Spacing.SPACE_12 }}
            >
              {t('INTRO_CLEANING_SERVICE.TITLE')}
            </CText>

            <BlockView flex>
              <CText style={styles.txt_note}>
                {t('INTRO_CLEANING_SERVICE.INTRO_DESCRIPTION')}
              </CText>
            </BlockView>

            <BlockView style={styles.wrapBottom}>
              <IntroItem
                icon={icIntroCleaning3}
                title={t('INTRO_CLEANING_SERVICE.INTRO_CONTENT_1')}
              />
              <IntroItem
                icon={icIntroCleaning4}
                title={t('INTRO_CLEANING_SERVICE.INTRO_CONTENT_2')}
              />
              <IntroItem
                icon={icIntroMoving3}
                title={t('INTRO_CLEANING_SERVICE.INTRO_CONTENT_3')}
              />
              <IntroItem
                icon={iconIntroOfficeCarpet3}
                title={t('INTRO_CLEANING_SERVICE.INTRO_CONTENT_4')}
              />
            </BlockView>
          </ScrollView>
        </BlockView>
      </BlockView>

      <ConditionView
        condition={Boolean(isHideButton)}
        viewFalse={
          <BottomView>
            <PrimaryButton
              testID="btnSubmitIntroService"
              onPress={onSubmit}
              title={t('INTRO_START_EXPERIENCE')}
            />
          </BottomView>
        }
        viewTrue={
          <BottomView>
            <PrimaryButton
              testID="btnSubmitIntroService"
              onPress={onBack}
              title={t('BTN_BACK')}
            />
          </BottomView>
        }
      />
    </BlockView>
  );
};
