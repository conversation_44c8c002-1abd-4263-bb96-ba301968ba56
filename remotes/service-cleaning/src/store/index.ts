import {
  AppStorage,
  createZustand,
  <PERSON><PERSON><PERSON>,
  I<PERSON>ddons,
  IA<PERSON>ress,
  IDataBooking,
  IDate,
  IDateOptionItem,
  IPaymentMethodInfo,
  IPrice,
  IPromotion,
  IRelatedTask,
  IService,
  IUser,
  Maybe,
  PaymentService,
  Requirement,
  SERVICES,
  TrackingPostTaskStep,
  TYPE_OF_PAYMENT,
} from '@btaskee/design-system';
import { createJSONStorage, persist } from 'zustand/middleware';

interface IState {
  address?: Maybe<IAddress>;
  duration?: number;
  requirements?: Requirement[];
  isPremium?: boolean;
  isAutoChooseTasker?: boolean;
  isFavouriteTasker?: boolean;
  gender?: Maybe<GENDER>;
  pet?: any;
  addons?: IAddons[];
  date?: IDate;
  schedule?: number[];
  isEnabledSchedule?: boolean;
  note?: string;
  isApplyNoteForAllTask?: boolean;
  price?: Maybe<IPrice>;
  service?: Maybe<IService>;
  forceTasker?: IUser;
  dataQuickPostTask?: Maybe<IDataBooking>;
  dateOptions?: IDateOptionItem[];
  paymentMethod?: IPaymentMethodInfo;
  promotion?: IPromotion;
  loadingPrice: boolean;
  loadingPostTask: boolean;
  relatedTask: Maybe<IRelatedTask>;
  homeNumber?: string;
  stepPostTask: TrackingPostTaskStep;
  isBookedTask: boolean;
  isFirstOpen: boolean;
}

interface IActions {
  setAddress: (address: Maybe<IAddress>) => void;
  setDuration: (duration: number) => void;
  setRequirements: (requirements: Requirement[]) => void;
  setIsPremium: (isPremium: boolean) => void;
  setIsAutoChooseTasker: (isAutoChooseTasker: boolean) => void;
  setIsFavouriteTasker: (isFavouriteTasker: boolean) => void;
  setGender: (gender: Maybe<GENDER>) => void;
  setAddons: (addons: IAddons[]) => void;
  setPet: (pet: any) => void;
  setDateTime: (date: IDate) => void;
  setSchedule: (schedule: number[]) => void;
  setIsEnabledSchedule: (isEnabledSchedule: boolean) => void;
  setNote: (note: string) => void;
  setIsApplyNoteForAllTask: (isApplyNoteForAllTask: boolean) => void;
  setPrice: (price: Maybe<IPrice>) => void;
  setHomeNumber: (homeNumber: string) => void;
  setPaymentMethod: (paymentMethod?: IPaymentMethodInfo) => void;
  setPromotion: (promotion?: IPromotion) => void;
  setLoadingPrice: (loadingPrice: boolean) => void;
  setLoadingPostTask: (loadingPostTask: boolean) => void;
  setRelatedTask: (relatedTask: IRelatedTask) => void;
  setService: (service: Maybe<IService>) => void;
  setForceTasker: (forceTasker: IUser) => void;
  setDateOptions: (dateOptions: IDateOptionItem[]) => void;
  setStepPostTask: (stepPostTask: TrackingPostTaskStep) => void;
  setIsBookedTask: (isBookedTask: boolean) => void;
  setIsFirstOpen: (isFirstOpen: boolean) => void;
  resetState: () => void;
}

const INITIAL_STATE: IState = {
  address: {},
  duration: 0,
  requirements: [],
  isPremium: false,
  isAutoChooseTasker: true,
  isFavouriteTasker: true,
  gender: null,
  pet: '',
  addons: [],
  schedule: [],
  isEnabledSchedule: false,
  note: '',
  isApplyNoteForAllTask: false,
  dateOptions: [],
  homeNumber: '',
  price: null,
  service: null,
  forceTasker: {},
  dataQuickPostTask: null,
  loadingPrice: false,
  loadingPostTask: false,
  relatedTask: null,
  stepPostTask: TrackingPostTaskStep.STEP_2,
  isBookedTask: false,
  isFirstOpen: true,
  paymentMethod: PaymentService.getDefaultPaymentMethod({
    serviceName: SERVICES.CLEANING,
    type: TYPE_OF_PAYMENT.bookTask,
  }),
};

type PostTaskState = IState & IActions;

export const usePostTaskStore = createZustand<PostTaskState>()(
  persist(
    (set) => ({
      ...INITIAL_STATE,
      setAddress: (address) => set({ address: address }),
      setDuration: (duration) => set({ duration: duration }),
      setRequirements: (requirements) => set({ requirements: requirements }),
      setIsPremium: (isPremium) => set({ isPremium: isPremium }),
      setIsAutoChooseTasker: (isAutoChooseTasker) =>
        set({ isAutoChooseTasker: isAutoChooseTasker }),
      setIsFavouriteTasker: (isFavouriteTasker) =>
        set({ isFavouriteTasker: isFavouriteTasker }),
      setGender: (gender) => set({ gender: gender }),
      setAddons: (addons: IAddons[]) => set({ addons: addons }),
      setPet: (pet: any) => set({ pet: pet }),
      setDateTime: (date: IDate) => set({ date: date }),
      setSchedule: (schedule: number[]) => set({ schedule: schedule }),
      setIsEnabledSchedule: (isEnabledSchedule: boolean) =>
        set({ isEnabledSchedule: isEnabledSchedule }),
      setNote: (note: string) => set({ note: note }),
      setIsApplyNoteForAllTask: (isApplyNoteForAllTask: boolean) =>
        set({ isApplyNoteForAllTask: isApplyNoteForAllTask }),
      setPrice: (price: Maybe<IPrice>) => set({ price: price }),
      setHomeNumber: (homeNumber: string) => set({ homeNumber: homeNumber }),
      setPaymentMethod: (paymentMethod) =>
        set({ paymentMethod: paymentMethod }),
      setPromotion: (promotion) => set({ promotion: promotion }),
      setLoadingPrice: (loadingPrice: boolean) =>
        set({ loadingPrice: loadingPrice }),
      setLoadingPostTask: (loadingPostTask: boolean) =>
        set({ loadingPostTask: loadingPostTask }),
      setRelatedTask: (relatedTask: IRelatedTask) =>
        set({ relatedTask: relatedTask }),
      setService: (service: Maybe<IService>) => set({ service: service }),
      setForceTasker: (forceTasker: IUser) => set({ forceTasker: forceTasker }),
      setDateOptions: (dateOptions: IDateOptionItem[]) =>
        set({ dateOptions: dateOptions }),
      setStepPostTask: (stepPostTask: TrackingPostTaskStep) =>
        set({ stepPostTask: stepPostTask }),
      setIsBookedTask: (isBookedTask: boolean) =>
        set({ isBookedTask: isBookedTask }),
      setIsFirstOpen: (isFirstOpen: boolean) =>
        set({ isFirstOpen: isFirstOpen }),
      resetState: () =>
        set((state) => ({
          ...INITIAL_STATE,
          isBookedTask: state.isBookedTask,
          isFirstOpen: state.isFirstOpen,
        })),
    }),
    {
      name: 'cleaning-store',
      storage: createJSONStorage(() => AppStorage.zustandStorage),
      version: 1,
      partialize: (state) => ({ isFirstOpen: state.isFirstOpen }),
    },
  ),
);
