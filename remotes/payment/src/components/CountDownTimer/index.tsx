import React, { useMemo } from 'react';
import {
  BlockView,
  BorderRadius,
  Colors,
  CText,
  FontSizes,
  formatTwoDigits,
  IconAssets,
  IconImage,
  Spacing,
  useCountDown,
} from '@btaskee/design-system';

type CountDownTimerProps = {
  expiryTimer: number; // milliseconds
  title?: string;
  onExpired?: () => void;
};

export const CountDownTimer = ({
  expiryTimer,
  title,
  onExpired,
}: CountDownTimerProps) => {
  const { minutes, seconds, isExpired } = useCountDown({
    expiryTimer,
    onExpired,
  });

  /**
   * Formats time components to display string
   * Purpose: Converts minutes and seconds to mm:ss format
   * @returns {string} Formatted time string in mm:ss format
   */
  const countDownText = useMemo(() => {
    if (isExpired) return '00:00';

    // Format as mm:ss (minutes:seconds)
    return `${formatTwoDigits(minutes)}:${formatTwoDigits(seconds)}`;
  }, [minutes, seconds, isExpired]);

  return (
    <BlockView
      center
      gap={Spacing.SPACE_08}
    >
      {!!title && (
        <CText
          size={FontSizes.SIZE_12}
          color={Colors.neutral300}
          center
        >
          {title}
        </CText>
      )}
      <BlockView
        row
        center
        width={80}
        padding={Spacing.SPACE_04}
        backgroundColor={Colors.orange50}
        radius={BorderRadius.RADIUS_08}
      >
        <IconImage
          source={IconAssets.icClock}
          size={20}
        />
        <CText
          flex
          size={FontSizes.SIZE_12}
          color={Colors.orange500}
          center
          margin={{ left: Spacing.SPACE_04 }}
          bold
        >
          {countDownText}
        </CText>
      </BlockView>
    </BlockView>
  );
};
