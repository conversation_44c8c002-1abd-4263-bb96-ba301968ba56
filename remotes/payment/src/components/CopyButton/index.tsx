import React from 'react';
import {
  Colors,
  CText,
  IconAssets,
  IconImage,
  Spacing,
  ToastHelpers,
  TouchableOpacity,
} from '@btaskee/design-system';
import Clipboard from '@react-native-clipboard/clipboard';

import { useI18n } from '@hooks';

export const CopyButton = ({
  content,
  copyText,
}: {
  content?: string;
  copyText?: string;
}) => {
  const { t } = useI18n();

  const clipboard = () => {
    const text = copyText || content;
    if (!text) return;
    ToastHelpers.showSuccess({ message: t('LABEL_COPY') });
    Clipboard.setString(text);
  };

  return (
    <TouchableOpacity
      flex
      row
      gap={Spacing.SPACE_08}
      onPress={clipboard}
    >
      <CText
        bold
        right
        flex
      >
        {content}
      </CText>
      <IconImage
        source={IconAssets.icCopy}
        size={20}
        color={Colors.green500}
      />
    </TouchableOpacity>
  );
};
