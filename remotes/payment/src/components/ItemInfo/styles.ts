import { StyleSheet } from 'react-native';
import {
  BorderRadius,
  Colors,
  FontSizes,
  Spacing,
} from '@btaskee/design-system';

export const styles = StyleSheet.create({
  boxInfoItem: {
    alignItems: 'center',
    backgroundColor: Colors.neutral50,
    padding: Spacing.SPACE_16,
    borderRadius: BorderRadius.RADIUS_08,
  },
  txtLabel: {
    fontSize: FontSizes.SIZE_14,
    color: Colors.neutral600,
    flex: 1,
  },
  boxValue: {
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  bgValue: {
    backgroundColor: Colors.orange50,
    paddingHorizontal: Spacing.SPACE_08,
    paddingVertical: Spacing.SPACE_04,
    borderRadius: 4,
  },
  txtValue: {
    fontSize: FontSizes.SIZE_14,
    flex: 1,
  },
  icon: {
    marginLeft: Spacing.SPACE_08,
  },
});
