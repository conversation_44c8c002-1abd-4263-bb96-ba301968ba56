import React, { ReactNode } from 'react';
import {
  BlockView,
  Colors,
  ConditionView,
  CText,
} from '@btaskee/design-system';

import { styles } from './styles';

interface ItemInfoProps {
  testID?: string;
  label?: string;
  value?: string | ReactNode;
  icon?: ReactNode;
  isShowBackgroundValue?: boolean;
  backgroundColor?: string;
}

export const ItemInfo = ({
  testID,
  label,
  value,
  icon,
  isShowBackgroundValue,
  backgroundColor,
}: ItemInfoProps) => {
  return (
    <BlockView
      row
      style={[styles.boxInfoItem, { backgroundColor }]}
    >
      <CText style={styles.txtLabel}>{label}</CText>
      <BlockView
        flex
        row
        style={styles.boxValue}
      >
        {typeof value === 'string' ? (
          <BlockView style={isShowBackgroundValue ? styles.bgValue : {}}>
            <CText
              bold
              testID={testID}
              style={styles.txtValue}
              right
              color={
                isShowBackgroundValue ? Colors.orange500 : Colors.neutral900
              }
            >
              {value}
            </CText>
          </BlockView>
        ) : (
          value
        )}
        <ConditionView
          condition={Boolean(icon)}
          viewTrue={<BlockView style={styles.icon}>{icon}</BlockView>}
        />
      </BlockView>
    </BlockView>
  );
};
