import { StyleSheet } from 'react-native';
import {
  BorderRadius,
  Colors,
  FontSizes,
  Spacing,
} from '@btaskee/design-system';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.neutralBackground,
  },
  content: {
    padding: Spacing.SPACE_16,
  },
  boxNote: {
    borderColor: Colors.orange500,
    borderWidth: 1,
    padding: Spacing.SPACE_16,
    borderRadius: BorderRadius.RADIUS_08,
  },
  imageArea: {
    marginVertical: Spacing.SPACE_04,
    alignItems: 'center',
  },
  txtNote: {
    marginLeft: Spacing.SPACE_04,
    color: Colors.orange500,
    alignSelf: 'center',
  },
  txtDes: {
    lineHeight: 20,
    color: Colors.orange500,
    fontSize: FontSizes.SIZE_14,
  },
  logoBank: {
    width: FontSizes.SIZE_18,
    height: FontSizes.SIZE_18,
  },
});
