import React, { useMemo } from 'react';
import {
  Alert,
  BlockView,
  BottomView,
  Colors,
  CText,
  getTextWithLocale,
  IconAssets,
  IconImage,
  imgPayment,
  Markdown,
  NavBar,
  NavigationService,
  PaymentRouteName,
  PaymentService,
  PaymentStackScreenProps,
  PrimaryButton,
  ScrollView,
  showPriceAndCurrency,
  Spacing,
  TouchableOpacity,
} from '@btaskee/design-system';

import { CopyButton, CountDownTimer, ItemInfo } from '@components';
import { useI18n } from '@hooks';

import { styles } from './styles';

type PaymentWithVAScreenProps =
  PaymentStackScreenProps<PaymentRouteName.PayWithVA>;

export const PaymentWithVAScreen = ({ route }: PaymentWithVAScreenProps) => {
  const { amount, expiryTimer, payment, userManual, virtualAccountNumber } =
    route.params || {};

  const { t } = useI18n();

  const iconBank = useMemo(() => {
    return PaymentService.getBankInfoByName(payment?.bank)?.icon;
  }, [payment?.bank]);

  const onDone = () => {
    NavigationService.goBack();
  };

  const onHowToPay = () => {
    Alert.alert.open({
      title: t('HOW_TO_PAY'),
      message: <Markdown text={getTextWithLocale(userManual)} />,
      actions: [{ text: t('CLOSE') }],
    });
  };

  return (
    <BlockView style={styles.container}>
      <NavBar
        title={t('PAYMENT_TOP_UP')}
        isShadow={true}
      />
      <ScrollView
        contentContainerStyle={styles.content}
        showsVerticalScrollIndicator={false}
      >
        <BlockView center>
          <IconImage
            source={imgPayment}
            size={150}
          />
        </BlockView>
        <BlockView
          gap={Spacing.SPACE_16}
          margin={{ top: Spacing.SPACE_16 }}
        >
          <ItemInfo
            backgroundColor={Colors.neutralWhite}
            label={t('FINAL_PAYMENT_DEADLINE')}
            value={<CountDownTimer expiryTimer={expiryTimer || 0} />}
          />
          <ItemInfo
            backgroundColor={Colors.neutralWhite}
            label={payment?.text}
            value={
              <IconImage
                source={iconBank}
                size={50}
              />
            }
          />
          <ItemInfo
            backgroundColor={Colors.neutralWhite}
            label={t('VIRTUAL_ACCOUNT_NUMBER')}
            value={<CopyButton content={virtualAccountNumber} />}
          />
          <ItemInfo
            backgroundColor={Colors.neutralWhite}
            label={t('TOTAL_PAYMENT')}
            value={
              <CopyButton
                content={showPriceAndCurrency(amount)}
                copyText={amount?.toString()}
              />
            }
          />
          <BlockView style={styles.boxNote}>
            <CText style={styles.txtDes}>
              {t('BANK_TRANSFER_DESCRIPTION')}
            </CText>
          </BlockView>
          <TouchableOpacity
            justify="flex-end"
            align="center"
            row
            gap={Spacing.SPACE_04}
            onPress={onHowToPay}
          >
            <IconImage
              source={IconAssets.icInfoCircle}
              size={18}
              color={Colors.green500}
            />
            <CText
              underline
              color={Colors.green500}
            >
              {t('HOW_TO_PAY')}
            </CText>
          </TouchableOpacity>
        </BlockView>
      </ScrollView>
      <BottomView>
        <PrimaryButton
          title={t('BTN_BACK')}
          onPress={onDone}
        />
      </BottomView>
    </BlockView>
  );
};
