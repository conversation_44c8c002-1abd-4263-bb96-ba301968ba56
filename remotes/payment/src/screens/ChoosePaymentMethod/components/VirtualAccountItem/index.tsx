import React, { useCallback } from 'react';
import {
  BlockView,
  CText,
  HitSlop,
  IBankInfo,
  IconImage,
  IconImageProps,
  Spacing,
  TouchableOpacity,
} from '@btaskee/design-system';

import { IconChecked } from '../IconChecked';
import {
  PaymentMethodItem,
  PaymentMethodItemProps,
} from '../PaymentMethodItem';
import { styles } from './styles';

export type VirtualAccountItemProps = {
  bankSelected?: string;
  banks?: IBankInfo[];
  onBankSelected: (bankInfo: IBankInfo) => void;
} & Pick<PaymentMethodItemProps, 'icon' | 'title' | 'disabled' | 'campaign'>;

type ItemProps = {
  label?: string;
  icon?: IconImageProps['source'];
  isSelected?: boolean;
  onPress?: () => void;
};

const Item = ({ label, icon, isSelected, onPress }: ItemProps) => {
  return (
    <TouchableOpacity
      hitSlop={HitSlop.SMALL}
      style={styles.itemContainer}
      onPress={onPress}
    >
      <BlockView style={styles.iconContainer}>
        <IconImage
          source={icon}
          size={28}
        />
      </BlockView>
      <CText flex>{label}</CText>
      <IconChecked
        isSelected={isSelected}
        size={20}
      />
    </TouchableOpacity>
  );
};

export const VirtualAccountItem = ({
  icon,
  title,
  disabled,
  bankSelected,
  banks,
  campaign,
  onBankSelected,
}: VirtualAccountItemProps) => {
  const _onBankSelected = useCallback(
    (item: IBankInfo) => () => {
      onBankSelected?.(item);
    },
    [onBankSelected],
  );

  return (
    <PaymentMethodItem
      title={title}
      icon={icon}
      iconSize={45}
      disabled={disabled}
      campaign={campaign}
    >
      <BlockView margin={{ left: Spacing.SPACE_16 }}>
        {banks?.map((item) => {
          return (
            <Item
              key={item.name}
              icon={item.icon}
              label={item.text}
              isSelected={bankSelected === item.name}
              onPress={_onBankSelected(item)}
            />
          );
        })}
      </BlockView>
    </PaymentMethodItem>
  );
};
