import React from 'react';
import {
  BorderRadius,
  BRewardRouteName,
  Colors,
  CText,
  getTextWithLocale,
  ICampaignPaymentMethod,
  IconAssets,
  IconImage,
  NavigationService,
  RouteName,
  Spacing,
  TouchableOpacity,
} from '@btaskee/design-system';
import { isEmpty } from 'lodash-es';

export interface CampaignProps {
  data?: ICampaignPaymentMethod;
}

export const getParamsFromNavigateTo = (navigateTo = '') => {
  const params: any = {};
  const pattern = /(\w+)=([^&]+)/g;
  const screenName = (navigateTo && navigateTo.split('?')[0]) || '';
  let match;
  while ((match = pattern.exec(navigateTo)) !== null) {
    const [fullMatch, key, value] = match;
    params[key] = value;
  }
  return { screenName: screenName as keyof typeof BRewardRouteName, params };
};

export const Campaign = ({ data }: CampaignProps) => {
  // navigateTo có dạng MarketingCampaignDetail?campaignId=xxx
  const handleOnPress = () => {
    const { screenName, params } = getParamsFromNavigateTo(data?.navigateTo);
    if (screenName === 'MarketingCampaignDetail') {
      params.isHideAction = true;
    }
    NavigationService.navigate(RouteName.BReward, {
      screen: BRewardRouteName[screenName],
      params,
    });
  };

  if (isEmpty(data)) {
    return null;
  }

  return (
    <TouchableOpacity
      row
      margin={{ top: Spacing.SPACE_12 }}
      padding={{ horizontal: Spacing.SPACE_12, vertical: Spacing.SPACE_08 }}
      backgroundColor={Colors.green50}
      radius={BorderRadius.RADIUS_08}
      gap={Spacing.SPACE_08}
      align="center"
      onPress={handleOnPress}
    >
      <IconImage
        source={IconAssets.icDiscount}
        color={Colors.green500}
        size={20}
      />
      <CText
        color={Colors.green500}
        numberOfLines={2}
        flex
      >
        {getTextWithLocale(data?.description)}
      </CText>
      <IconImage
        source={IconAssets.icNext}
        color={Colors.green500}
        size={16}
      />
    </TouchableOpacity>
  );
};
