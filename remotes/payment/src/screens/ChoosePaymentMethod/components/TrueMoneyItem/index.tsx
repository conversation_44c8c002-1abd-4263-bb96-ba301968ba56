import React, { useCallback, useState } from 'react';
import {
  BlockView,
  Colors,
  CText,
  HitSlop,
  IPaymentMethodInfo,
  NavigationService,
  PaymentRouteName,
  PaymentService,
  RouteName,
  Spacing,
  TouchableOpacity,
} from '@btaskee/design-system';

import { useI18n } from '@hooks';

import {
  PaymentMethodItem,
  PaymentMethodItemProps,
} from '../PaymentMethodItem';

export type TrueMoneyItemProps = {
  walletInfo?: IPaymentMethodInfo['walletInfo'];
  onSelected: (walletInfo: IPaymentMethodInfo['walletInfo']) => void;
} & Pick<PaymentMethodItemProps, 'icon' | 'title' | 'campaign' | 'isSelected'>;

export const TrueMoneyItem = ({
  icon,
  title,
  walletInfo,
  isSelected,
  campaign,
  onSelected,
}: TrueMoneyItemProps) => {
  const { t } = useI18n();

  const onLinkWallet = useCallback(() => {
    NavigationService.navigate(RouteName.Payment, {
      screen: PaymentRouteName.LinkWallet,
      params: {
        onLinked: (data) => {
          onSelected?.(data);
        },
      },
    });
  }, [onSelected]);

  const onPress = useCallback(() => {
    if (walletInfo?.phoneNumber) {
      onSelected?.(walletInfo);
    } else {
      onLinkWallet();
    }
  }, [onLinkWallet, onSelected, walletInfo]);

  return (
    <PaymentMethodItem
      title={title}
      subTitle={PaymentService.showTrueMoneyAccount(walletInfo?.phoneNumber)}
      icon={icon}
      campaign={campaign}
      isSelected={isSelected}
      onPress={onPress}
    >
      <BlockView
        margin={{ top: Spacing.SPACE_16 }}
        padding={{ horizontal: Spacing.SPACE_16 }}
        gap={Spacing.SPACE_16}
      >
        <TouchableOpacity
          onPress={onLinkWallet}
          hitSlop={HitSlop.MEDIUM}
        >
          <CText color={Colors.green500}>
            {walletInfo?.phoneNumber ? t('EDIT') : t('LINK_WALLET')}
          </CText>
        </TouchableOpacity>
      </BlockView>
    </PaymentMethodItem>
  );
};
