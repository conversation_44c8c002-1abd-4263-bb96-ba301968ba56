import React, { useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import {
  BlockView,
  CheckBox,
  CKeyboardStickyView,
  CText,
  FastImage,
  FormTextInput,
  getDefaultCountryWithIsoCode,
  getPhoneNumber,
  IconAssets,
  NavBar,
  NavigationService,
  PaymentRouteName,
  PaymentStackScreenProps,
  PrimaryButton,
  useAppStore,
  validPhoneNumber,
} from '@btaskee/design-system';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';

import { useI18n } from '@hooks';

import { styles } from './styles';

type LinkWalletScreenProps =
  PaymentStackScreenProps<PaymentRouteName.LinkWallet>;

export const LinkWalletScreen = ({ route }: LinkWalletScreenProps) => {
  const { onLinked } = route.params || {};

  const { t } = useI18n();
  const { isoCode } = useAppStore();
  const countryCode = getDefaultCountryWithIsoCode(isoCode)?.countryCode;

  const [isSave, setIsSave] = useState(true);

  const loginSchema = useMemo(
    () =>
      yup.object().shape({
        phoneNumber: yup
          .string()
          .trim()
          .required(t('THIS_FIELD_IS_REQUIRED'))
          .test('valid-phone', t('INVALID_PHONE'), function (value) {
            const context = this.options.context;
            if (!value) return false;

            const valid = validPhoneNumber(value, context?.countryCode);
            if (valid) return true;
            return this.createError({
              path: 'phoneNumber',
              message: t('PHONE_NUMBER_SYNTAX_IS_INCORRECT'),
            });
          }),
      }),
    [t],
  );

  const {
    control,
    handleSubmit,
    formState: { isValid },
  } = useForm({
    mode: 'onChange',
    resolver: yupResolver(loginSchema),
    context: {
      countryCode,
    },
  });

  const onConfirmed = () => {
    handleSubmit((params) => {
      onLinked?.({
        phoneNumber: getPhoneNumber(params.phoneNumber, countryCode),
        countryCode: countryCode,
        isSave,
      });
      NavigationService.goBack();
    })();
  };

  return (
    <BlockView style={styles.container}>
      <NavBar title={t('PAYMENT_METHOD_TRUE_MONEY')} />
      <BlockView style={styles.wrapContent}>
        <BlockView row>
          <FastImage
            source={IconAssets.logoTrueMoneyTransparent}
            style={styles.icon}
          />
          <BlockView flex>
            <CText>{t('TRUEMONEY_DESCRIPTION_1')}</CText>
            <CText>{t('TRUEMONEY_DESCRIPTION_2')}</CText>
          </BlockView>
        </BlockView>
        <BlockView style={styles.content}>
          <FormTextInput
            control={control}
            name="phoneNumber"
            label={t('TRUEMONEY_DESCRIPTION_3')}
            placeholder={t('INPUT_PHONE_NUMBER')}
            keyboardType="phone-pad"
            variant="secondary"
            maxLength={12}
            clearButtonMode="while-editing"
          />
          <CheckBox
            checked={isSave}
            onChecked={setIsSave}
            title={t('TRUEMONEY_SAVE')}
          />
        </BlockView>
      </BlockView>
      <CKeyboardStickyView>
        <PrimaryButton
          title={t('OK')}
          onPress={onConfirmed}
          disabled={!isValid}
        />
      </CKeyboardStickyView>
    </BlockView>
  );
};
