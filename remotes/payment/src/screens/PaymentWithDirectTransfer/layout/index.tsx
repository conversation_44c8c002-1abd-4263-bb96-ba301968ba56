import React, { useMemo } from 'react';
import {
  bg<PERSON><PERSON>er,
  BlockView,
  BottomView,
  Colors,
  CText,
  FastImage,
  getTextWithLocale,
  IconAssets,
  IconImage,
  NavBar,
  NavigationService,
  PaymentRouteName,
  PaymentStackScreenProps,
  PrimaryButton,
  ScrollView,
  showPriceAndCurrency,
  Spacing,
  useSettingsStore,
} from '@btaskee/design-system';

import { useI18n } from '@hooks';

import { CopyButton, ItemInfo } from '../../../components';
import { LineOrTransfer } from '../components/LineOrTransfer';
import { QRCodeBlock } from '../components/QRCodeBlock';
import { styles } from './styles';

type PaymentWithDirectTransferScreenProps =
  PaymentStackScreenProps<PaymentRouteName.PayWithDirectTransfer>;

export const PaymentWithDirectTransferScreen = ({
  route,
}: PaymentWithDirectTransferScreenProps) => {
  const { cost, orderId, phone, taskPlace } = route.params || {};
  const { t } = useI18n();
  const { settings } = useSettingsStore();

  const instruction = useMemo(() => {
    if (!settings?.settingSystem?.subscriptionPaymentInstruction?.length)
      return;

    return (
      settings?.settingSystem.subscriptionPaymentInstruction.find((item) => {
        return item.city === taskPlace?.city;
      }) || settings?.settingSystem.subscriptionPaymentInstruction[0]
    );
  }, [settings, taskPlace?.city]);

  const content = useMemo(() => {
    return `${orderId} - ${phone}`.toUpperCase();
  }, [orderId, phone]);

  const onDone = () => {
    NavigationService.goBack();
  };

  if (!instruction) {
    return null;
  }

  return (
    <BlockView style={styles.container}>
      <FastImage
        resizeMode="cover"
        source={bgHeader}
        style={styles.bgImage}
      />
      <NavBar
        title={t('BANK_INFO')}
        backgroundColor={Colors.transparent}
        isShadow={true}
        borderBottomWidth={0}
        color={Colors.neutralWhite}
      />
      <ScrollView
        contentContainerStyle={styles.content}
        showsVerticalScrollIndicator={false}
      >
        <BlockView
          gap={Spacing.SPACE_20}
          style={styles.boxInfo}
        >
          <QRCodeBlock uriQR={instruction?.qrCode} />
          <LineOrTransfer />
          <BlockView gap={Spacing.SPACE_16}>
            <ItemInfo
              label={t('BANK_NAME')}
              value={getTextWithLocale(instruction?.bankName).toUpperCase()}
              testID="bankName"
              icon={
                instruction?.bankIcon ? (
                  <FastImage
                    resizeMode="cover"
                    source={{ uri: instruction.bankIcon }}
                    style={styles.logoBank}
                  />
                ) : null
              }
            />
            <ItemInfo
              label={t('BANK_BRANCH')}
              value={getTextWithLocale(
                instruction?.bankDepartment,
              ).toUpperCase()}
              testID="bankBranch"
            />
            <ItemInfo
              label={t('SUBSCRIPTION_PAYMENT_INSTRUCTION_ACCOUNT')}
              value={<CopyButton content={instruction?.accountNumber} />}
              testID="accountNumber"
            />
            <ItemInfo
              label={t('SUBSCRIPTION_PAYMENT_INSTRUCTION_ACCOUNT_OWNER')}
              value={instruction?.accountHolder?.toUpperCase()}
              testID="accountHolder"
            />
            <ItemInfo
              isShowBackgroundValue
              label={t('SUBSCRIPTION_PAYMENT_AMOUNT')}
              value={showPriceAndCurrency(cost)}
              testID="costOfSubscription"
            />
            <ItemInfo
              label={t('SUBSCRIPTION_PAYMENT_INSTRUCTION_CONTENT')}
              value={<CopyButton content={content} />}
              testID="descriptionTransfer"
            />
          </BlockView>
          <BlockView style={styles.boxNote}>
            <BlockView
              row
              style={styles.imageArea}
            >
              <IconImage source={IconAssets.icWarningCircle} />
              <CText
                bold
                style={styles.txtNote}
              >
                {t('NOTE')}
              </CText>
            </BlockView>
            <CText style={styles.txtDes}>
              {t('DESCRIPTION_DIRECT_TRANSFER')}
            </CText>
          </BlockView>
        </BlockView>
      </ScrollView>
      <BottomView>
        <PrimaryButton
          title={t('DONE')}
          onPress={onDone}
        />
      </BottomView>
    </BlockView>
  );
};
