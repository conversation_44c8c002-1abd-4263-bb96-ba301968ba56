import { Dimensions, StyleSheet } from 'react-native';
import {
  BorderRadius,
  Colors,
  <PERSON>ceHelper,
  FontSizes,
  Spacing,
} from '@btaskee/design-system';

const { width, height } = Dimensions.get('window');

export const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  bgImage: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: <PERSON><PERSON><PERSON>elper.WINDOW.HEIGHT / 4,
    zIndex: -1,
  },
  content: {
    padding: Spacing.SPACE_16,
    shadowColor: Colors.neutral300,
    shadowOffset: {
      width: 1,
      height: 5,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  boxInfo: {
    backgroundColor: Colors.neutralWhite,
    padding: Spacing.SPACE_16,
    borderRadius: BorderRadius.RADIUS_08,
  },
  boxNote: {
    borderColor: Colors.orange500,
    borderWidth: 1,
    marginTop: Spacing.SPACE_24,
    padding: Spacing.SPACE_16,
    borderRadius: BorderRadius.RADIUS_08,
  },
  imageArea: {
    marginVertical: Spacing.SPACE_04,
    alignItems: 'center',
  },
  txtNote: {
    marginLeft: Spacing.SPACE_04,
    color: Colors.orange500,
    alignSelf: 'center',
  },
  txtDes: {
    lineHeight: 20,
    color: Colors.orange500,
    fontSize: FontSizes.SIZE_14,
  },
  logoBank: {
    width: FontSizes.SIZE_18,
    height: FontSizes.SIZE_18,
  },
});
