import React, { useRef } from 'react';
import ViewShot from 'react-native-view-shot';
import {
  BlockView,
  Colors,
  CText,
  FastImage,
  FontSizes,
  ImageHelpers,
  imgNapas247,
  imgVietQR,
  PrimaryButton,
  Spacing,
  ToastHelpers,
} from '@btaskee/design-system';
import { useI18n } from '@hooks';

import { styles } from './styles';

interface QRCodeBlockProps {
  uriQR?: string;
}

export const QRCodeBlock = ({ uriQR }: QRCodeBlockProps) => {
  const { t } = useI18n();
  const viewShotRef = useRef<ViewShot>(null);

  const saveQRCode = async () => {
    try {
      const path = await viewShotRef.current?.capture?.();
      if (!path) return;
      const uri = ImageHelpers.convertPathToUri(path);
      await ImageHelpers.saveImageToPhotoLibrary(uri);
      ToastHelpers.showSuccess({ message: t('SAVE_IMAGE_SUCCESS') });
    } catch (error) {
      ToastHelpers.showError({ message: t('ERROR_TRY_AGAIN') });
    }
  };

  return (
    <BlockView>
      <ViewShot
        ref={viewShotRef}
        options={{ format: 'png' }}
        style={styles.container}
      >
        <BlockView>
          <CText
            bold
            center
          >
            {t('SCAN_QR_CODE_TO_TRANSFER').toUpperCase()}
          </CText>
        </BlockView>
        <FastImage
          source={{ uri: uriQR }}
          style={styles.qrCode}
        />
        <BlockView gap={Spacing.SPACE_04}>
          <CText
            center
            color={Colors.neutral400}
            size={FontSizes.SIZE_12}
          >
            {t('PAYMENT_NOTE')}
          </CText>
          <BlockView
            row
            gap={Spacing.SPACE_24}
            center
          >
            <FastImage
              source={imgVietQR}
              style={styles.imgLogo}
            />
            <FastImage
              source={imgNapas247}
              style={styles.imgLogo}
            />
          </BlockView>
        </BlockView>
      </ViewShot>
      <PrimaryButton
        title={t('SAVE_IMAGE')}
        style={styles.button}
        titleProps={{
          size: FontSizes.SIZE_12,
        }}
        onPress={saveQRCode}
      />
    </BlockView>
  );
};
