import React from 'react';
import {
  BlockView,
  Colors,
  CText,
  SizedBox,
  Spacing,
} from '@btaskee/design-system';

import { useI18n } from '@hooks';

import { styles } from './styles';

export const LineOrTransfer = () => {
  const { t } = useI18n();

  return (
    <BlockView margin={{ vertical: Spacing.SPACE_16 }}>
      <SizedBox
        width={'100%'}
        height={1}
        color={Colors.neutral200}
      />
      <BlockView style={styles.text}>
        <CText
          center
          alignSelf="center"
          color={Colors.neutral400}
          backgroundColor={Colors.neutralWhite}
          padding={{ horizontal: Spacing.SPACE_24 }}
        >
          {t('OR_PAYMENT_TRANSFER')}
        </CText>
      </BlockView>
    </BlockView>
  );
};
