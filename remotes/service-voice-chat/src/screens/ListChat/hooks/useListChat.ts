import { useCallback, useEffect, useRef, useState } from 'react';
import { useAgentStore, useUserStore } from '@btaskee/design-system';

import { MessageStatus, Sender } from '@models';

import { useConversationsStore } from '../../../stores';
import { fetchConversations } from '../services/conversation.service';
import { useSocketAgent, UseSocketAgentProps } from './useSocketAgent';

export const useListChat = () => {
  const { resetMessageUnread } = useAgentStore();
  const { user } = useUserStore();

  const userId = user?._id;

  const {
    conversationId,
    conversations,
    setConversations,
    addNewMessage,
    addNewConversations,
    resetConversations,
    removeWaitingMessage,
    setConversationId,
  } = useConversationsStore();

  const [isSingleMode, setIsSingleMode] = useState(true);
  const [isWaitingResponse, setIsWaitingResponse] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const isLoadingMoreRef = useRef(isLoadingMore);
  const isFinalLoadMoreRef = useRef(false);

  const lastMessageIdRef = useRef<string>();

  const onMessage = useCallback<UseSocketAgentProps['onMessage']>(
    (data) => {
      setIsWaitingResponse(false);
      setIsSingleMode(data?.status === MessageStatus.active);

      if (!data?.success) {
        addNewMessage({
          message: data?.error,
          sender: Sender.ai,
        });
        return;
      }

      if (data.sender && [Sender.ai, Sender.cs].includes(data.sender)) {
        addNewMessage({
          id: data.messageId,
          message: data.message,
          createdAt: data.createdAt,
          sender: data.sender,
        });
        return;
      }

      removeWaitingMessage();
    },
    [addNewMessage, removeWaitingMessage],
  );

  const { emitMessage } = useSocketAgent({
    onMessage,
  });

  const loadConversations = useCallback(
    async (isLoadMore = false) => {
      try {
        isLoadingMoreRef.current = isLoadMore;
        setIsLoadingMore(isLoadMore);

        const response = await fetchConversations({
          user_id: userId,
          before: lastMessageIdRef.current,
        });
        setIsSingleMode(response?.status === MessageStatus.active);
        const messages = response?.messages || [];

        isFinalLoadMoreRef.current = !!response?.isFinal;

        const lastMessageId = messages?.[0]?.id;

        lastMessageIdRef.current =
          lastMessageId !== lastMessageIdRef.current ? lastMessageId : '';

        const newConversations = messages?.reverse() || [];

        if (isLoadMore) {
          // Append new conversations to existing list
          return addNewConversations(newConversations);
        }

        setConversations(newConversations);
      } catch (error) {
      } finally {
        setIsLoadingMore(false);
        isLoadingMoreRef.current = false;
      }
    },
    [addNewConversations, setConversations, userId],
  );

  const onSendMessage = useCallback(
    (message?: string) => {
      if (!message?.trim()) return;

      addNewMessage({
        message,
        sender: Sender.user,
      });
      emitMessage({ text: message });

      if (isSingleMode) {
        setIsWaitingResponse(true);
        addNewMessage({ isWaiting: true });
      }
    },
    [addNewMessage, emitMessage, isSingleMode],
  );

  useEffect(() => {
    // Fake conversationId = userId để keep list chat
    // Nếu khác conversationId thì reset list chat
    if (userId !== conversationId) {
      resetConversations();
      setConversationId(userId);
    }
  }, [conversationId, resetConversations, setConversationId, userId]);

  // Auto-load conversations on mount
  useEffect(() => {
    resetMessageUnread();
    loadConversations();
  }, [loadConversations, resetMessageUnread]);

  const loadMoreConversations = useCallback(() => {
    if (
      isFinalLoadMoreRef.current ||
      isLoadingMoreRef.current ||
      !lastMessageIdRef.current
    )
      return;

    loadConversations(true);
  }, [loadConversations]);

  return {
    conversations,
    isWaitingResponse,
    isLoadingMore,
    isSingleMode,
    loadMoreConversations,
    onSendMessage,
  };
};
