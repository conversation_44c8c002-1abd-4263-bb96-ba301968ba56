/**
 * Conversation API service for pagination support
 * Purpose: Handles API calls to fetch conversations with pagination parameters
 */

import Config from 'react-native-config';
import { Log } from '@btaskee/design-system';

import { IGetConversations } from '@models';
import { ConversationUtils, DeviceUtils } from '@utils';

// API Configuration
const MESSAGES_ENDPOINT = '/mobile/messages';

const DEFAULT_LIMIT = 20;

export const fetchConversations = async (
  params: Pick<IGetConversations['params'], 'user_id' | 'limit' | 'before'>,
) => {
  const deviceId = await DeviceUtils.getDeviceId();
  const timestamp = ConversationUtils.getCurrentTimestamp();
  const signature = ConversationUtils.generateHMACSignature(
    deviceId,
    timestamp,
  );
  // Build URL with query parameters
  const urlParams = new URLSearchParams();
  urlParams.append('limit', (params.limit || DEFAULT_LIMIT).toString());
  urlParams.append('device_id', deviceId);
  urlParams.append('timestamp', timestamp.toString());

  if (params.user_id) {
    urlParams.append('user_id', params.user_id || '');
  }

  if (params.before) {
    urlParams.append('before', params.before);
  }

  const headers = {
    'Content-Type': 'application/json',
    signature,
  };

  const url = `${
    Config.VOICE_CHAT_BASE_URL
  }${MESSAGES_ENDPOINT}?${urlParams.toString()}`;

  try {
    const response = await fetch(url, {
      method: 'GET',
      headers,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = (await response.json()) as IGetConversations['response'];
    Log.consoleLog('🟢 fetchConversations', {
      url,
      urlParams,
      headers,
      response: data,
    });

    // Transform API response to match our interface
    return data;
  } catch (error) {
    Log.consoleLog('🔴 fetchConversations', {
      url,
      urlParams,
      headers,
      error,
    });
    throw error;
  }
};
