import { IDate } from '@btaskee/design-system';

export enum MessageStatus {
  active = 'active',
  escalated = 'escalated',
  agent_active = 'agent_active',
}

export enum Sender {
  ai = 'ai',
  user = 'user',
  cs = 'cs',
}

export interface IMessage {
  id?: string;
  message?: string;
  createdAt?: IDate;
  sender?: Sender;
  isWaiting?: boolean;
}

interface IParams {
  user_id?: string;
  limit?: number;
  before?: string;
}

interface IResponse {
  conversationId?: string;
  messages?: IMessage[];
  isFinal?: boolean;
  status?: MessageStatus;
}

export interface IGetConversations {
  params: IParams;
  response?: IResponse | null;
}
