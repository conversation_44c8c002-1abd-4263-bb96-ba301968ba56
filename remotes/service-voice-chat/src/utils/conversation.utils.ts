import Config from 'react-native-config';
import { DateTimeHelpers, StringHelpers } from '@btaskee/design-system';
import CryptoJS from 'crypto-js';

export class ConversationUtils {
  static generateHMACSignature(deviceId: string, timestamp: number) {
    try {
      const payload = {
        device_id: deviceId,
        timestamp: timestamp,
      };

      const accessKey = Config.VOICE_CHAT_ACCESS_KEY_API;
      if (!accessKey) {
        throw new Error('VOICE_CHAT_ACCESS_KEY_API is not set');
      }
      const payloadString = JSON.stringify(
        payload,
        Object.keys(payload).sort(),
      );
      const signature = CryptoJS.HmacSHA256(
        payloadString,
        accessKey,
      ).toString();

      return signature;
    } catch (error) {
      return '';
    }
  }

  static getCurrentTimestamp() {
    return DateTimeHelpers.toDayTz({}).unix();
  }

  static genCreatedAtMessage() {
    return DateTimeHelpers.formatToString({});
  }

  static genMessageId() {
    return StringHelpers.genUniqueId();
  }
}
