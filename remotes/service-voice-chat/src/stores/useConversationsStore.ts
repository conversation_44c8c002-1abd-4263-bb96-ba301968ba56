import { create } from 'zustand';

import { IMessage } from '@models';
import { ConversationUtils } from '@utils';

// <PERSON><PERSON>nh nghĩa kiểu dữ liệu cho state
interface IConversationState {
  conversationId?: string;
  conversations: IMessage[];
  setConversationId: (conversationId?: string) => void;
  setConversations: (conversations: IMessage[]) => void;
  addNewConversations: (newConversation: IMessage[]) => void;
  addNewMessage: (message: IMessage) => void;
  removeWaitingMessage: () => void;
  resetConversations: () => void;
}

export const useConversationsStore = create<IConversationState>()(
  (set, get) => ({
    conversations: [],
    setConversationId: (conversationId) => set({ conversationId }),
    setConversations: (conversations) => set({ conversations }),
    addNewConversations: (newConversation) => {
      const state = get();
      const prevConversations = state.conversations;
      return set({
        conversations: [...prevConversations, ...newConversation],
      });
    },
    addNewMessage: (message) => {
      const state = get();
      const prevConversations = state.conversations;
      const createdAt = ConversationUtils.genCreatedAtMessage();
      const id = ConversationUtils.genMessageId();
      const newConversation = {
        id,
        createdAt,
        ...message,
      };

      return set({
        conversations: [
          newConversation,
          ...prevConversations.filter((item) => !item.isWaiting),
        ],
      });
    },
    removeWaitingMessage: () => {
      const state = get();
      const prevConversations = state.conversations;
      return set({
        conversations: prevConversations.filter((item) => !item.isWaiting),
      });
    },
    resetConversations: () => set({ conversations: [] }),
  }),
);
