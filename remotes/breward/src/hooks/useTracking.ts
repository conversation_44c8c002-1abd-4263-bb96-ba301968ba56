import { useCallback } from 'react';
import { AppState, AppStateStatus } from 'react-native';
import {
  TrackingActions,
  TrackingScreenNames,
  TrackingServices,
  useAppStore,
  useUserStore,
} from '@btaskee/design-system';
import { useIsFocused } from '@react-navigation/native';
import { useAppNavigation } from '@src/hooks';

/**
 * BReward Service Tracking Hook
 *
 * Provides comprehensive tracking functionality for breward service following
 * the multi-provider tracking architecture with provider-agnostic interface.
 *
 * Features:
 * - Screen view tracking for all breward screens
 * - User interaction tracking (clicks, searches, navigation)
 * - Reward redemption tracking (clicks, success, failure)
 * - Search tracking with query and filter parameters
 * - Navigation tracking with entry points
 * - Error and performance tracking
 * - Multi-provider support (CleverTap, Appmetrica, Firebase Analytics)
 */
export const useTracking = () => {
  const { user } = useUserStore();
  const { isoCode } = useAppStore();
  const navigation = useAppNavigation();
  const isFocused = useIsFocused();

  /**
   * Track screen view for BReward Home screen
   * @param entryPoint - Where the user came from to reach this screen
   */
  const trackingHomeScreenView = useCallback(
    (entryPoint?: string) => {
      TrackingServices.trackingScreenView({
        screenName: TrackingScreenNames.bRewards,
        entryPoint: entryPoint || TrackingScreenNames.Home,
        additionalInfo: {
          userPoint: user?.point,
          userRank: user?.rankInfo?.rankName,
          country: isoCode,
        },
      });
    },
    [user?.point, user?.rankInfo?.rankName, isoCode],
  );

  /**
   * Track screen view for Search screen
   * @param entryPoint - Where the user came from to reach this screen
   */
  const trackingSearchScreenView = useCallback(
    (entryPoint?: string) => {
      TrackingServices.trackingScreenView({
        screenName: TrackingScreenNames.SearchResult,
        entryPoint: entryPoint || TrackingScreenNames.bRewards,
        additionalInfo: {
          userPoint: user?.point,
          userRank: user?.rankInfo?.rankName,
          country: isoCode,
        },
      });
    },
    [user?.point, user?.rankInfo?.rankName, isoCode],
  );

  /**
   * Track screen view for Reward Detail screen
   * @param rewardId - ID of the reward being viewed
   * @param entryPoint - Where the user came from to reach this screen
   */
  const trackingRewardDetailScreenView = useCallback(
    (rewardId: string, entryPoint?: string) => {
      TrackingServices.trackingScreenView({
        screenName: TrackingScreenNames.DetailNewVoucher,
        entryPoint: entryPoint || TrackingScreenNames.bRewards,
        additionalInfo: {
          rewardId,
          userPoint: user?.point,
          userRank: user?.rankInfo?.rankName,
          country: isoCode,
        },
      });
    },
    [user?.point, user?.rankInfo?.rankName, isoCode],
  );

  /**
   * Track screen view for My Reward screen
   * @param entryPoint - Where the user came from to reach this screen
   */
  const trackingMyRewardScreenView = useCallback(
    (entryPoint?: string) => {
      TrackingServices.trackingScreenView({
        screenName: TrackingScreenNames.ListRandomVoucher,
        entryPoint: entryPoint || TrackingScreenNames.bRewards,
        additionalInfo: {
          userPoint: user?.point,
          userRank: user?.rankInfo?.rankName,
          country: isoCode,
        },
      });
    },
    [user?.point, user?.rankInfo?.rankName, isoCode],
  );

  /**
   * Track screen view for All Reward screen
   * @param type - Type of rewards being viewed
   * @param categoryName - Category name if filtering by category
   * @param entryPoint - Where the user came from to reach this screen
   */
  const trackingAllRewardScreenView = useCallback(
    (type?: string, categoryName?: string, entryPoint?: string) => {
      TrackingServices.trackingScreenView({
        screenName: TrackingScreenNames.ListBundle,
        entryPoint: entryPoint || TrackingScreenNames.bRewards,
        additionalInfo: {
          type,
          categoryName,
          userPoint: user?.point,
          userRank: user?.rankInfo?.rankName,
          country: isoCode,
        },
      });
    },
    [user?.point, user?.rankInfo?.rankName, isoCode],
  );

  /**
   * Track reward click events
   * @param rewardId - ID of the clicked reward
   * @param rewardName - Name of the clicked reward
   * @param rewardPoint - Points required for the reward
   * @param screenName - Screen where the click occurred
   * @param position - Position of the reward in the list (optional)
   */
  const trackingRewardClick = useCallback(
    (params: {
      rewardId: string;
      rewardName?: string;
      rewardPoint?: number;
      screenName: TrackingScreenNames;
      position?: number;
    }) => {
      const { rewardId, rewardName, rewardPoint, screenName, position } =
        params;

      TrackingServices.trackingButtonClick({
        screenName,
        action: TrackingActions.ViewDetail,
        additionalInfo: {
          rewardId,
          rewardName,
          rewardPoint,
          position,
          userPoint: user?.point,
          userRank: user?.rankInfo?.rankName,
          country: isoCode,
        },
      });

      // Also track the legacy reward clicked event
      TrackingServices.trackingRewardClicked({
        rewardName: rewardName,
        rankName: user?.rankInfo?.rankName,
        idReward: rewardId,
      });
    },
    [user?.point, user?.rankInfo?.rankName, isoCode],
  );

  /**
   * Track search actions
   * @param searchQuery - The search query entered by user
   * @param searchType - Type of search (text, filter, etc.)
   * @param resultsCount - Number of results returned (optional)
   */
  const trackingSearchAction = useCallback(
    (params: {
      searchQuery: string;
      searchType?: 'text' | 'filter' | 'recent';
      resultsCount?: number;
    }) => {
      const { searchQuery, searchType = 'text', resultsCount } = params;

      TrackingServices.trackingButtonClick({
        screenName: TrackingScreenNames.SearchResult,
        action: TrackingActions.Search,
        additionalInfo: {
          searchQuery,
          searchType,
          resultsCount,
          userPoint: user?.point,
          userRank: user?.rankInfo?.rankName,
          country: isoCode,
        },
      });
    },
    [user?.point, user?.rankInfo?.rankName, isoCode],
  );

  /**
   * Track point redeem click (when user clicks redeem button)
   * @param rewardId - ID of the reward being redeemed
   * @param rewardName - Name of the reward
   * @param rewardPoint - Points required for redemption
   * @param from - Source of the reward (bTaskee, partner, etc.)
   */
  const trackingPointRedeemClick = useCallback(
    (params: {
      rewardId: string;
      rewardName?: string;
      rewardPoint?: number;
      from?: string;
    }) => {
      const { rewardId, rewardName, rewardPoint, from } = params;

      TrackingServices.trackingPointRedeemClick({
        point: rewardPoint,
        rankName: user?.rankInfo?.rankName,
        rewardName: rewardName,
        from: from,
        idReward: rewardId,
      });
    },
    [user?.rankInfo?.rankName],
  );

  /**
   * Track navigation back actions
   * @param screenName - Current screen name
   * @param action - Navigation action (Back, Close, etc.)
   */
  const trackingNavigationAction = useCallback(
    (params: {
      screenName: TrackingScreenNames;
      action: TrackingActions;
      additionalInfo?: any;
    }) => {
      const { screenName, action, additionalInfo } = params;

      TrackingServices.trackingButtonClick({
        screenName,
        action,
        additionalInfo: {
          ...additionalInfo,
          userPoint: user?.point,
          userRank: user?.rankInfo?.rankName,
          country: isoCode,
        },
      });
    },
    [user?.point, user?.rankInfo?.rankName, isoCode],
  );

  /**
   * Track filter and sort actions
   * @param screenName - Screen where filter/sort was applied
   * @param filterType - Type of filter applied
   * @param filterValue - Value of the filter
   * @param sortType - Type of sort applied (optional)
   */
  const trackingFilterSortAction = useCallback(
    (params: {
      screenName: TrackingScreenNames;
      filterType?: string;
      filterValue?: string;
      sortType?: string;
    }) => {
      const { screenName, filterType, filterValue, sortType } = params;

      TrackingServices.trackingButtonClick({
        screenName,
        action: TrackingActions.Filter,
        additionalInfo: {
          filterType,
          filterValue,
          sortType,
          userPoint: user?.point,
          userRank: user?.rankInfo?.rankName,
          country: isoCode,
        },
      });
    },
    [user?.point, user?.rankInfo?.rankName, isoCode],
  );

  /**
   * Track member detail screen view
   * @param entryPoint - Where the user came from to reach this screen
   */
  const trackingMemberDetailScreenView = useCallback(
    (entryPoint?: string) => {
      TrackingServices.trackingScreenView({
        screenName: TrackingScreenNames.Account,
        entryPoint: entryPoint || TrackingScreenNames.bRewards,
        additionalInfo: {
          userPoint: user?.point,
          userRank: user?.rankInfo?.rankName,
          country: isoCode,
        },
      });
    },
    [user?.point, user?.rankInfo?.rankName, isoCode],
  );

  /**
   * Track history point screen view
   * @param entryPoint - Where the user came from to reach this screen
   */
  const trackingHistoryPointScreenView = useCallback(
    (entryPoint?: string) => {
      TrackingServices.trackingScreenView({
        screenName: TrackingScreenNames.TaskHistory,
        entryPoint: entryPoint || TrackingScreenNames.Account,
        additionalInfo: {
          userPoint: user?.point,
          userRank: user?.rankInfo?.rankName,
          country: isoCode,
        },
      });
    },
    [user?.point, user?.rankInfo?.rankName, isoCode],
  );

  /**
   * Track marketing campaign detail screen view
   * @param campaignId - ID of the campaign being viewed
   * @param entryPoint - Where the user came from to reach this screen
   */
  const trackingMarketingCampaignDetailScreenView = useCallback(
    (campaignId: string, entryPoint?: string) => {
      TrackingServices.trackingScreenView({
        screenName: TrackingScreenNames.MarketingCampaign,
        entryPoint: entryPoint || TrackingScreenNames.bRewards,
        additionalInfo: {
          campaignId,
          userPoint: user?.point,
          userRank: user?.rankInfo?.rankName,
          country: isoCode,
        },
      });
    },
    [user?.point, user?.rankInfo?.rankName, isoCode],
  );

  /**
   * Track see more button clicks
   * @param type - Type of rewards being viewed
   * @param title - Title of the section
   * @param screenName - Current screen name
   */
  const trackingSeeMoreClick = useCallback(
    (params: {
      type: string;
      title: string;
      screenName: TrackingScreenNames;
    }) => {
      const { type, title, screenName } = params;

      TrackingServices.trackingButtonClick({
        screenName,
        action: TrackingActions.ViewDetail,
        additionalInfo: {
          type,
          title,
          userPoint: user?.point,
          userRank: user?.rankInfo?.rankName,
          country: isoCode,
        },
      });
    },
    [user?.point, user?.rankInfo?.rankName, isoCode],
  );

  /**
   * Track recent search item clicks
   * @param searchQuery - The search query that was clicked
   */
  const trackingRecentSearchClick = useCallback(
    (searchQuery: string) => {
      TrackingServices.trackingButtonClick({
        screenName: TrackingScreenNames.SearchResult,
        action: TrackingActions.Search,
        additionalInfo: {
          searchQuery,
          searchType: 'recent',
          userPoint: user?.point,
          userRank: user?.rankInfo?.rankName,
          country: isoCode,
        },
      });
    },
    [user?.point, user?.rankInfo?.rankName, isoCode],
  );

  /**
   * Track clear search history action
   */
  const trackingClearSearchHistory = useCallback(() => {
    TrackingServices.trackingButtonClick({
      screenName: TrackingScreenNames.SearchResult,
      action: TrackingActions.Close,
      additionalInfo: {
        action: TrackingActions.ClearSearchHistory,
        userPoint: user?.point,
        userRank: user?.rankInfo?.rankName,
        country: isoCode,
      },
    });
  }, [user?.point, user?.rankInfo?.rankName, isoCode]);

  /**
   * Track error events (API failures, redemption errors, etc.)
   * @param errorType - Type of error that occurred
   * @param errorMessage - Error message
   * @param screenName - Screen where error occurred
   * @param additionalInfo - Additional error context
   */
  const trackingError = useCallback(
    (params: {
      errorType: string;
      errorMessage?: string;
      screenName: TrackingScreenNames;
      additionalInfo?: any;
    }) => {
      const { errorType, errorMessage, screenName, additionalInfo } = params;

      TrackingServices.trackingButtonClick({
        screenName,
        action: TrackingActions.TryAgain,
        additionalInfo: {
          errorType,
          errorMessage,
          ...additionalInfo,
          userPoint: user?.point,
          userRank: user?.rankInfo?.rankName,
          country: isoCode,
        },
      });
    },
    [user?.point, user?.rankInfo?.rankName, isoCode],
  );

  /**
   * Track external link clicks (social media, websites, app stores, etc.)
   * @param url - The URL being opened
   * @param screenName - Screen where link was clicked
   */
  const trackingExternalLinkClick = useCallback(
    (params: {
      url: string;
      screenName: TrackingScreenNames;
      rewardId?: string;
    }) => {
      const { url, screenName, rewardId } = params;

      TrackingServices.trackingButtonClick({
        screenName,
        action: TrackingActions.ViewDetail,
        additionalInfo: {
          url,
          rewardId,
          userPoint: user?.point,
          userRank: user?.rankInfo?.rankName,
          country: isoCode,
        },
      });
    },
    [user?.point, user?.rankInfo?.rankName, isoCode],
  );

  /**
   * Handle tracking back navigation following service-cleaning patterns
   * @param screenName - Current screen name
   * @param additionalInfo - Additional tracking information
   */
  const handleTrackingBack = useCallback(
    (screenName: TrackingScreenNames, additionalInfo?: any) => {
      if (!isFocused) {
        return null;
      }

      trackingNavigationAction({
        screenName,
        action: TrackingActions.Back,
        additionalInfo,
      });
    },
    [isFocused, trackingNavigationAction],
  );

  /**
   * Handle app state change tracking following service-cleaning patterns
   * @param nextAppState - Next app state
   * @param screenName - Current screen name
   */
  const handleAppStateChange = useCallback(
    (nextAppState: AppStateStatus, screenName: TrackingScreenNames) => {
      if (nextAppState === 'background' || nextAppState === 'inactive') {
        trackingNavigationAction({
          screenName,
          action: TrackingActions.Back,
          additionalInfo: {
            appState: nextAppState,
          },
        });
      }
    },
    [trackingNavigationAction],
  );

  /**
   * Setup navigation listeners for tracking back actions
   * @param screenName - Current screen name
   * @param additionalInfo - Additional tracking information
   */
  const setupNavigationTracking = useCallback(
    (screenName: TrackingScreenNames, additionalInfo?: any) => {
      // Setup beforeRemove listener
      const unsubscribeNavigation = navigation.addListener(
        'beforeRemove',
        () => {
          handleTrackingBack(screenName, additionalInfo);
        },
      );

      // Setup app state change listener
      const handleAppStateChangeWrapper = (nextAppState: AppStateStatus) => {
        handleAppStateChange(nextAppState, screenName);
      };

      const appStateSubscription = AppState.addEventListener(
        'change',
        handleAppStateChangeWrapper,
      );

      // Return cleanup function
      return () => {
        unsubscribeNavigation();
        appStateSubscription?.remove();
      };
    },
    [navigation, handleTrackingBack, handleAppStateChange],
  );

  return {
    // Screen view tracking
    trackingHomeScreenView,
    trackingSearchScreenView,
    trackingRewardDetailScreenView,
    trackingMyRewardScreenView,
    trackingAllRewardScreenView,
    trackingMemberDetailScreenView,
    trackingHistoryPointScreenView,
    trackingMarketingCampaignDetailScreenView,

    // User interaction tracking
    trackingRewardClick,
    trackingSearchAction,
    trackingNavigationAction,
    trackingFilterSortAction,
    trackingSeeMoreClick,
    trackingRecentSearchClick,
    trackingClearSearchHistory,

    // Reward redemption tracking
    trackingPointRedeemClick,

    // Error and performance tracking
    trackingError,
    trackingExternalLinkClick,

    // Navigation tracking (following service-cleaning patterns)
    handleTrackingBack,
    handleAppStateChange,
    setupNavigationTracking,
  };
};
