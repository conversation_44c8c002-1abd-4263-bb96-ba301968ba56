import { useCallback, useMemo } from 'react';
import {
  getTextWithLocale,
  RewardDetailItem,
  useCheckSignIn,
  useReward,
  useUserStore,
} from '@btaskee/design-system';
import { useAppNavigation, useI18n, useTracking } from '@src/hooks';
import { isNil } from 'lodash-es';

const useFooterContent = (data?: RewardDetailItem) => {
  const { t, i18n } = useI18n();
  const navigation = useAppNavigation();
  const { trackingPointRedeemClick } = useTracking();
  const { redeemGiftReward } = useReward();

  const { user } = useUserStore();

  const { onHandleCheckSignIn } = useCheckSignIn();

  const userPoint = user?.point;
  const giftPoint = data?.point || 0;

  const isEnableRedeem = useMemo(() => {
    if (isNil(userPoint)) {
      return true;
    }
    return giftPoint <= userPoint;
  }, [userPoint, giftPoint]);

  /**
   * Handle redeem button click with tracking
   */
  const handleRedeemClick = useCallback(() => {
    // Track point redeem click
    if (data?._id) {
      trackingPointRedeemClick({
        rewardId: data?._id,
        rewardName: getTextWithLocale(data?.title),
        rewardPoint: giftPoint,
      });
      redeemGiftReward({ rewardDetail: data });
    }
  }, [data, redeemGiftReward, trackingPointRedeemClick, giftPoint]);

  return {
    navigation,
    t,
    i18n,
    userPoint,
    isEnableRedeem,
    onHandleCheckSignIn,
    handleRedeemClick,
  };
};

export default useFooterContent;
