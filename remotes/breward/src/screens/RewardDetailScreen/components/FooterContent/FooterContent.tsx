import React, { memo, useCallback, useMemo } from 'react';
import {
  <PERSON><PERSON>,
  BlockView,
  Colors,
  CText,
  <PERSON>ceHelper,
  FontSizes,
  Lottie,
  PrimaryButton,
  RewardDetailItem,
  Spacing,
  Trans,
} from '@btaskee/design-system';
import { dealLottie } from '@src/assets/lotties';
import { NAME_SPACE } from '@src/hooks';

import useFooterContent from './hook';
import styles from './styles';

interface FooterContentProps {
  isFetching: boolean;
  data?: RewardDetailItem;
}

const FooterContent = ({ isFetching, data }: FooterContentProps) => {
  const { t, isEnableRedeem, onHandleCheckSignIn, handleRedeemClick } =
    useFooterContent(data);

  const RedeemBottom = useMemo(() => {
    return (
      <BlockView
        center
        padding={Spacing.SPACE_16}
      >
        <Lottie
          source={dealLottie}
          autoPlay={true}
          count={3}
          style={styles.image}
        />
        <CText
          size={FontSizes.SIZE_16}
          bold
          color={Colors.neutral800}
          margin={{ top: Spacing.SPACE_24 }}
        >
          {t('REDEEM')}
        </CText>
        <CText
          margin={{ top: Spacing.SPACE_08 }}
          size={FontSizes.SIZE_14}
          color={Colors.neutral400}
          center
          width={252 * DeviceHelper.WIDTH_RATIO}
        >
          <Trans
            i18nKey={'CONFIRM_REDEEM'}
            ns={NAME_SPACE}
            values={{ point: data?.point || 0 }}
            components={{
              tag: (
                <CText
                  color={Colors.orange500}
                  bold
                />
              ),
            }}
          />
        </CText>
      </BlockView>
    );
  }, [t, data?.point]);

  const onPressRedeem = useCallback(() => {
    Alert.alert.open({
      message: RedeemBottom,
      actions: [
        {
          text: t('CANCEL'),
          style: 'cancel',
        },
        {
          text: t('CONFIRM'),
          onPress: handleRedeemClick,
        },
      ],
    });
  }, [RedeemBottom, t, handleRedeemClick]);

  return (
    <BlockView
      inset={'bottom'}
      backgroundColor={Colors.neutralWhite}
      padding={Spacing.SPACE_16}
    >
      <PrimaryButton
        onPress={() => onHandleCheckSignIn(onPressRedeem)}
        disabled={isFetching || !isEnableRedeem}
        title={t('REDEEM_VOUCHER')}
      />
    </BlockView>
  );
};

export default memo(FooterContent);
