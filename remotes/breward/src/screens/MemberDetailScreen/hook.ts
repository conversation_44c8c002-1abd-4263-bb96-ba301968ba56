import { useEffect } from 'react';
import { TrackingScreenNames } from '@btaskee/design-system';
import { useAppNavigation, useI18n, useTracking } from '@src/hooks';

const useMemberDetailScreen = () => {
  const { t } = useI18n();
  const navigation = useAppNavigation();
  const { trackingMemberDetailScreenView, setupNavigationTracking } =
    useTracking();

  // Track screen view on component mount
  useEffect(() => {
    trackingMemberDetailScreenView();
  }, [trackingMemberDetailScreenView]);

  // Setup navigation tracking
  useEffect(() => {
    const cleanup = setupNavigationTracking(TrackingScreenNames.Account, {
      screenType: TrackingScreenNames.MemberDetail,
    });
    return cleanup;
  }, [setupNavigationTracking]);

  return { navigation, t };
};

export default useMemberDetailScreen;
