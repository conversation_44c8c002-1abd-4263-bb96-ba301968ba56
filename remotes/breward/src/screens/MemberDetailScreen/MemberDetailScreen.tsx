import React, { memo } from 'react';
import { BlockView, ScrollView, Spacing } from '@btaskee/design-system';

import ProgressLevel from './components/ProgressLevel';
import RankLevel from './components/RankLevel';
import RecommendBlock from './components/RecommendBlock';
import useMemberDetailScreen from './hook';

export const MemberDetailScreen = memo(() => {
  useMemberDetailScreen();

  return (
    <ScrollView showsVerticalScrollIndicator={false}>
      <BlockView
        padding={Spacing.SPACE_16}
        inset={'bottom'}
      >
        <ProgressLevel />
        <RankLevel />
        <RecommendBlock />
      </BlockView>
    </ScrollView>
  );
});
