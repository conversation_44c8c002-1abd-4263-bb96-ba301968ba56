import React, { memo } from 'react';
import { ImageBackground } from 'react-native';
import {
  BlockView,
  ConditionView,
  CText,
  FastImage,
  formatMoney,
  imgFlashSale,
  TouchableOpacity,
} from '@btaskee/design-system';
import { useI18n } from '@src/hooks';

import { icBadge } from '@images';

import styles from './styles';

interface RewardItemProps {
  image: string;
  title: string;
  brandName: string;
  point: number;
  onPress: () => void;
  index?: number;
  style?: object;
  originalPoint?: number;
  discountPercent?: number;
  iconRight?: any;
}

const RewardItem: React.FC<RewardItemProps> = ({
  image,
  title,
  brandName,
  point,
  onPress,
  index,
  style,
  originalPoint,
  discountPercent,
  iconRight,
}) => {
  const { t } = useI18n();

  return (
    <BlockView
      style={[styles.boxSlide, style]}
      testID={`rewardSearch${index}`}
    >
      <TouchableOpacity
        testID={'itemReward_' + index}
        style={styles.slideItem}
        onPress={onPress}
      >
        <ImageBackground
          source={{ uri: image }}
          style={styles.imageItem}
          imageStyle={styles.imageStyle}
          resizeMode="cover"
        >
          <ConditionView
            condition={!!discountPercent}
            viewTrue={
              <ImageBackground
                source={imgFlashSale}
                style={styles.imageFlashSale}
                resizeMode="stretch"
              >
                <CText style={styles.txtSale}>{t('DISCOUNT_REWARD')}</CText>
                <CText
                  style={styles.txtPercent}
                  bold
                >
                  {discountPercent + '%'}
                </CText>
              </ImageBackground>
            }
          />
          <ConditionView
            condition={Boolean(iconRight)}
            viewTrue={
              <FastImage
                source={iconRight}
                style={styles.imageRight}
                resizeMode="cover"
              />
            }
          />
        </ImageBackground>
        <BlockView
          flex
          style={styles.boxContent}
        >
          <CText numberOfLines={2}>{title}</CText>
          <ConditionView
            condition={!!brandName}
            viewTrue={
              <CText
                flex
                numberOfLines={1}
                style={styles.branchNameTxt}
              >
                {brandName}
              </CText>
            }
          />
          <BlockView
            row
            style={styles.boxPoint}
          >
            <BlockView
              row
              center
            >
              <FastImage
                style={styles.iconBadge}
                source={icBadge}
              />
              <CText
                bold
                style={styles.txtPoint}
              >
                {formatMoney(point)}
              </CText>
              <ConditionView
                condition={!!originalPoint}
                viewTrue={
                  <CText
                    numberOfLines={1}
                    style={styles.linePoint}
                  >
                    {formatMoney(originalPoint)}
                  </CText>
                }
              />
            </BlockView>
          </BlockView>
        </BlockView>
      </TouchableOpacity>
    </BlockView>
  );
};

export default memo(RewardItem);
