import { StyleSheet } from 'react-native';
import {
  Colors,
  DeviceHelper,
  FontSizes,
  Spacing,
} from '@btaskee/design-system';

const IMAGE_SIZE = Math.round(
  (DeviceHelper.WINDOW.WIDTH - Spacing.SPACE_16 * 2) * 0.55,
);

export default StyleSheet.create({
  boxSlide: {
    paddingVertical: Spacing.SPACE_12,
    borderBottomColor: Colors.neutral100,
    borderBottomWidth: 1,
  },
  slideItem: {
    backgroundColor: Colors.neutralWhite,
    flexDirection: 'row',
    borderRadius: 10,
  },
  imageItem: {
    width: IMAGE_SIZE,
    resizeMode: 'contain',
    height: IMAGE_SIZE / 2,
    aspectRatio: 2,
  },
  imageStyle: { borderRadius: 10 },
  boxContent: {
    justifyContent: 'space-between',
    paddingHorizontal: Spacing.SPACE_12,
  },
  branchNameTxt: {
    fontSize: FontSizes.SIZE_12,
    color: Colors.neutral500,
    marginTop: Spacing.SPACE_04,
  },
  boxPoint: {
    marginTop: Spacing.SPACE_04,
  },
  txtPoint: {
    color: Colors.orange500,
    marginLeft: Spacing.SPACE_04,
  },
  linePoint: {
    textDecorationLine: 'line-through',
    color: Colors.neutral500,
    textAlign: 'center',
    marginLeft: Spacing.SPACE_04,
  },
  imageFlashSale: {
    width: 50,
    height: 50,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: Spacing.SPACE_16,
    paddingBottom: Spacing.SPACE_08,
  },
  txtSale: {
    color: Colors.neutralWhite,
    fontSize: FontSizes.SIZE_12,
  },
  txtPercent: {
    color: Colors.neutralWhite,
    fontSize: FontSizes.SIZE_12,
  },
  imageRight: {
    width: 28,
    height: 28,
    alignSelf: 'flex-end',
    margin: Spacing.SPACE_04,
  },
  iconBadge: {
    width: 14,
    height: 14,
  },
});
