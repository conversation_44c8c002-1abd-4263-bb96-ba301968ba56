import React, { memo } from 'react';
import { ImageSourcePropType } from 'react-native';
import {
  BlockView,
  CText,
  FontSizes,
  IconImage,
  Spacing,
  TouchableOpacity,
} from '@btaskee/design-system';

import styles from './styles';

interface BlockButtonProps {
  icon: ImageSourcePropType;
  label: string;
  onPress: () => void;
}

export const BlockButton: React.FC<BlockButtonProps> = ({
  icon,
  label,
  onPress,
}) => {
  return (
    <TouchableOpacity
      flex
      center
      onPress={onPress}
    >
      <BlockView
        center
        style={styles.iconContainer}
      >
        <IconImage
          source={icon}
          size={30}
        />
      </BlockView>
      <CText
        center
        size={FontSizes.SIZE_12}
        padding={{ horizontal: Spacing.SPACE_04 }}
      >
        {label}
      </CText>
    </TouchableOpacity>
  );
};

export default memo(BlockButton);
