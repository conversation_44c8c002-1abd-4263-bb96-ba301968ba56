import React, { memo } from 'react';
import {
  BlockView,
  BRewardRouteName,
  Colors,
  ConditionView,
  CText,
  EndpointKeys,
  FontSizes,
  getTextWithLocale,
  RewardGroupType,
  SizedBox,
  Spacing,
  TouchableOpacity,
  useApiQuery,
  useAppStore,
} from '@btaskee/design-system';
import { useAppNavigation, useI18n } from '@src/hooks';

import RewardItem from '../RewardItem';
import RecommendBlockSkeleton from './SkeletonLoading/SkeletonLoading';

const RecommendBlock = () => {
  const navigation = useAppNavigation();
  const { t } = useI18n();
  const { isoCode } = useAppStore();

  const { data, isFetching } = useApiQuery({
    key: EndpointKeys.getRewardsForYou,
    params: { isoCode },
  });

  const onViewMore = () => {
    navigation.navigate(BRewardRouteName.AllReward, {
      title: t('RECOMMEND_FOR_YOU'),
      type: RewardGroupType.RECOMMEND_FOR_YOU,
    });
  };

  const onPressItem = (id: string) => {
    navigation.navigate(BRewardRouteName.RewardDetail, { rewardId: id });
  };

  if (isFetching) {
    return <RecommendBlockSkeleton />;
  }

  return (
    <ConditionView
      condition={!!data?.length}
      viewTrue={
        <BlockView margin={{ top: Spacing.SPACE_32 }}>
          <CText
            bold
            size={FontSizes.SIZE_20}
          >
            {t('RECOMMEND_FOR_YOU')}
          </CText>
          <SizedBox height={Spacing.SPACE_08} />
          <BlockView>
            {data?.map((item, index) => {
              const isLastItem = index === data.length - 1;
              const borderBottomWidth = isLastItem ? 0 : 1;
              return (
                <RewardItem
                  key={index}
                  image={item.image}
                  point={item.point}
                  title={getTextWithLocale(item.title)}
                  brandName={getTextWithLocale(item.brandText)}
                  onPress={() => onPressItem(item?._id)}
                  style={{ borderBottomWidth }}
                  originalPoint={item?.originalPoint}
                  discountPercent={item?.discountPercent}
                />
              );
            })}
          </BlockView>
          <SizedBox height={Spacing.SPACE_08} />
          <TouchableOpacity onPress={onViewMore}>
            <CText
              bold
              center
              color={Colors.green500}
            >
              {t('SEE_MORE')}
            </CText>
          </TouchableOpacity>
        </BlockView>
      }
    />
  );
};

export default memo(RecommendBlock);
