import React, { memo } from 'react';
import { BlockView, SkeletonBox, Spacing } from '@btaskee/design-system';

import { styles } from './styles';

const RecommendBlockSkeleton: React.FC = () => {
  return (
    <BlockView margin={{ top: Spacing.SPACE_32 }}>
      <SkeletonBox style={styles.titleSkeleton} />

      {Array.from({ length: 3 }, (_, index) => (
        <BlockView
          key={index}
          row
          gap={Spacing.SPACE_12}
          margin={{ bottom: Spacing.SPACE_16 }}
        >
          <SkeletonBox style={styles.itemImage} />

          <BlockView
            flex
            jBetween
          >
            <BlockView>
              <SkeletonBox style={styles.itemTitle} />
              <SkeletonBox style={styles.itemBrand} />
            </BlockView>

            <SkeletonBox style={styles.itemPoints} />
          </BlockView>
        </BlockView>
      ))}

      <SkeletonBox style={styles.seeMoreSkeleton} />
    </BlockView>
  );
};

export default memo(RecommendBlockSkeleton);
