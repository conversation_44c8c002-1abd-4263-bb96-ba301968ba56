import { StyleSheet } from 'react-native';
import { BorderRadius, Spacing } from '@btaskee/design-system';

export const styles = StyleSheet.create({
  titleSkeleton: {
    width: '60%',
    height: 24,
    borderRadius: BorderRadius.RADIUS_04,
    marginBottom: Spacing.SPACE_16,
  },
  itemImage: {
    width: 80,
    height: 80,
    borderRadius: BorderRadius.RADIUS_08,
  },
  itemTitle: {
    width: '90%',
    height: 16,
    borderRadius: BorderRadius.RADIUS_04,
    marginBottom: Spacing.SPACE_08,
  },
  itemBrand: {
    width: '60%',
    height: 14,
    borderRadius: BorderRadius.RADIUS_04,
  },
  itemPoints: {
    width: 80,
    height: 20,
    borderRadius: BorderRadius.RADIUS_04,
    alignSelf: 'flex-end',
  },
  seeMoreSkeleton: {
    width: 100,
    height: 20,
    borderRadius: BorderRadius.RADIUS_04,
    alignSelf: 'center',
    marginTop: Spacing.SPACE_08,
  },
});
