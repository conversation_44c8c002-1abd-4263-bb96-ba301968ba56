import { useEffect } from 'react';
import { EndpointKeys, handleError, useApiQuery } from '@btaskee/design-system';
import { useAppNavigation, useI18n } from '@src/hooks';

const useProgressLevel = () => {
  const { t } = useI18n();
  const navigation = useAppNavigation();

  const { data, isFetching, error } = useApiQuery({
    key: EndpointKeys.getMemberInfo,
    params: {},
  });

  useEffect(() => {
    if (error) {
      handleError(error, navigation.goBack);
    }
  }, [error, navigation.goBack]);

  return { navigation, t, data, isFetching, memberInfo: data };
};

export default useProgressLevel;
