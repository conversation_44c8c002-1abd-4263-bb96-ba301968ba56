import React, { memo, useMemo } from 'react';
import { ImageBackground } from 'react-native';
import * as Progress from 'react-native-progress';
import {
  BlockView,
  BorderRadius,
  BRewardRouteName,
  Colors,
  ConditionView,
  CText,
  DeviceHelper,
  FastImage,
  FontSizes,
  formatMoney,
  getTextWithLocale,
  icBRewardBook,
  icBRewardHistory,
  icBRewardTicket,
  IconImage,
  icTimeOut,
  NavigationService,
  RANK_TYPE,
  RouteName,
  SizedBox,
  Spacing,
  useSettingsStore,
} from '@btaskee/design-system';

import { icBadge } from '@images';

import BlockButton from '../BlockButon';
import useProgressLevel from './hook';
import ProgressLevelSkeleton from './ProgressLevelSkeleton';
import styles from './styles';

interface ProgressLevelProps {}

function ProgressLevel({}: ProgressLevelProps) {
  const { settings } = useSettingsStore();
  const { memberInfo, isFetching, t, navigation } = useProgressLevel();

  const data = useMemo(() => {
    const rankSetting = (settings?.settingSystem?.rankSetting || []).sort(
      (a, b) => {
        return (a?.point ?? 0) - (b?.point ?? 0);
      },
    );

    const currentPoint = memberInfo?.currentPoint || 0;
    const currentPointRankInfo = memberInfo?.currentRankInfo?.point || 0;
    const currentRankInfo = memberInfo?.currentRankInfo;
    const nextRank = memberInfo?.nextRankInfo;

    const bPointResetNote = getTextWithLocale(memberInfo?.bPointResetNote);
    const rankName = getTextWithLocale(memberInfo?.currentRankInfo?.text);
    const bPointNote = getTextWithLocale(memberInfo?.bPointNote);

    const imageBackground =
      Object.values(RANK_TYPE).find((e) => e.key === currentRankInfo?.rankName)
        ?.imageBackground || RANK_TYPE.MEMBER.imageBackground;

    const iconNextRank = Object.values(RANK_TYPE).find(
      (e) => e.key === nextRank?.rankName,
    )?.icon;

    return {
      rankName,
      imageBackground,
      nextRank,
      bPointNote,
      currentPoint,
      currentPointRankInfo,
      bPointResetNote,
      rankSetting,
      iconNextRank,
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [memberInfo?.currentPoint, memberInfo?.currentRankInfo]);

  const _goHistoryPoints = () => {
    navigation.navigate(BRewardRouteName.HistoryPoints);
  };

  const _goGift = () => {
    const state = navigation.getState();
    const routes = state.routes;
    const currentIndex = state.index;
    // Lấy route ngay trước màn hiện tại
    const prevRoute = routes[currentIndex - 1];

    if (prevRoute?.name === BRewardRouteName.Home) {
      navigation.goBack();
    } else {
      navigation.navigate(BRewardRouteName.Home);
    }
  };

  const _openRewardWeb = () => {
    if (data.rankSetting.length) {
      const webLink = data.rankSetting[0]?.webLink?.url;
      if (webLink) {
        NavigationService.navigate(RouteName.WebView, {
          source: { uri: getTextWithLocale(webLink) },
          title: t('MENU_REWARDS'),
        });
      }
    }
  };

  return (
    <BlockView>
      <ConditionView
        condition={isFetching}
        viewTrue={<ProgressLevelSkeleton />}
        viewFalse={
          <>
            <ImageBackground
              source={data.imageBackground}
              resizeMode="cover"
              style={styles.imageBackground}
            >
              <BlockView
                style={styles.container}
                gap={Spacing.SPACE_16}
              >
                <CText
                  bold
                  size={FontSizes.SIZE_20}
                >
                  {data?.rankName}
                </CText>
                <BlockView
                  row
                  horizontal
                >
                  <FastImage
                    source={icBadge}
                    style={styles.iconBadge}
                  />
                  <BlockView
                    row
                    horizontal
                  >
                    <CText
                      bold
                      size={FontSizes.SIZE_24}
                      style={styles.bPointTxt}
                    >
                      {formatMoney(data?.currentPoint)}
                    </CText>
                    <CText size={FontSizes.SIZE_16}>{t('B_POINT')}</CText>
                  </BlockView>
                </BlockView>
                <BlockView
                  row
                  horizontal
                >
                  <Progress.Bar
                    progress={
                      data.currentPointRankInfo /
                      (data?.nextRank?.point ?? data?.currentPointRankInfo)
                    }
                    borderWidth={0}
                    height={6}
                    borderRadius={BorderRadius.RADIUS_04}
                    width={313 * DeviceHelper.WIDTH_RATIO}
                    color={Colors.orange500}
                    style={{ backgroundColor: Colors.neutralWhite }}
                  />
                  <ConditionView
                    condition={data?.iconNextRank}
                    viewTrue={
                      <FastImage
                        source={data.iconNextRank}
                        style={styles.iconNextRank}
                      />
                    }
                  />
                </BlockView>

                <CText size={FontSizes.SIZE_14}>{data.bPointNote}</CText>
              </BlockView>
            </ImageBackground>
            {/* block notify expired bPoint */}
            <ConditionView
              condition={!!data?.bPointResetNote}
              viewTrue={
                <BlockView
                  row
                  horizontal
                  style={styles.pointExpiredContainer}
                >
                  <IconImage
                    source={icTimeOut}
                    size={20}
                  />
                  <SizedBox width={Spacing.SPACE_08} />
                  <CText>
                    <CText
                      bold
                      size={FontSizes.SIZE_12}
                      style={[styles.bPointTxt]}
                    >
                      {data?.bPointResetNote}
                    </CText>
                  </CText>
                </BlockView>
              }
            />
          </>
        }
      />
      <SizedBox height={Spacing.SPACE_32} />
      <BlockView
        row
        jBetween
        left
      >
        <BlockButton
          icon={icBRewardTicket}
          label={t('LBL_SHOP_GIFT')}
          onPress={_goGift}
        />
        <BlockButton
          icon={icBRewardHistory}
          label={t('HISTORY_OF_POINT')}
          onPress={_goHistoryPoints}
        />
        <BlockButton
          icon={icBRewardBook}
          label={t('ACCUMULATE_REWARD_POINT')}
          onPress={_openRewardWeb}
        />
      </BlockView>
    </BlockView>
  );
}
export default memo(ProgressLevel);
