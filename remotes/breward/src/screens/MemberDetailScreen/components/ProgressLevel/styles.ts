import { StyleSheet } from 'react-native';
import { BorderRadius, Colors, Spacing } from '@btaskee/design-system';

export default StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: Spacing.SPACE_16,
    paddingVertical: Spacing.SPACE_20,
  },

  imageBackground: {
    width: '100%',
    borderRadius: 15,
    overflow: 'hidden',
  },
  bPointTxt: {
    color: Colors.orange500,
    marginLeft: Spacing.SPACE_08,
    marginRight: Spacing.SPACE_04,
  },
  pointExpiredContainer: {
    marginTop: Spacing.SPACE_16,
    backgroundColor: Colors.orange100,
    paddingVertical: Spacing.SPACE_12,
    paddingHorizontal: Spacing.SPACE_16,
    borderRadius: BorderRadius.RADIUS_04,
  },
  iconNextRank: {
    width: 20,
    height: 20,
    position: 'absolute',
    right: 0,
  },
  iconBadge: {
    width: 30,
    height: 30,
  },
});
