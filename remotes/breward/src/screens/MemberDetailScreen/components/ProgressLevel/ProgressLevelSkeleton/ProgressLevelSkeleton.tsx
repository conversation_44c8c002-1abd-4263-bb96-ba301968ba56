import React, { memo } from 'react';
import { BlockView, SkeletonBox } from '@btaskee/design-system';

import { styles } from './styles';

const ProgressLevelSkeleton: React.FC = () => {
  return (
    <BlockView>
      {/* Main background container skeleton */}
      <SkeletonBox style={styles.imageBackground} />

      {/* Point expired notice skeleton */}
      <SkeletonBox style={styles.pointExpiredContainer} />
    </BlockView>
  );
};

export default memo(ProgressLevelSkeleton);
