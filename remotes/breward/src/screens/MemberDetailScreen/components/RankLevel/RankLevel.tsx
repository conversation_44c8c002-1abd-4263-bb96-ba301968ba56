import React, { memo, useEffect, useState } from 'react';
import {
  BlockView,
  Colors,
  CText,
  getTextWithLocale,
  IconImage,
  IRankSetting,
  NavigationService,
  RouteName,
  SizedBox,
  Spacing,
  TouchableOpacity,
  useSettingsStore,
  useUserStore,
} from '@btaskee/design-system';
import { useI18n } from '@src/hooks';

import styles from './styles';

const RewardImageLevel = ({
  title,
  isActive,
  onPress,
}: {
  title: string;
  isActive: boolean;
  onPress: () => void;
}) => {
  return (
    <TouchableOpacity
      onPress={onPress}
      flex
      center
      padding={{ horizontal: Spacing.SPACE_04, vertical: Spacing.SPACE_16 }}
      border={{
        bottom: {
          width: 1,
          color: isActive ? Colors.orange500 : Colors.neutral100,
        },
      }}
    >
      <BlockView
        flex
        center
      >
        <CText
          bold={isActive}
          center
          numberOfLines={2}
        >
          {title}
        </CText>
      </BlockView>
    </TouchableOpacity>
  );
};

const RankLevel = () => {
  const { t } = useI18n();
  const { settings } = useSettingsStore();
  const { getRankUser } = useUserStore();

  const rankSetting = (settings?.settingSystem?.rankSetting || []).sort(
    (a, b) => {
      return (a?.point ?? 0) - (b?.point ?? 0);
    },
  );

  const [itemSelected, setItemSelected] = useState<IRankSetting | undefined>(
    undefined,
  );

  useEffect(() => {
    const currentRank = getRankUser();

    const itemMatch = rankSetting.find(
      (item) => item?.rankName === currentRank?.rankName,
    );
    setItemSelected(itemMatch);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const _goSeeMore = () => {
    const webLink = itemSelected?.webLink;
    if (webLink?.url) {
      NavigationService.navigate(RouteName.WebView, {
        source: { uri: getTextWithLocale(webLink?.url) },
        title: t('MENU_REWARDS'),
      });
    }
  };

  if (!rankSetting?.length) {
    return null;
  }

  return (
    <BlockView style={styles.container}>
      <BlockView row>
        {rankSetting?.map((item, index) => {
          const isActive = itemSelected?.rankName === item?.rankName;
          return (
            <RewardImageLevel
              key={index}
              title={getTextWithLocale(item?.text)}
              isActive={isActive}
              onPress={() => {
                setItemSelected(item);
              }}
            />
          );
        })}
      </BlockView>
      <BlockView style={styles.contentContainer}>
        <CText>
          {t('INCENTIVES_FOR', {
            t: getTextWithLocale(itemSelected?.text),
          })}
        </CText>
        <SizedBox height={Spacing.SPACE_20} />

        {itemSelected?.privilege?.map((item, index) => {
          return (
            <BlockView
              row
              center
              key={index}
              style={styles.itemPrivilege}
            >
              <IconImage
                style={styles.iconRank}
                color={itemSelected.color}
                source={{ uri: item.icon }}
              />
              <SizedBox width={Spacing.SPACE_12} />
              <CText
                flex
                color={Colors.neutral300}
              >
                {getTextWithLocale(item.text)}
              </CText>
            </BlockView>
          );
        })}
        <CText
          flex
          color={Colors.orange500}
        >
          {t('REDEEM_POINTS_NOT_AFFECT')}
        </CText>
      </BlockView>
      <BlockView style={styles.boxFooterRankLevel}>
        <TouchableOpacity onPress={_goSeeMore}>
          <CText
            bold
            color={Colors.green500}
          >
            {t('SEE_MORE')}
          </CText>
        </TouchableOpacity>
      </BlockView>
    </BlockView>
  );
};

export default memo(RankLevel);
