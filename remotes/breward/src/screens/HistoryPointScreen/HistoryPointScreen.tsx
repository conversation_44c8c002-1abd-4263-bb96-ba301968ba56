import React, { memo, useCallback, useMemo } from 'react';
import { BlockView, Colors, CText, FontSizes } from '@btaskee/design-system';
import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';

import ReceivedPointTab from './components/ReceivedPointTab';
import UsedPointTab from './components/UsedPointTab';
import useHistoryPointScreen from './hook';

const Tab = createMaterialTopTabNavigator();
export const HistoryPointScreen = memo(() => {
  const { t } = useHistoryPointScreen();

  const LIST_TABS = useMemo(
    () => [
      { label: t('RECEIVED_POINTS'), Component: ReceivedPointTab },
      { label: t('USED_POINTS'), Component: UsedPointTab },
    ],
    [t],
  );

  const renderTabBar = useCallback(
    ({
      children,
      color,
      focused,
    }: {
      focused: boolean;
      color: string;
      children: string;
    }) => {
      return (
        <CText
          size={FontSizes.SIZE_14}
          color={focused ? Colors.orange500 : color}
          bold={focused}
        >
          {children}
        </CText>
      );
    },
    [],
  );

  return (
    <BlockView flex={1}>
      <Tab.Navigator
        screenOptions={{
          lazy: true,
          tabBarAllowFontScaling: false,
          tabBarActiveTintColor: Colors.orange500,
          tabBarInactiveTintColor: Colors.neutral500,
          tabBarIndicatorStyle: {
            backgroundColor: Colors.orange500,
          },
          tabBarLabelStyle: {
            fontSize: FontSizes.SIZE_14,
          },
          tabBarLabel: renderTabBar,
        }}
      >
        {LIST_TABS.map(({ Component, label }, index) => {
          return (
            <Tab.Screen
              key={index}
              name={label}
            >
              {() => <Component />}
            </Tab.Screen>
          );
        })}
      </Tab.Navigator>
    </BlockView>
  );
});
