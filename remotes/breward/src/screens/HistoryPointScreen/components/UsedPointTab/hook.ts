import { EndpointKeys, useApiQuery, useAppStore } from '@btaskee/design-system';
import { useAppNavigation, useI18n } from '@src/hooks';

const useUsedPointTab = () => {
  const { t } = useI18n();
  const navigation = useAppNavigation();
  const { isoCode } = useAppStore();
  const { data, isFetching } = useApiQuery({
    key: EndpointKeys.getListPointTransactionUsed,
    params: { isoCode },
  });

  return { navigation, t, data: data || [], isFetching };
};

export default useUsedPointTab;
