import React, { memo } from 'react';
import {
  BlockView,
  Card,
  Colors,
  ConditionView,
  ScrollView,
  Spacing,
} from '@btaskee/design-system';
import EmptyRewards from '@src/components/EmptyRewards';
import { isEmpty } from 'lodash-es';

import { imgEmptyTransaction } from '@images';

import ReceivedPointItem from '../ReceivedPointItem';
import ReceivedPointSkeleton from '../ReceivedPointSkeleton';
import useUsedPointTab from './hook';

const UsedPointTab = () => {
  const { t, data, isFetching } = useUsedPointTab();

  if (isFetching) return <ReceivedPointSkeleton />;

  return (
    <BlockView
      flex={1}
      backgroundColor={Colors.neutralBackground}
    >
      <ConditionView
        condition={isEmpty(data)}
        viewTrue={
          <EmptyRewards
            title={t('NO_TRANSACTION_TITLE')}
            source={imgEmptyTransaction}
          />
        }
        viewFalse={
          <ScrollView showsVerticalScrollIndicator={false}>
            <BlockView inset={'bottom'}>
              <Card
                margin={Spacing.SPACE_16}
                padding={Spacing.SPACE_0}
              >
                {data.map((item, index) => (
                  <ReceivedPointItem
                    key={item._id}
                    from="usedPointsScreen"
                    historyPoint={item}
                    highlighted={data.length - 1 !== index}
                  />
                ))}
              </Card>
            </BlockView>
          </ScrollView>
        }
      />
    </BlockView>
  );
};

export default memo(UsedPointTab);
