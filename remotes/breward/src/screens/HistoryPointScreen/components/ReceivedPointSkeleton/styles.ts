import { StyleSheet } from 'react-native';
import { BorderRadius, Spacing } from '@btaskee/design-system';

export const styles = StyleSheet.create({
  // Icon skeleton inside the circular container
  iconSkeleton: {
    width: 20,
    height: 20,
    borderRadius: BorderRadius.RADIUS_04,
  },

  // Transaction title skeleton
  titleSkeleton: {
    height: Spacing.SPACE_16,
    width: '80%',
    borderRadius: BorderRadius.RADIUS_04,
  },

  // Transaction description skeleton (shorter line)
  descriptionSkeleton: {
    height: Spacing.SPACE_16,
    width: '65%',
    borderRadius: BorderRadius.RADIUS_04,
  },

  // Transaction date skeleton
  dateSkeleton: {
    height: Spacing.SPACE_16,
    width: '50%',
    borderRadius: BorderRadius.RADIUS_04,
  },

  // Point amount skeleton (right side)
  pointAmountSkeleton: {
    height: Spacing.SPACE_16,
    width: 60,
    borderRadius: BorderRadius.RADIUS_04,
  },

  // Point unit skeleton (smaller text)
  pointUnitSkeleton: {
    height: Spacing.SPACE_16,
    width: 30,
    borderRadius: BorderRadius.RADIUS_04,
  },
});
