import React, { memo } from 'react';
import {
  BlockView,
  BorderRadius,
  Card,
  Colors,
  ScrollView,
  SkeletonBox,
  Spacing,
} from '@btaskee/design-system';

import { styles } from './styles';

/**
 * Individual skeleton item for received point transaction
 * Purpose: Shows skeleton structure of a single point transaction while loading
 * @param showBorder - Whether to show bottom border separator
 * @returns {JSX.Element} Skeleton item matching ReceivedPointItem layout
 */
const ReceivedPointItemSkeleton = memo(
  ({ showBorder = true }: { showBorder?: boolean }) => {
    return (
      <BlockView
        row
        horizontal
        jBetween
        padding={Spacing.SPACE_16}
        border={{
          bottom: {
            width: showBorder ? 1 : 0,
            color: Colors.neutral100,
          },
        }}
      >
        {/* Left side - Icon and transaction info */}
        <BlockView
          row
          horizontal
          gap={Spacing.SPACE_12}
          flex
        >
          {/* Transaction type icon skeleton */}
          <BlockView
            width={40}
            height={40}
            radius={BorderRadius.RADIUS_FULL}
            backgroundColor={Colors.orange50}
            center
          >
            <SkeletonBox style={styles.iconSkeleton} />
          </BlockView>

          {/* Transaction details */}
          <BlockView
            flex
            gap={Spacing.SPACE_04}
          >
            {/* Transaction title skeleton */}
            <SkeletonBox style={styles.titleSkeleton} />

            {/* Transaction description skeleton */}
            <SkeletonBox style={styles.descriptionSkeleton} />

            {/* Transaction date skeleton */}
            <SkeletonBox style={styles.dateSkeleton} />
          </BlockView>
        </BlockView>

        {/* Right side - Point amount */}
        <BlockView
          align="flex-end"
          gap={Spacing.SPACE_04}
        >
          {/* Point amount skeleton */}
          <SkeletonBox style={styles.pointAmountSkeleton} />

          {/* Point unit skeleton */}
          <SkeletonBox style={styles.pointUnitSkeleton} />
        </BlockView>
      </BlockView>
    );
  },
);

/**
 * Skeleton loading component for ReceivedPointTab
 * Purpose: Shows skeleton list while point transaction data is loading
 * @returns {JSX.Element} Complete skeleton layout matching ReceivedPointTab structure
 */
const ReceivedPointSkeleton = memo(() => {
  const SKELETON_ITEMS = 8; // Show 8 skeleton items
  const skeletonList = Array.from({ length: SKELETON_ITEMS }, (_, i) => i);

  return (
    <BlockView
      flex={1}
      backgroundColor={Colors.neutralBackground}
    >
      <ScrollView showsVerticalScrollIndicator={false}>
        <BlockView inset={'bottom'}>
          <Card
            margin={Spacing.SPACE_16}
            padding={Spacing.SPACE_0}
          >
            {skeletonList.map((index) => (
              <ReceivedPointItemSkeleton
                key={index}
                showBorder={index < SKELETON_ITEMS - 1}
              />
            ))}
          </Card>
        </BlockView>
      </ScrollView>
    </BlockView>
  );
});

ReceivedPointSkeleton.displayName = 'ReceivedPointSkeleton';
ReceivedPointItemSkeleton.displayName = 'ReceivedPointItemSkeleton';

export default ReceivedPointSkeleton;
