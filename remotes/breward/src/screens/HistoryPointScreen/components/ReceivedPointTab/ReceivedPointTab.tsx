import React, { memo } from 'react';
import {
  BlockView,
  Card,
  Colors,
  ConditionView,
  ScrollView,
  Spacing,
} from '@btaskee/design-system';
import EmptyRewards from '@src/components/EmptyRewards';
import { isEmpty } from 'lodash-es';

import { imgEmptyTransaction } from '@images';

import ReceivedPointItem from '../ReceivedPointItem';
import ReceivedPointSkeleton from '../ReceivedPointSkeleton';
import useReceivedPointTab from './hook';

const ReceivedPointTab = () => {
  const { data, isFetching, t } = useReceivedPointTab();

  if (isFetching) return <ReceivedPointSkeleton />;

  return (
    <BlockView
      flex={1}
      backgroundColor={Colors.neutralBackground}
    >
      <ConditionView
        condition={isEmpty(data)}
        viewTrue={
          <EmptyRewards
            title={t('NO_TRANSACTION_TITLE')}
            source={imgEmptyTransaction}
          />
        }
        viewFalse={
          <ScrollView showsVerticalScrollIndicator={false}>
            <BlockView inset={'bottom'}>
              <Card
                margin={Spacing.SPACE_16}
                padding={Spacing.SPACE_0}
              >
                {data.map((item, index) => (
                  <ReceivedPointItem
                    key={item._id}
                    historyPoint={item}
                    highlighted={data.length - 1 !== index}
                  />
                ))}
              </Card>
            </BlockView>
          </ScrollView>
        }
      />
    </BlockView>
  );
};

export default memo(ReceivedPointTab);
