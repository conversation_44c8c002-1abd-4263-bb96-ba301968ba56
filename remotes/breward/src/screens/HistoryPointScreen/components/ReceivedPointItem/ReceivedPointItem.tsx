import React, { memo } from 'react';
import {
  BlockView,
  Colors,
  CText,
  DateTimeHelpers,
  FontSizes,
  getTextWithLocale,
  IHistoryPoint,
  Spacing,
  TypeFormatDate,
} from '@btaskee/design-system';

interface ReceivedPointItemProps {
  highlighted?: boolean;
  historyPoint: IHistoryPoint;
  from?: string;
}

const ReceivedPointItem: React.FC<ReceivedPointItemProps> = ({
  highlighted,
  historyPoint,
  from,
}) => {
  return (
    <BlockView
      row
      jBetween
      padding={Spacing.SPACE_12}
      flex
      border={{
        bottom: {
          width: highlighted ? 1 : 0,
          color: Colors.neutral100,
        },
      }}
    >
      <BlockView
        flex={3}
        padding={{ right: Spacing.SPACE_32 }}
        gap={Spacing.SPACE_04}
      >
        <CText
          testID={`txtTitlePoint${historyPoint?.point}`}
          bold
          size={FontSizes.SIZE_16}
          color={Colors.orange500}
        >
          {getTextWithLocale(historyPoint?.reason)}
        </CText>
        {historyPoint?.createdAt ? (
          <CText
            size={FontSizes.SIZE_12}
            color={Colors.neutral300}
          >
            {DateTimeHelpers.formatToString({
              date: historyPoint?.createdAt,
              typeFormat: TypeFormatDate.HourMinuteDate,
            })}
          </CText>
        ) : null}
      </BlockView>
      <BlockView
        center
        border={{
          left: {
            width: 1,
            color: Colors.neutral100,
          },
        }}
        padding={{ left: Spacing.SPACE_16 }}
        flex
      >
        <CText
          testID={`txtPoint${historyPoint?.point}`}
          bold
          color={from === 'usedPointsScreen' ? Colors.red500 : Colors.green500}
        >
          {from === 'usedPointsScreen'
            ? `-${historyPoint?.point}`
            : `+${historyPoint?.point}`}
        </CText>
      </BlockView>
    </BlockView>
  );
};

export default memo(ReceivedPointItem);
