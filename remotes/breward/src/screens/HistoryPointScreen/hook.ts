import { useEffect } from 'react';
import { TrackingScreenNames } from '@btaskee/design-system';
import { useAppNavigation, useI18n, useTracking } from '@src/hooks';

const useHistoryPointScreen = () => {
  const { t } = useI18n();
  const navigation = useAppNavigation();
  const { trackingHistoryPointScreenView, setupNavigationTracking } =
    useTracking();

  // Track screen view on component mount
  useEffect(() => {
    trackingHistoryPointScreenView();
  }, [trackingHistoryPointScreenView]);

  // Setup navigation tracking
  useEffect(() => {
    const cleanup = setupNavigationTracking(TrackingScreenNames.bRewards, {
      screenType: TrackingScreenNames.HistoryPoint,
    });
    return cleanup;
  }, [setupNavigationTracking]);

  return { navigation, t };
};

export default useHistoryPointScreen;
