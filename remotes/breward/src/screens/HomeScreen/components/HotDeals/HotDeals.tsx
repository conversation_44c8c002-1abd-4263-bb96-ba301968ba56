import React, { memo, useCallback, useState } from 'react';
import {
  <PERSON><PERSON>,
  BlockView,
  Colors,
  CountDown,
  CText,
  DateTimeHelpers,
  <PERSON>ceHelper,
  FastImage,
  FlatList,
  FontSizes,
  getTextWithLocale,
  RewardCard,
  RewardGroup,
  RewardItem,
  Spacing,
} from '@btaskee/design-system';
import { useI18n } from '@src/hooks';
import { get, size } from 'lodash-es';

import { bgHotDeal } from '@images';

import styles from './styles';

interface HotDealsProps extends RewardGroup {}

const HotDeals = ({
  rewards,
  endDate,
  text,
  title,
  thumbnail,
}: HotDealsProps) => {
  const { t } = useI18n();

  const [diffSecond, setDiffSecond] = useState(
    DateTimeHelpers.diffDate({
      firstDate: endDate as string,
      secondDate: DateTimeHelpers.toDayTz({}),
      unit: 'second',
    }),
  );

  const renderItem = useCallback(({ item }: { item: RewardItem }) => {
    return (
      <RewardCard
        point={get(item, 'point')}
        title={get(item, 'title')}
        image={get(item, 'image', '')}
        id={get(item, '_id', '')}
        height={200 * DeviceHelper.WIDTH_RATIO}
        width={132 * DeviceHelper.WIDTH_RATIO}
        titleSize={FontSizes.SIZE_12}
      />
    );
  }, []);

  if (!size(rewards) || diffSecond === 0) {
    return null;
  }

  return (
    <BlockView
      height={349}
      margin={{ bottom: Spacing.SPACE_32 }}
    >
      <FastImage
        source={thumbnail ? { uri: thumbnail } : bgHotDeal}
        style={styles.bgHotDeals}
        resizeMode="cover"
      />
      <BlockView
        padding={{
          horizontal: Spacing.SPACE_16,
          vertical: Spacing.SPACE_16,
        }}
        gap={Spacing.SPACE_16}
      >
        <BlockView gap={Spacing.SPACE_04}>
          <CText
            size={FontSizes.SIZE_24}
            bold
          >
            {getTextWithLocale(title) || 'Flash sale 🔥'}
          </CText>
          <CText
            size={FontSizes.SIZE_14}
            color={Colors.neutral600}
          >
            {getTextWithLocale(text) || t('TODAY_ONLY')}
          </CText>
        </BlockView>
        <FlatList
          data={rewards}
          horizontal
          showsHorizontalScrollIndicator={false}
          keyExtractor={(item, index) => `${item._id}-${index}`}
          renderItem={renderItem}
          contentContainerStyle={styles.contentContainerStyle}
          style={styles.flatList}
        />
        <BlockView
          row
          horizontal
          gap={Spacing.SPACE_08}
        >
          <CText size={FontSizes.SIZE_14}>{t('WILL_END_IN')}</CText>
          <CountDown
            until={diffSecond}
            onFinish={() => {
              setTimeout(() => {
                setDiffSecond(0);
                Alert.alert.open({
                  title: t('LABEL_EXPIRED'),
                  message: t('TITLE_END_FLASH_SALE'),
                  actions: [{ text: t('OK') }],
                });
              }, 1000);
            }}
            timeToShow={['D', 'H', 'M', 'S']}
            timeLabels={{
              d: undefined,
              h: undefined,
              m: undefined,
              s: undefined,
            }}
            showSeparator={true}
            timeToHiddenWhenEqualToZero={['D', 'H']}
          />
        </BlockView>
      </BlockView>
    </BlockView>
  );
};

export default memo(HotDeals);
