import React, { memo } from 'react';
import Animated, {
  interpolate,
  SharedValue,
  useAnimatedStyle,
} from 'react-native-reanimated';
import Carousel from 'react-native-reanimated-carousel';
import {
  BlockView,
  BorderRadius,
  Colors,
  ConfigHelpers,
  CText,
  DeviceHelper,
  FastImage,
  FontSizes,
  getTextWithLocale,
  RewardGroup,
  RewardItem,
  Spacing,
  TouchableOpacity,
} from '@btaskee/design-system';

import styles from './styles';

interface CarouselRewardProps extends RewardGroup {
  onPressVoucher: (item: RewardItem) => void;
}

const CarouselReward = ({
  text,
  rewards,
  onPressVoucher,
}: CarouselRewardProps) => {
  return (
    <BlockView gap={Spacing.SPACE_16}>
      <CText
        center
        size={FontSizes.SIZE_18}
        color={Colors.neutral800}
        bold
      >
        {getTextWithLocale(text)}
      </CText>

      <Carousel
        data={rewards ?? []}
        height={400}
        autoPlay={ConfigHelpers.isE2ETesting ? false : true}
        autoPlayInterval={3000}
        loop={ConfigHelpers.isE2ETesting ? false : true}
        width={DeviceHelper.WINDOW.WIDTH}
        mode="parallax"
        modeConfig={{
          parallaxScrollingScale: 1,
          parallaxScrollingOffset: 150,
          parallaxAdjacentItemScale: 0.9,
        }}
        onConfigurePanGesture={(panGesture) => {
          'worklet';
          // ✅ Allow horizontal scroll, completely disable vertical scroll
          panGesture.failOffsetY([-1, 1]); // Fail on any vertical movement
        }}
        renderItem={({ item, index, animationValue }) => (
          <CustomItem
            key={index}
            animationValue={animationValue}
            onPressVoucher={onPressVoucher}
            item={item}
          />
        )}
      />
    </BlockView>
  );
};

export default memo(CarouselReward);

interface ItemProps {
  animationValue: SharedValue<number>;
  onPressVoucher: (item: RewardItem) => void;
  item: RewardItem;
}

const CustomItem: React.FC<ItemProps> = ({
  animationValue,
  onPressVoucher,
  item,
}) => {
  const maskStyle = useAnimatedStyle(() => {
    const opacity = interpolate(
      animationValue.value,
      [-1, 0, 1],
      [0.5, 0, 0.5],
    );

    return {
      opacity: opacity,
      width: '100%',
      height: '100%',
      position: 'absolute',
      backgroundColor: Colors.neutral800,
      zIndex: 1,
    };
  });

  return (
    <TouchableOpacity
      width={236 * DeviceHelper.WIDTH_RATIO}
      style={styles.carouselItem}
      height={400}
      radius={BorderRadius.RADIUS_16}
      overflow="hidden"
      onPress={() => onPressVoucher(item)}
      testID="CardBRewardBtn"
    >
      <Animated.View style={maskStyle} />
      <FastImage
        source={{ uri: item.image }}
        resizeMode="cover"
        style={styles.image}
      />
    </TouchableOpacity>
  );
};
