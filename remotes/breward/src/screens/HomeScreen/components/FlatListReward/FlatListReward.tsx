import React, { memo, useCallback } from 'react';
import {
  BlockView,
  Colors,
  CText,
  <PERSON>ce<PERSON>el<PERSON>,
  FlatList,
  FontSizes,
  getTextWithLocale,
  IconImage,
  RewardCard,
  RewardGroup,
  RewardItem,
  Spacing,
  TouchableOpacity,
} from '@btaskee/design-system';
import { get } from 'lodash-es';

import { icChevronRightGreen } from '@images';

import styles from './styles';

interface FlatListRewardProps extends RewardGroup {
  onSeeMore: (type: string, title: string, endDate?: string) => void;
}

const FlatListReward = ({
  text,
  rewards,
  onSeeMore,
  type,
  endDate,
}: FlatListRewardProps) => {
  const renderItem = useCallback(({ item }: { item: RewardItem }) => {
    return (
      <RewardCard
        testID="CardBRewardBtn"
        point={get(item, 'point')}
        title={get(item, 'title')}
        image={get(item, 'image', '')}
        id={get(item, '_id', '')}
        height={277 * DeviceHelper.WIDTH_RATIO}
        width={183 * DeviceHelper.WIDTH_RATIO}
      />
    );
  }, []);

  return (
    <BlockView gap={Spacing.SPACE_16}>
      <BlockView
        row
        jBetween
        horizontal
        padding={{ horizontal: Spacing.SPACE_16 }}
      >
        <CText
          bold
          size={FontSizes.SIZE_18}
          color={Colors.neutral800}
        >
          {getTextWithLocale(text)}
        </CText>
        <TouchableOpacity
          onPress={() =>
            onSeeMore(type ?? '', getTextWithLocale(text), endDate)
          }
          width={100}
          right
        >
          <IconImage
            source={icChevronRightGreen}
            size={18}
          />
        </TouchableOpacity>
      </BlockView>
      <FlatList
        data={rewards}
        horizontal
        showsHorizontalScrollIndicator={false}
        keyExtractor={(item, index) => `${item._id}-${index}`}
        renderItem={renderItem}
        contentContainerStyle={styles.contentContainerStyle}
      />
    </BlockView>
  );
};

export default memo(FlatListReward);
