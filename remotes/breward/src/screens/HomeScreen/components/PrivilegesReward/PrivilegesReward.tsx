import React, { memo, useCallback, useMemo } from 'react';
import {
  BlockView,
  Colors,
  CText,
  DeviceHelper,
  FastImage,
  FontSizes,
  getTextWithLocale,
  IconImage,
  RewardGroup,
  RewardItem,
  Spacing,
  TouchableOpacity,
} from '@btaskee/design-system';
import { map } from 'lodash-es';

import { icChevronRightGreen } from '@images';

import styles from './styles';

interface PrivilegesRewardProps extends RewardGroup {
  onSeeMore: (type: string, title: string, endDate?: string) => void;
  onPressVoucher: (item: RewardItem) => void;
}

const PrivilegesReward = ({
  onSeeMore,
  text,
  type,
  endDate,
  rewards,
  onPressVoucher,
}: PrivilegesRewardProps) => {
  const chunkRewards = useMemo(() => {
    const limit = rewards?.slice(0, 4);
    return limit?.reduce((acc: RewardItem[][], item, index) => {
      if (index % 2 === 0) {
        acc.push([item]);
      } else {
        acc[acc.length - 1].push(item);
      }
      return acc;
    }, []);
  }, [rewards]);

  const renderItem = useCallback(
    (row: RewardItem[], index: number) => {
      return (
        <BlockView
          row
          key={index}
          jBetween
          gap={Spacing.SPACE_08}
        >
          {map(row, (item, j) => {
            const isLeft = j % 2 === 0;
            return (
              <TouchableOpacity
                onPress={() => onPressVoucher(item)}
                key={j}
                gap={Spacing.SPACE_08}
                margin={{ top: isLeft ? 0 : Spacing.SPACE_24 }}
                overflow="hidden"
                width={175 * DeviceHelper.WIDTH_RATIO}
                testID="cardBRewardBtn"
              >
                <FastImage
                  source={{ uri: item.image }}
                  style={styles.image}
                  resizeMode="cover"
                />

                <CText
                  size={FontSizes.SIZE_14}
                  color={Colors.neutral800}
                  numberOfLines={2}
                >
                  {getTextWithLocale(item.title)}
                </CText>
              </TouchableOpacity>
            );
          })}
        </BlockView>
      );
    },
    [onPressVoucher],
  );

  return (
    <BlockView gap={Spacing.SPACE_16}>
      <BlockView
        row
        jBetween
        horizontal
        padding={{ horizontal: Spacing.SPACE_16 }}
      >
        <CText
          bold
          size={FontSizes.SIZE_18}
          color={Colors.neutral800}
        >
          {getTextWithLocale(text)}
        </CText>
        <TouchableOpacity
          onPress={() =>
            onSeeMore(type ?? '', getTextWithLocale(text), endDate)
          }
          padding={{ left: Spacing.SPACE_20 }}
        >
          <IconImage
            source={icChevronRightGreen}
            size={18}
          />
        </TouchableOpacity>
      </BlockView>
      <BlockView
        padding={{ horizontal: Spacing.SPACE_16 }}
        gap={Spacing.SPACE_24}
      >
        {map(chunkRewards, renderItem)}
      </BlockView>
    </BlockView>
  );
};

export default memo(PrivilegesReward);
