// require('isomorphic-fetch');
const { element, by, device, waitFor, expect } = require('detox');
// const moment = require('moment');
const { PORT_NODE, PORT_GO } = require('./port');

const urlServer = `http://localhost:${PORT_NODE}/api/`;
const urlService = `http://localhost:${PORT_GO}/api`;
const ADDRESS_KEY = {
  HCM: 'bTaskee, D1',
  choRayHospital: 'Cho Ray Hospital',
  DN: 'Bach <PERSON>, Da Nan<PERSON>',
  HN: 'Lăng Chủ tịch <PERSON>',
  ID: 'Jakarta Barat',
  TH: '500 Thanon Tanao',
  MY: 'Segambut',
};
const ADDRESS_VALUE = {
  [ADDRESS_KEY.HCM]:
    'bT<PERSON>ee, Đường D1, <PERSON><PERSON> <PERSON><PERSON> thị <PERSON>, <PERSON><PERSON>, Quận 7, <PERSON><PERSON><PERSON>, Việt Nam',
  [ADDRESS_KEY.choRayHospital]:
    'Cho <PERSON>, <PERSON><PERSON><PERSON><PERSON>, ph<PERSON><PERSON><PERSON> 12, <PERSON><PERSON><PERSON><PERSON> 5, <PERSON><PERSON><PERSON><PERSON><PERSON>, Việt Nam',
  [ADDRESS_KEY.DN]: '12 Bạch Đằng, Thạch Thang, Hải Châu, Đà Nẵng, Việt Nam',
  [ADDRESS_KEY.HN]:
    'Lăng Chủ tịch Hồ Chí Minh, Hùng Vương, Điện Biên, Ba Đình, Hà Nội, Việt Nam',
  [ADDRESS_KEY.ID]:
    'Jakarta Barat, Kota Jakarta Barat, Daerah Khusus Ibukota Jakarta, Indonesia',
  [ADDRESS_KEY.TH]:
    '500 Thanon Tanao, แขวงวัดบวรนิเวศ เขตพระนคร กรุงเทพมหานคร ประเทศไทย',
  [ADDRESS_KEY.MY]:
    'Segambut, Kuala Lumpur, Wilayah Persekutuan Kuala Lumpur, Malaysia',

  default:
    'Công ty TNHH bTaskee, Hẻm 284/25 Lý Thường Kiệt, phường 14, Quận 10, Hồ Chí Minh, Việt Nam',
};

const callService = async (path, data) => {
  const params = data;
  if (['/v3/accept-task-vn/accept'].includes(path) && !params.appVersion) {
    params.appVersion = '1.0.0';
  }
  const myHeaders = new Headers();
  myHeaders.append('Content-Type', 'application/json');

  const raw = JSON.stringify(params || {});

  const requestOptions = {
    method: 'POST',
    headers: myHeaders,
    body: raw,
  };

  const result = await fetch(`${urlService}${path}`, requestOptions);
  return result.json();
};

const initData = async (path, data) => {
  const myHeaders = new Headers();
  myHeaders.append('Content-Type', 'application/json');

  const raw = JSON.stringify(data || {});

  const requestOptions = {
    method: 'POST',
    headers: myHeaders,
    body: raw,
  };

  // Add timeout to prevent hanging
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

  try {
    const result = await fetch(`${urlServer}${path}`, {
      ...requestOptions,
      signal: controller.signal,
    });
    clearTimeout(timeoutId);
    return result.json();
  } catch (error) {
    clearTimeout(timeoutId);
    throw error;
  }
};

const reloadApp = async () => {
  // Skip data reset for e2e testing if servers are not running
  try {
    await initData('resetData');
  } catch (error) {
    console.log(
      'Warning: Could not reset data - server may not be running:',
      error.message,
    );
    // Continue without data reset for testing purposes
  }
};

const selectCountryCode = async (countryCode = '+84') => {
  await waitForElement('chooseCountryCode', 500);
  await tapId('chooseCountryCode');
  try {
    await tapId('chooseCountryCode');
  } catch (error) {}
  await tapId(`chooseCountryCode${countryCode}`);
};

// const postTask = async (
//   serviceId,
//   address,
//   description = 'My Task',
//   skipChooseService = false,
// ) => {
//   let defaultAddress = ADDRESS_VALUE.default;
//   if (serviceId === 'postTaskServicePATIENT_CARE') {
//     defaultAddress = ADDRESS_VALUE[ADDRESS_KEY.choRayHospital];
//   }
//   const idAddress = ADDRESS_VALUE[address] || defaultAddress;
//   if (!skipChooseService) {
//     await tapIdService(serviceId);
//   }

//   try {
//     await tapText('Đặt dịch vụ ngay');
//   } catch (error) {}

//   try {
//     await tapText('Bắt đầu trải nghiệm');
//   } catch (error) {}

//   // Only choose address when a new user.
//   const checkExist = await checkElementVisible('txtInputAddress');

//   if (!checkExist) {
//     try {
//       await tapText(idAddress);
//     } catch (error) {}
//     const checkAddress = await checkElementVisible('address0');
//     if (checkAddress) {
//       await tapId('address0');
//     }
//     const checkAddress1 = await checkElementVisible('address1');
//     if (checkAddress1) {
//       await tapId('address1');
//     }
//     return null;
//   }

//   await element(by.id('txtInputAddress')).typeText(address);

//   await waitForElement(idAddress, 3000, 'text');
//   await tapText(idAddress);
//   await tapId('btnSelectLocation');
//   await typeToTextField('txtDescriptionMap', description);
//   await tapText('Đồng ý');
//   try {
//     await tapId('iconCloseModal');
//   } catch (error) {}
// };

// const chooseMarketAddress = async (address, description = 'My Task') => {
//   let idAddress = null;

//   // Only choose address when a new user.
//   // const checkExist = await checkElementVisible('btnAddress');
//   //
//   // if (!checkExist) return null;
//   //
//   try {
//     await tapId('btnAddress');
//   } catch (e) {}
//   try {
//     await tapId('inputAddress');
//   } catch (e) {}

//   if (address === '500 Thanon Tanao') {
//     await element(by.id('txtInputAddress')).typeText(address);
//     // if (device.getPlatform() === 'android') {
//     //   idAddress = '500 Thanon Tanao, Wat Bowon Niwet, Phra Nakhon, Vùng đô thị Bangkok, Thái Lan'
//     // }else{
//     idAddress =
//       '500 Thanon Tanao, Wat Bowon Niwet, Phra Nakhon, Vùng đô thị Bangkok, Thái Lan';
//     // }
//   } else {
//     await element(by.id('txtInputAddress')).typeText('12 ' + address);
//     idAddress =
//       'Chợ Tân Mỹ, Đường Tân Mỹ, Tân Phú, Quận 7, Thành phố Hồ Chí Minh, Việt Nam';
//     if (address === '12 Kim Ma, Ha Noi') {
//       idAddress = 'Kim Mã, Ba Đình, Hà Nội, Việt Nam';
//     } else if (address === 'Bach Dang, Da Nang') {
//       idAddress = 'Bạch Đằng, Hải Châu 1, Hải Châu, Đà Nẵng, Việt Nam';
//     } else if (address === 'Đồng Khởi' || address === 'Dong Khoi') {
//       idAddress =
//         '12 Đồng Khởi, Bến Nghé, Quận 1, Thành phố Hồ Chí Minh, Việt Nam';
//     } else if (address === 'lotter quan 9') {
//       idAddress = 'Quận 9, Thành phố Hồ Chí Minh, Việt Nam';
//     }
//   }
//   await waitForElement(idAddress, 3000, 'text');
//   await tapText(idAddress);
//   await tapId('btnSelectLocation');
// };

// This step, handle login on modal login
// const loginWithModal = async (phone, password = '123456', countryCode = '') => {
//   // Click button login in modal login
//   await waitForElement('modalBtnLogin', 500);
//   await tapId('modalBtnLogin');

//   // Login screens
//   if (countryCode) {
//     await waitForElement('chooseCountryCode', 500);
//     await tapId('chooseCountryCode');
//     await tapId(`chooseCountryCode${countryCode}`);
//   }
//   await waitForElement('txtPhoneSignIn', 1000);
//   await element(by.id('txtPhoneSignIn')).tap();
//   await waitForElement('txtPhoneSignIn', 1000);
//   await element(by.id('txtPhoneSignIn')).typeText(phone);
//   await element(by.id('txtPasswordSigIn')).tap();
//   await element(by.id('txtPasswordSigIn')).typeText(`${password}\n`);
//   // if (device.getPlatform() === "ios") {
//   await element(by.id('btnSignIn')).tap();
//   // } else {
//   //   await element(by.id('btnSignInAndroid')).tap();
//   // }
//   await waitForLoading(1000);
// };

// const loginWithPhoneAndPassword = async (phone, password, countryCode) => {
//   // await device.reloadReactNative();

//   // DON'T NEED BECAUSE AUTO LOGOUT WORKEDÍÍÍÍÍ
//   // const account = await checkElementVisible('TÀI KHOẢN', 'text');
//   // if (account) {
//   //   await tapText('TÀI KHOẢN');
//   //   await tapText('Cài đặt');
//   //   await waitForElement('btnLogout', 500);
//   //   await tapId('btnLogout');
//   // }
//   try {
//     await waitForElement('cancelVerify', 1000);
//     await tapId('cancelVerify');
//   } catch (error) {}

//   // const signInVisible = await checkElementVisible('homeHeaderBtnLogin');

//   // User Not login
//   try {
//     // Choose country screen
//     await tapId('lang_vi');
//     await tapId('country_VN');
//     await tapId('btnNext');

//     // await waitForElement('Select service', 2000, 'text');
//     await tapId('btnNextIntroPage');
//     // await waitForElement('Task description', 500, 'text');
//     await tapId('btnNextIntroPage');
//     // await waitForElement('Confirm and finish', 500, 'text');
//     await tapId('btnStartIntroPage');
//   } catch (e) {}

//   // Click login button in home screen and open modal
//   await waitForElement('homeHeaderBtnLogin', 500);
//   await tapId('homeHeaderBtnLogin');

//   await loginWithModal(phone, password, countryCode);
// };

// ========================== Tap Element ======================================

const typeToTextField = async (id, value) => {
  await element(by.id(id)).tap();
  await element(by.id(id)).typeText(value);
  // Tap to hide keyboard
  // if (device.getPlatform() === 'ios') {
  //   await tapAtPoint(id, 0, -10);
  // }
};

const typeToTextFieldSubmitKeyboard = async (id, value) => {
  await element(by.id(id)).tap();
  await element(by.id(id)).typeText(`${value}\n`);
};

const typeToTextFieldAtIndex = async (id, value, index = 0) => {
  await element(by.id(id)).atIndex(index).tap();
  await element(by.id(id)).atIndex(index).typeText(value);
};

const tapId = async (id) => {
  await waitForElement(id, 1000);
  await element(by.id(id)).tap();
};

const tapIdAtIndex = async (id, index = 0) => {
  await element(by.id(id)).atIndex(index).tap();
};

const tapText = async (text) => {
  await waitForElement(text, 1000, 'text');
  await element(by.text(text)).tap();
};

const tapTextAtIndex = async (text, index = 0) => {
  await element(by.text(text)).atIndex(index).tap();
};

const tapAtPoint = async (id, x, y) => {
  await element(by.id(id)).tapAtPoint({ x, y });
};

const tapHeaderBack = async (index = 0) => {
  if (device.getPlatform() === 'ios') {
    try {
      await tapIdAtIndex('header-back', index);
    } catch (error) {
      await tapIdAtIndex('header-back', 1);
    }
  } else {
    await device.pressBack();
  }
};

const scrollTo = async (id, direction) => {
  // direction: 'top' or 'bottom'
  await element(by.id(id)).scrollTo(direction);
};

const scroll = async (
  id,
  pixels,
  direction,
  startPositionX = NaN,
  startPositionY = NaN,
) => {
  // direction: 'up' or 'down'
  await element(by.id(id)).scroll(
    pixels,
    direction,
    startPositionX,
    startPositionY,
  );
};

const swipe = async (id, direction, type) => {
  // direction: left/right/up/down
  if (type === 'text') {
    await element(by.text(id)).swipe(direction, 'fast', NaN, NaN, 0.4);
  } else {
    await element(by.id(id)).swipe(direction, 'fast', NaN, NaN, 0.4);
  }
};

const clearTextInput = async (id) => {
  const objElement = element(by.id(id));
  try {
    await objElement.atIndex(0).tap();
  } catch (error) {
    await objElement.atIndex(1).tap();
  }
  await objElement.clearText();
};

const clearTextInputAtIndex = async (id, index) => {
  await element(by.id(id)).atIndex(index).tap();
  await element(by.id(id)).atIndex(index).clearText();
};

// ========================== Expect Element ===================================

const waitForElement = async (value, seconds, type) => {
  if (type === 'text') {
    await waitFor(element(by.text(value)))
      .toBeVisible()
      .withTimeout(seconds);
    await expectElementVisible(value, 'text');
  } else {
    await waitFor(element(by.id(value)))
      .toBeVisible()
      .withTimeout(seconds);
    await expectElementVisible(value);
  }
};

const waitForElementAtIndex = async (value, seconds, index = 0, type) => {
  if (type === 'text') {
    await waitFor(element(by.text(value)).atIndex(index))
      .toBeVisible()
      .withTimeout(seconds);
    await expectElementVisibleAtIndex(value, index, 'text');
  } else {
    await waitFor(element(by.id(value)).atIndex(index))
      .toBeVisible()
      .withTimeout(seconds);
    await expectElementVisibleAtIndex(value, index);
  }
};

const waitForLoading = async (seconds) => {
  try {
    await waitFor(element(by.id('appLoading')))
      .toBeVisible()
      .withTimeout(seconds);
  } catch (e) {}
};

//I should see text or id
const expectElementVisible = async (id, type) => {
  if (type === 'text') {
    await expect(element(by.text(id))).toBeVisible();
  } else {
    await expect(element(by.id(id))).toBeVisible();
  }
};

const expectElementVisibleAtIndex = async (id, index, type) => {
  if (type === 'text') {
    await expect(element(by.text(id)).atIndex(index)).toBeVisible();
  } else {
    await expect(element(by.id(id)).atIndex(index)).toBeVisible();
  }
};

const expectElementNotVisible = async (value, type) => {
  if (type === 'text') {
    await expect(element(by.text(value))).toBeNotVisible();
  } else {
    await expect(element(by.id(value))).toBeNotVisible();
  }
};

const expectElementNotExist = async (value, type) => {
  if (type === 'text') {
    await expect(element(by.text(value))).toNotExist();
  } else {
    await expect(element(by.id(value))).toNotExist();
  }
};

const expectIdToHaveText = async (id, text) => {
  await waitForElement(id, 500);
  await expect(element(by.id(id))).toHaveText(text);
};

const expectIdToHaveValue = async (id, value) => {
  await expect(element(by.id(id))).toHaveValue(value);
};

const checkElementVisible = async (id, type) => {
  try {
    await waitForElement(id, 200, type);
    return true;
  } catch (e) {
    return false;
  }
};

const expectIdToHaveTextAtIndex = async (id, text, index) => {
  await expect(element(by.id(id)).atIndex(index)).toHaveText(text);
};

const expectIdToHaveValueAtIndex = async (id, value) => {
  await expect(element(by.id(id))).toHaveValue(value);
};

const typePromotionCode = async (code) => {
  await element(by.id('textInputPromotion')).tap();
  await element(by.id('textInputPromotion')).typeText(`${code}\n`);
  await waitForLoading(500);
};

/**
 *
 * @param hour hour want set [0-23]
 * @param hourDefault hour default when open post task step 2; 14h default, 8h for subscription
 */
const selectTime24h = async (hour, hourDefault = 14, id) => {
  await waitForElement(id || 'dpTimePicker', 500);
  await tapId(id || 'dpTimePicker');
  await waitForElement('dateTimePicker', 500);
  const dp = element(by.id('dateTimePicker'));
  const hourTap = hour - hourDefault;
  if (hourTap > 0) {
    // Tap the next time
    for (var i = 0; i < hourTap; i++) {
      await dp.tapAtPoint({ x: 110, y: 135 });
    }
  } else {
    // Tap the previous time
    for (var i = 0; i > hourTap; i--) {
      await dp.tapAtPoint({ x: 110, y: 90 });
    }
  }
};

const selectTime = async (hour = 1, next = true, ampm) => {
  if (device.getPlatform() === 'ios') {
    await waitForElement('dpTimePicker', 500);
    await tapId('dpTimePicker');
    await waitForElement('dateTimePicker', 500);
    const dp = element(by.id('dateTimePicker'));
    if (hour) {
      if (next) {
        // Tap the next time
        for (var i = 0; i < hour; i++) {
          await dp.tapAtPoint({ x: 110, y: 135 });
        }
      } else {
        // Tap the previous time
        for (var i = 0; i < hour; i++) {
          await dp.tapAtPoint({ x: 110, y: 90 });
        }
      }
    }
    if (ampm) {
      if (ampm === 'AM') {
        await dp.tapAtPoint({ x: 220, y: 90 });
      } else {
        await dp.tapAtPoint({ x: 220, y: 125 });
      }
    }
  } else {
  }
};

const selectTimeAtIndex = async (hour = 1, next = true, ampm, index = 0) => {
  if (device.getPlatform() === 'ios') {
    await tapIdAtIndex('dpTimePicker', index);
    await waitForElement('dateTimePicker', 500);
    const dp = element(by.id('dateTimePicker'));
    if (hour) {
      if (next) {
        // Tap the next time
        for (var i = 0; i < hour; i++) {
          await dp.tapAtPoint({ x: 110, y: 135 });
        }
      } else {
        // Tap the previous time
        for (var i = 0; i < hour; i++) {
          await dp.tapAtPoint({ x: 110, y: 90 });
        }
      }
    }
    if (ampm) {
      if (ampm === 'AM') {
        await dp.tapAtPoint({ x: 220, y: 90 });
      } else {
        await dp.tapAtPoint({ x: 220, y: 125 });
      }
    }
  } else {
  }
};

const fillActiveCode = async (phone, countryCode) => {
  const data = await initData('user/getActivationCode', {
    phone: phone,
    countryCode: countryCode,
  });
  const code = data.data.code;
  await element(by.id('inputOTP')).typeText(code);
};

const typeToTextFieldDishName = async (numDish = 2) => {
  await waitForElement('dishDetail1', 500);
  for (let i = 1; i <= numDish; i++) {
    // await typeToTextField(`dishDetail${i}`, `Mon ${i}`);
    await element(by.id(`dishDetail${i}`)).tap();
    await element(by.id(`dishDetail${i}`)).typeText(`Mon ${i}`);
  }
  await element(by.id(`dishDetail${numDish}`)).typeText(`\n`);
};

const logout = async () => {
  try {
    await tapText('Tài khoản');
    await swipe('scrollAccount', 'up');
    await tapText('Cài đặt');
    await tapText('Đăng xuất');
    await tapText('Đồng ý');
  } catch (error) {}
};

const cancelTask = async ({ fee = '0', currency = '₫', promotion = false }) => {
  await swipe('scrollTaskDetail', 'up');
  await tapId('btnEditTask');

  try {
    await tapText('Giữ Tasker hiện tại');
  } catch (error) {}
  // await swipe('updatePage', 'up');
  await tapId('cancelTask');
  await tapId('btnConfirmCancel');
  await tapText('Không cần công việc này nữa.');
  await tapText('Đồng ý');
  if (promotion) {
    await waitForElement(
      'Khi công việc bị hủy và có tính phí, khuyến mãi đã áp dụng sẽ không được hoàn lại hoặc sử dụng lại.',
      500,
      'text',
    );
    await tapText('Tiếp tục');
  }
};

const houseKeepingPTCreateRoom = async (
  roomName = 'Full House',
  area = '50',
) => {
  // CREATE ROOM
  await tapText('Tạo phòng');
  await typeToTextField('roomName', roomName);
  await typeToTextField('roomArea', area);
  await tapId('btnCreateNewRoom');
  await waitForElement(
    'Tạo buồng phòng thành công. Bạn có thể đăng việc ngay bây giờ!',
    500,
    'text',
  );
  await tapText('Đăng việc ngay');
};

const postTaskElderlyCare = async (duration = 4) => {
  // await postTask('postTaskServiceELDERLY_CARE', ADDRESS_KEY.HCM, 'My Task');
  try {
    await waitForElement('address1', 500);
    await tapId('address1');
  } catch (e) {}
  await tapId('btnChooseDay');
  await tapId(`chooseDuration-${duration}`);
  await expectElementVisible('lbPrice');
  await tapId('btnNextStep2');
  await selectTime24h(10);
  await tapText('Đồng ý');
  await typeToTextField(
    'taskNote',
    'Cham soc nguoi gia can than.\nTiem thuoc dung gio',
  );
  await tapId('btnNextStep3');
};

const postTaskPatientCare = async (duration = 4) => {
  // await postTask('postTaskServicePATIENT_CARE', 'Cho Ray Hospital', 'My Task');
  try {
    await waitForElement('address1', 500);
    await tapId('address1');
  } catch (e) {}
  await tapId('btnChooseDay');
  await tapId(`chooseDuration-${duration}`);
  await tapId('btnNextStep2');
  await typeToTextField(
    'taskNote',
    'Cham soc nguoi gia can than.\nTiem thuoc dung gio',
  );
  await tapId('btnNextStep3');
  await tapText('Đăng việc');
  await waitForElement('Theo dõi công việc', 500, 'text');
  await tapText('Theo dõi công việc');
};

const postTaskMakeup = async () => {
  await tapIdService('postTaskServiceBEAUTY_CARE');
  await tapText('Trang điểm & Tạo mẫu tóc');
  await tapId('1-people');
  await tapId('graduationMakeup');
  await tapText('Tiếp tục');
  await tapId('graduationNaturalMakeup');
  await tapText('Tiếp tục');
  try {
    await tapId('address-0');
  } catch (e) {
    await typeToTextField('txtInputAddress', 'btaskee D1');
    await tapText(ADDRESS_VALUE[ADDRESS_KEY.HCM]);
    await waitForElement('Chọn vị trí này', 500, 'text');
    await tapText('Chọn vị trí này');
    await typeToTextField('txtInputPhoneNumber', '0834567890');
    await typeToTextField('txtInputContactName', 'Kaiser');
    await typeToTextField('txtInputHomeNumber', 'My Task');
    await tapText('Xác nhận');
  }
  await waitForElement('Tiếp tục', 500, 'text');
  await tapText('Tiếp tục');
  await waitForElement('taskNoteConfirm', 500);
  await tapId('taskNoteConfirm');
  await waitForElement('scrollViewStep4', 1000);
  await swipe('scrollViewStep4', 'up');
  await tapId('checkboxPolicyPostTask');
  await tapText('Tiếp tục');
};

const postTaskNail = async () => {
  await tapIdService('postTaskServiceBEAUTY_CARE');
  await tapText('Làm móng');
  await tapId('1-people');
  await tapId('nailBaseCare');

  await waitForElement('Sơn thường', 500, 'text');
  await tapText('Sơn thường');
  await tapText('Chọn');
  await waitForElement('Sơn mắt mèo', 1000, 'text');
  await tapText('Sơn mắt mèo');
  await tapText('Chọn');
  await tapText('Tiếp tục');
  try {
    await tapId('address-0');
  } catch (e) {
    await typeToTextField('txtInputAddress', 'btaskee D1');
    await tapText(ADDRESS_VALUE[ADDRESS_KEY.HCM]);
    await waitForElement('Chọn vị trí này', 500, 'text');
    await tapText('Chọn vị trí này');
    await typeToTextField('txtInputPhoneNumber', '0834567890');
    await typeToTextField('txtInputContactName', 'Kaiser');
    await typeToTextField('txtInputHomeNumber', 'My Task');
    await tapText('Xác nhận');
  }
  await waitForElement('Tiếp tục', 500, 'text');
  await tapText('Tiếp tục');
  await waitForElement('taskNoteConfirm', 500);
  await tapId('taskNoteConfirm');
  await waitForElement('scrollViewStep4', 1000);
  await swipe('scrollViewStep4', 'up');
  await tapId('checkboxPolicyPostTask');
  await tapText('Tiếp tục');
};

const postTaskHairStyling = async () => {
  await tapIdService('postTaskServiceBEAUTY_CARE');
  await tapText('Làm tóc');
  await tapId('1-people');
  await tapId('relaxingShampoo');
  await tapText('Tiếp tục');
  try {
    await tapId('address-0');
  } catch (e) {
    await typeToTextField('txtInputAddress', 'btaskee D1');
    await tapText(ADDRESS_VALUE[ADDRESS_KEY.HCM]);
    await waitForElement('Chọn vị trí này', 500, 'text');
    await tapText('Chọn vị trí này');
    await typeToTextField('txtInputPhoneNumber', '0834567890');
    await typeToTextField('txtInputContactName', 'Kaiser');
    await typeToTextField('txtInputHomeNumber', 'My Task');
    await tapText('Xác nhận');
  }
  await waitForElement('Tiếp tục', 500, 'text');
  await tapText('Tiếp tục');
  await waitForElement('taskNoteConfirm', 500);
  await tapId('taskNoteConfirm');
  await waitForElement('scrollViewStep4', 1000);
  await swipe('scrollViewStep4', 'up');
  await tapId('checkboxPolicyPostTask');
  await tapText('Tiếp tục');
};

// Continue post task step 4
const signUpWithModal = async (
  name = 'name',
  phone = '0834567890',
  email = '<EMAIL>',
  countryCode,
  referralCode,
) => {
  try {
    await waitForElement('Select service', 2000, 'text');
    await tapId('btnNextIntroPage');
    await waitForElement('Task description', 500, 'text');
    await tapId('btnNextIntroPage');
    await waitForElement('Confirm and finish', 500, 'text');
    await tapId('btnStartIntroPage');

    // Choose country screen
    await waitForElement('btnChooseLanguage', 1000);
    await tapId('btnChooseLanguage');
    await tapId('vietnam');
    await tapId('countryvietnam');
  } catch (e) {}

  // Modal sign up
  await waitForElement('ModalBtnSignUp', 500);
  await tapId('ModalBtnSignUp');

  await typeToTextField('txtName', name);
  await typeToTextField('txtPhone', phone);
  await typeToTextField('txtEmail', email);
  referralCode &&
    (await typeToTextField('txtSignUpPromotionCode', referralCode));
  await tapId('checkboxPolicy');
  await tapId('btnSignup');
  await waitForElement('Xác thực tài khoản', 1000, 'text');
  await fillActiveCode(phone, countryCode);
  await typeToTextField('txtPassword', '*********');
  await typeToTextField('txtSecondPassword', '*********');
  try {
    await tapIdAtIndex('btnSavePassword', 0);
  } catch (e) {
    await tapIdAtIndex('btnSavePassword', 1);
  }
};

const forgotPasswordWithModal = async (phone, countryCode) => {
  // Modal sign up
  await waitForElement('modalBtnLogin', 500);
  await tapId('modalBtnLogin');

  // Check keyboard
  try {
    // await waitForElementAtIndex('signInBtnForgotPassword', 500, 0);
    await tapIdAtIndex('signInBtnForgotPassword', 0);
  } catch (e) {
    // await waitForElementAtIndex('signInBtnForgotPassword', 500, 1);
    await tapIdAtIndex('signInBtnForgotPassword', 1);
  }

  // Activation screen
  await typeToTextField('inputPhoneNumberForgotPass', phone);

  try {
    await tapIdAtIndex('nextButtonForgotPass', 0);
  } catch (e) {
    await tapIdAtIndex('nextButtonForgotPass', 1);
  }
  await waitForElement('Xác thực tài khoản', 1000, 'text');
  await fillActiveCode(phone, countryCode);

  // Type new password
  await typeToTextField('txtPassword', '*********');
  await typeToTextField('txtSecondPassword', '*********');

  try {
    await tapIdAtIndex('btnSavePassword', 0);
  } catch (e) {
    await tapIdAtIndex('btnSavePassword', 1);
  }
};

const chooseIsoCodeFromSetting = async (isoCode = 'VN') => {
  await tapId('Tab_Account');
  await tapText('Cài đặt');
  await tapId('txtCountrySelected');
  await tapId(`btn${isoCode}`);
  try {
    await tapText('Đồng ý');
  } catch (error) {
    await tapHeaderBack();
    await tapId('Tab_Home');
  }
};

const chooseAddressGrocery = async () => {
  try {
    await waitForElement('address1', 500);
    await tapId('address1');
  } catch (e) {}

  // Choose grocery v3
  await tapId('groceryAssistantV3');

  // Choose store market
  await waitForElement('Big C', 500, 'text');
  await tapText('Big C');
};

const postTaskGroceryAssistant = async ({ isDepositMoney = true }) => {
  // await postTask('postTaskServiceGO_MARKET', 'Pham Van Nghi');

  // Choose address
  await chooseAddressGrocery();
  await tapId('iconCloseModal');
  // add food to cart
  await tapId('add_Củ quả_Đào tươi (2 trái)');
  await expectIdToHaveText('cartQuantity', '1');

  // See cart
  await tapId('rightButtonCart');
  await waitForElement('Xem giỏ hàng', 500, 'text');
  await tapText('Tiếp tục');

  // Check price step 3
  if (isDepositMoney) {
    await waitForElement(
      'Đơn hàng này yêu cầu khách hàng đặt cọc 200,000 ₫ và thanh toán bằng bPay.',
      500,
      'text',
    );
    await tapText('Đồng ý');
  }

  await tapId('btnNextStep3');
  await tapId('btnNextNoteStep3');

  // Check price step 4
  await swipe('scrollViewStep4', 'up');

  // step4FeeBuy
  await tapText('Đăng việc');
  await waitForElement('Theo dõi công việc', 500, 'text');
  await tapText('Theo dõi công việc');
};

const socketPaymentWithMoMo = async () => {
  const transaction = await initData('momo/getTransaction');
  try {
    await callService('/mockup/momo', { transactionId: transaction._id });
  } catch {}
};

const socketPaymentWithZaloPay = async () => {
  const transaction = await initData('zaloPay/getTransaction');
  try {
    await callService('/mockup/zalo-pay', { transactionId: transaction._id });
  } catch {}
};

const socketPaymentWithShopeePay = async () => {
  const transaction = await initData('shopeePay/getTransaction');
  try {
    await callService('/mockup/shopee-pay', { transactionId: transaction._id });
  } catch {}
};
const socketPaymentWithVNPay = async () => {
  const transaction = await initData('vnPay/getTransaction');
  try {
    await callService('/mockup/vn-pay', { transactionId: transaction._id });
  } catch {}
};
// For truemoney, promtpay, card thái lan
const socketPaymentWith2c2p = async () => {
  const transaction = await initData('2c2p/getTransaction');
  try {
    await callService('/mockup/payment-2c2p', {
      transactionId: transaction._id,
    });
  } catch {}
};
const socketPaymentWithShopeePayTH = async () => {
  const transaction = await initData('shopeePayTH/getTransaction');
  try {
    await callService('/mockup/shopee-pay-th', {
      transactionId: transaction._id,
    });
  } catch {}
};

const postTaskDisinfection = async (address = '') => {
  // await postTask('postTaskServiceDISINFECTION_SERVICE', address, 'My Task');
  try {
    //Tab khi lần đầu vào app
    await tapId('iconCloseModal');
  } catch (error) {}
};

const postTaskOfficeCarpetCleaning = async () => {
  // await postTask(
  //   'postTaskServiceOFFICE_CARPET_CLEANING',
  //   ADDRESS_KEY.HCM,
  //   'My Task',
  // );
  try {
    //Tab khi lần đầu vào app
    await tapId('iconCloseModal');
  } catch (error) {}
};

const checkPolicyCancel = async () => {
  await waitForElement(
    'Bạn được hủy miễn phí trong 3 trường hợp sau:',
    1000,
    'text',
  );
  await expectElementVisible(
    '1. Hủy trong vòng 10 phút sau khi đăng việc.',
    'text',
  );
  await expectElementVisible('2. Hủy khi chưa có ai nhận việc.', 'text');
  await expectElementVisible(
    '3. Hủy trước giờ làm việc ít nhất 6 tiếng.',
    'text',
  );
  await expectElementVisible(
    'Ngoài 3 trường hợp trên chúng tôi sẽ tính phí:',
    'text',
  );
  await tapText('Đồng ý');
};

const formatMoney = (number = 0) => {
  if (number) {
    const str = number.toString();
    return str.replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,');
  }
  return number;
};

//ex:concatString(['a', "b", "c"]) => abc
const concatStrings = (stringArray) => {
  if (stringArray?.length) {
    return ''.concat(...stringArray);
  }
  return '';
};

/**
 *
 * @param {*} weekdays Array number [0, 1, 2, 3, 4, 5, 6]
 * @param {*} month Number
 * @returns Array string
 */
const getDateFromWeekday = (weekdays, month, hour = 8) => {
  // if (weekdays && weekdays.length > 0) {
  //   const result = [];
  //   let start = moment().add(3, 'days').set({ hours: 8, minutes: 0, seconds: 0 }).toDate();
  //   const endDate = moment(start).add(month, 'months').endOf('day').toDate();
  //   for (start; start.getTime() <= endDate.getTime(); start = moment(start).add(1, 'day').toDate()) {
  //     if (weekdays.indexOf(start.getDay()) >= 0) {
  //       result.push(moment(start).set({ hours: 8, minutes: 0, seconds: 0 }).toDate());
  //     }
  //   }
  //   return result;
  // }
  return [];
};

/**
 *
 * @param {*} schedule Array string
 * @param {*} serviceName String CLEANING_SUBSCRIPTION | OFFICE_CLEANING_SUBSCRIPTION | ELDERLY_CARE_SUBSCRIPTION | PATIENT_CARE_SUBSCRIPTION
 * @param {*} duration Number
 * @param {*} month Number
 * @param {*} taskPlace Object
 * @param {*} userId String
 * @returns Object
 */
const getPriceSubscription = async ({
  schedule,
  detailOfficeCleaning,
  requirements,
  detailChildCare,
  serviceName = 'CLEANING_SUBSCRIPTION',
  duration = 2,
  month = 1,
  taskPlace,
  isoCode = 'VN',
  userId = 'userId_0834567890',
  promotion,
}) => {
  const TASK_PLACE = {
    VN: {
      country: 'VN',
      city: 'Hồ Chí Minh',
      district: 'Quận 10',
    },
    ID: {
      country: 'ID',
      city: 'Jakarta',
      district: 'West Jakarta',
    },
  };
  const CLEANING_SUBSCRIPTION_ID = {
    VN: 'aRQamufWWW5pToqQNVN',
    ID: 'aRQamufWWW5pToqID',
  };

  const CLEANING_SUBSCRIPTION_API = {
    VN: '/v2/pricing/subscription',
    ID: '/v3/pricing-indo/subscription',
  };

  // Service
  const service = {
    CLEANING_SUBSCRIPTION: CLEANING_SUBSCRIPTION_ID[isoCode],
    OFFICE_CLEANING_SUBSCRIPTION: 'x6291a09d6b0d9e0db9067735xxx',
    ELDERLY_CARE_SUBSCRIPTION: '617b80bc548df3b332e95c51',
    PATIENT_CARE_SUBSCRIPTION: '617b80bc548df3b332e95c52',
    CHILD_CARE_SUBSCRIPTION: '6291a09d6b0d9e0db9067735',
  };
  // url
  const api = {
    CLEANING_SUBSCRIPTION: CLEANING_SUBSCRIPTION_API[isoCode],
    OFFICE_CLEANING_SUBSCRIPTION: '/v2/pricing/subscription-office-cleaning',
    ELDERLY_CARE_SUBSCRIPTION: '/v2/pricing/subscription-elderly-care',
    PATIENT_CARE_SUBSCRIPTION: '/v2/pricing/subscription-patient-care',
    CHILD_CARE_SUBSCRIPTION: '/v2/pricing/subscription-child-care',
  };
  // Params
  const params = {
    schedule: schedule,
    service: {
      _id: service[serviceName],
    },
    task: {
      taskPlace: taskPlace || TASK_PLACE[isoCode],
      duration: duration,
      autoChooseTasker: true,
      homeType: 'HOME',
    },
    month: month,
    isoCode: isoCode,
    userId: userId,
  };
  if (detailOfficeCleaning) {
    params.task.detailOfficeCleaning = detailOfficeCleaning;
  }
  if (detailChildCare) {
    params.task.detailChildCare = detailChildCare;
  }
  if (requirements) {
    params.task.requirements = requirements;
  }
  if (promotion) {
    params.task.promotion = promotion;
  }
  // Call api
  const result = await callService(api[serviceName], params);
  // Return result
  return result;
};

/**
 * @description dùng để update lại thời gian nhận đồ của laundry trong giờ hoạt động của services
 */
const updateCollectTimeLaundry = async () => {
  // const currentHour = moment().hours();
  // if (currentHour < 10 || currentHour > 17) {
  //   await tapIdAtIndex('weekdays_3', 1); //Đổi ngày trả đồ
  //   await tapIdAtIndex('weekdays_1', 0); //Đổi ngày nhận đồ
  //   await selectTime24h(14, currentHour, 'dpTimePickercollectionDate'); //Thay đổi ngày nhận đồ thành 14h để nằm trong khung giờ làm việc
  //   await tapText('Đồng ý');
  // }
};

const isWeekend = () => {
  //là cuối tuần t7 và cn
  // const day = moment().day();
  // return [0, 6].includes(day);
};

const capitalize = (str) => {
  return str.charAt(0).toUpperCase() + str.slice(1);
};

const sleep = async (milliseconds = 1000) => {
  try {
    await waitForElement('sleep', milliseconds);
  } catch (e) {}
};

const tapIdService = async (id) => {
  try {
    await tapId(id);
  } catch (error) {
    await tapId('seeMoreService');
    try {
      await tapId(id);
    } catch (e) {
      await swipe('scrollExploreBTaskeeScreen', 'up');
      await tapId(id);
    }
  }
};

const checkHomeTypeHomeMoving = async (id, homeType, acreage) => {
  await expectIdToHaveText(id, `${homeType} (${acreage})`);
};

const tapTask = async (descriptionTask) => {
  try {
    await tapId(`task${descriptionTask}`);
  } catch (error) {
    await tapId(`TAB_UPCOMING_${descriptionTask}`);
  }
};

const removeIntroHouseKeeping = async () => {
  // if had intro page
  try {
    await waitForElement('Bỏ qua', 500, 'text');
    await tapText('Bỏ qua');
    await waitForElement('Tạo hồ sơ', 500, 'text');
    await tapText('Tạo hồ sơ');
  } catch (e) {}
};

const postTaskICToStep4 = async (isNotChooseService) => {
  if (!isNotChooseService) {
    // await postTask('postTaskServiceINDUSTRIAL_CLEANING');
  }

  // Step 2 Chọn loại dịch vụ
  await tapId('homeTypeIC-HOME');
  await tapId('typeHomeTypeItem-newHouse');
  await tapId('itemAreaIC-area1');
  await tapId('btnNextIC');

  // Step 2 Add On
  await tapId('btnNextIC');

  // Step 3 Chọn thời gian
  await tapId('weekdays_2');
  await tapId('btnNextStep3');
};

module.exports = {
  callService,
  initData,
  // postTask,
  typeToTextField,
  typeToTextFieldAtIndex,
  tapId,
  tapIdAtIndex,
  tapText,
  tapTextAtIndex,
  tapAtPoint,
  tapHeaderBack,
  scrollTo,
  scroll,
  swipe,
  waitForElement,
  waitForElementAtIndex,
  waitForLoading,
  expectElementVisible,
  expectElementVisibleAtIndex,
  expectElementNotVisible,
  expectElementNotExist,
  expectIdToHaveText,
  expectIdToHaveValue,
  clearTextInput,
  checkElementVisible,
  // loginWithPhoneAndPassword,
  expectIdToHaveTextAtIndex,
  expectIdToHaveValueAtIndex,
  typePromotionCode,
  selectTime,
  selectTime24h,
  selectTimeAtIndex,
  // chooseMarketAddress,
  fillActiveCode,
  typeToTextFieldDishName,
  logout,
  typeToTextFieldSubmitKeyboard,
  cancelTask,
  houseKeepingPTCreateRoom,
  postTaskElderlyCare,
  postTaskPatientCare,
  postTaskMakeup,
  postTaskNail,
  postTaskHairStyling,
  // loginWithModal,
  signUpWithModal,
  forgotPasswordWithModal,
  chooseIsoCodeFromSetting,
  chooseAddressGrocery,
  postTaskGroceryAssistant,
  socketPaymentWithMoMo,
  socketPaymentWithZaloPay,
  socketPaymentWithShopeePay,
  socketPaymentWithVNPay,
  socketPaymentWith2c2p,
  socketPaymentWithShopeePayTH,
  reloadApp,
  formatMoney,
  concatStrings,
  postTaskDisinfection,
  checkPolicyCancel,
  clearTextInputAtIndex,
  ADDRESS_KEY,
  ADDRESS_VALUE,
  getDateFromWeekday,
  getPriceSubscription,
  updateCollectTimeLaundry,
  isWeekend,
  capitalize,
  sleep,
  tapIdService,
  postTaskOfficeCarpetCleaning,
  checkHomeTypeHomeMoving,
  tapTask,
  removeIntroHouseKeeping,
  postTaskICToStep4,
};
