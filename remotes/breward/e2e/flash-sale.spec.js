/* eslint-disable no-restricted-imports */
import dayjs from 'dayjs';

import TEXT from '../src/i18n/localization/vi.json';

const { device, element, by } = require('detox');

const {
  initData,
  tapText,
  tapId,
  waitForElement,
  expectElementNotVisible,
  waitForLoading,
  expectElementVisible,
} = require('./step-definition');

const USER_ASKER_DATA = {
  isoCode: 'VN',
  phone: '0834567890',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
};
const USER_TASKER_DATA = {
  isoCode: 'VN',
  phone: '0834567891',
  name: 'Tasker',
  type: 'TASKER',
  status: 'ACTIVE',
};

const USER_ASKER_DATA_UPDATE = {
  phone: USER_ASKER_DATA.phone,
  isoCode: USER_ASKER_DATA.isoCode,
  dataUpdate: {
    point: 1000,
    rankInfo: {
      point: 1000.0,
      rankName: 'MEMBER',
      text: {
        vi: 'Thành viên',
      },
    },
  },
};

describe('FILE: e2e/a-vietnam/flow-test/b-reward/flash-sale.spec.js - Asker see flash sale from bTaskee', () => {
  beforeEach(async () => {
    await device.reloadReactNative();
    await initData('resetData');
    await initData('user/createUser', [USER_ASKER_DATA, USER_TASKER_DATA]);
    await initData('user/updateUser', [USER_ASKER_DATA_UPDATE]);
  });

  it('LINE 59 - Book task Cleaning and redeem reward flash sale', async () => {
    const INCENTIVE_DATA_01 = {
      isShowHomePage: true,
      typeOfPromotion: 'PERCENTAGE',
      valueOfPromotion: 0.1,
      serviceName: 'CLEANING',
      isoCode: 'VN',
      title: 'Giảm giá 10% dịch vụ dọn dẹp nhà',
      point: 100,
      content:
        'bTaskee là công ty tiên phong ứng dụng nền tảng công nghệ vào ngành giúp việc gia đình ở Việt Nam, cho phép bạn cùng Tasker chủ động đăng và nhận việc trực tiếp trên ứng dụng.',
      note: 'Chỉ áp dụng trên app bTaskee',
    };
    const res = await initData(
      'incentives/createIncentives',
      INCENTIVE_DATA_01,
    );

    const FLASH_SALE_01 = {
      isoCode: 'VN',
      endDate: dayjs().add(2, 'day').toDate(),
      incentiveInfos: [
        {
          _id: res?.incentiveId,
          originalPoint: 100,
          point: 80,
        },
      ],
    };
    await initData('flash-sale/insert', FLASH_SALE_01);

    await device.reloadReactNative();

    await waitForElement('bRewards', 8000, 'text');

    await waitForElement('SeeMoreBRewardBtn', 8000);
    await tapId('SeeMoreBRewardBtn');

    await waitForElement('ScrollUserReward', 8000);
    await element(by.id('ScrollUserReward')).scroll(200, 'down');

    await waitForElement(INCENTIVE_DATA_01.title, 8000, 'text');
    await expectElementVisible(INCENTIVE_DATA_01.title, 'text');

    // Đổi quà
    await tapText(INCENTIVE_DATA_01.title);

    await waitForElement(TEXT.REDEEM_VOUCHER, 8000, 'text');
    await tapText(TEXT.REDEEM_VOUCHER);

    await waitForElement(TEXT.CONFIRM, 8000, 'text');
    await tapText(TEXT.CONFIRM);

    await waitForElement(TEXT.USED_GIFT, 8000, 'text');
    await tapText(TEXT.USED_GIFT);

    await waitForElement('Danh sách địa điểm', 8000, 'text');
    await expectElementVisible('Danh sách địa điểm', 'text');
  });

  it('LINE 119 - Asker not see flash sale', async () => {
    const INCENTIVE_DATA_01 = {
      isShowHomePage: true,
      typeOfPromotion: 'PERCENTAGE',
      valueOfPromotion: 0.1,
      serviceName: 'CLEANING',
      isoCode: 'VN',
      title: 'Giảm giá 10% dịch vụ dọn dẹp nhà',
      point: 100,
      content:
        'bTaskee là công ty tiên phong ứng dụng nền tảng công nghệ vào ngành giúp việc gia đình ở Việt Nam, cho phép bạn cùng Tasker chủ động đăng và nhận việc trực tiếp trên ứng dụng.',
      note: 'Chỉ áp dụng trên app bTaskee',
    };
    const res = await initData(
      'incentives/createIncentives',
      INCENTIVE_DATA_01,
    );

    const FLASH_SALE_01 = {
      isoCode: 'VN',
      endDate: dayjs().subtract(2, 'day').toDate(),
      incentiveInfos: [
        {
          _id: res?.incentiveId,
          originalPoint: 100,
          point: 80,
        },
      ],
    };
    await initData('flash-sale/insert', FLASH_SALE_01);

    await device.reloadReactNative();

    await waitForElement('SeeMoreBRewardBtn', 8000);
    await tapId('SeeMoreBRewardBtn');

    await expectElementNotVisible(INCENTIVE_DATA_01.title, 'text');
  });

  it('LINE 158 - Asker see flash sale and flash sale end', async () => {
    const INCENTIVE_DATA_01 = {
      isShowHomePage: true,
      typeOfPromotion: 'PERCENTAGE',
      valueOfPromotion: 0.1,
      serviceName: 'CLEANING',
      isoCode: 'VN',
      title: 'Giảm giá 10% dịch vụ dọn dẹp nhà',
      point: 100,
      content:
        'bTaskee là công ty tiên phong ứng dụng nền tảng công nghệ vào ngành giúp việc gia đình ở Việt Nam, cho phép bạn cùng Tasker chủ động đăng và nhận việc trực tiếp trên ứng dụng.',
      note: 'Chỉ áp dụng trên app bTaskee',
    };
    const res = await initData(
      'incentives/createIncentives',
      INCENTIVE_DATA_01,
    );

    const FLASH_SALE_01 = {
      isoCode: 'VN',
      endDate: dayjs().add(15, 'second').toDate(),
      incentiveInfos: [
        {
          _id: res?.incentiveId,
          originalPoint: 100,
          point: 80,
        },
      ],
    };
    await initData('flash-sale/insert', FLASH_SALE_01);
    await device.reloadReactNative();

    await waitForElement('bRewards', 8000, 'text');

    await waitForElement('SeeMoreBRewardBtn', 8000);
    await tapId('SeeMoreBRewardBtn');

    await waitForElement('ScrollUserReward', 8000);
    await element(by.id('ScrollUserReward')).scroll(200, 'down');

    await waitForElement(INCENTIVE_DATA_01.title, 8000, 'text');
    await expectElementVisible(INCENTIVE_DATA_01.title, 'text');

    await waitForLoading(10000);
    await waitForElement(TEXT.TITLE_END_FLASH_SALE, 8000, 'text');
    await expectElementVisible(TEXT.TITLE_END_FLASH_SALE, 'text');
    await tapText('OK');

    await expectElementNotVisible(INCENTIVE_DATA_01.title);
  });
});
