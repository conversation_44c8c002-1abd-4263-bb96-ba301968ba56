export const USER = {
  _id: 'userId_**********',
  services: {
    password: {
      bcrypt: '$2a$10$JB6kMoIYpyxO2ROr7C9Zc.azIQI1w1orh9K5J7D03etyYuWKZefvq',
    },
    loginTokens:
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************.6_e9d2XH9qMNilr5Y5vC9J5enhmQ240UQRu3yf9jzpY',
  },
  username: '***********',
  language: 'vi',
  referralCode: 'ASKER_**********',
  phone: '**********',
  isoCode: 'VN',
  taskDone: 0,
  totalTaskDone: 0,
  countryCode: '+84',
  fAccountId: 'financialAccount_**********',
  avgRating: 0,
  status: 'ACTIVE',
  type: 'ASKER',
  name: 'Asker',
  avatar:
    'https://img.docbao.vn/images/uploads/2019/06/30/xa-hoi/vo-ngoc-tran-4.jpg',
  appVersion: '4.0.9',
  workingPlaces: [
    { country: 'VN', city: 'Hồ Chí Minh', district: 'Quận 9' },
    { country: 'VN', city: 'Hồ Chí Minh', district: 'Quận 11' },
    { country: 'VN', city: 'Hồ Chí Minh', district: 'Quận 10' },
    { country: 'VN', city: 'Hồ Chí Minh', district: 'Bình Tân' },
  ],
  score: 10,
  updatePrivacyPolicyAt: '2025-07-13T04:32:56.247Z',
  locations: [
    {
      _id: 'x6001bc7692559edd072cf7e4f',
      lat: 10.7331278,
      lng: 106.706233,
      country: 'VN',
      city: 'Hồ Chí Minh',
      district: 'Quận 10',
      address:
        'Công ty TNHH bTaskee, Hẻm 284/25 Lý Thường Kiệt, phường 14, Quận 10, Hồ Chí Minh, Việt Nam',
      contact: 'Asker',
      phoneNumber: '**********',
      shortAddress: '284/25 Lý Thường Kiệt',
      countryCode: '+84',
      isoCode: 'VN',
      homeType: 'HOME',
      description: 'My Task',
    },
  ],
};
