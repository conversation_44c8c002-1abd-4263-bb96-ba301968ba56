/* eslint-disable no-restricted-imports */
import TEXT from '../src/i18n/localization/vi.json';

const {
  initData,
  tapText,
  expectElementVisible,
  tapId,
  waitForElement,
  swipe,
  tapIdAtIndex,
  expectElementVisibleAtIndex,
} = require('./step-definition');

const { device } = require('detox');

const USER_ASKER_DATA = {
  isoCode: 'VN',
  phone: '0834567890',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
};
const USER_TASKER_DATA = {
  isoCode: 'VN',
  phone: '0834567891',
  name: 'Tasker',
  type: 'TASKER',
  status: 'ACTIVE',
};

const USER_ASKER_DATA_UPDATE = {
  phone: USER_ASKER_DATA.phone,
  isoCode: USER_ASKER_DATA.isoCode,
  dataUpdate: {
    point: 1000,
    rankInfo: {
      point: 1000.0,
      rankName: 'MEMBER',
      text: {
        vi: 'Thành viên',
      },
    },
  },
};

describe('FILE: e2e/a-vietnam/flow-test/b-reward/redeem.spec.js - Asker see incentive from bTaskee', () => {
  beforeEach(async () => {
    await device.reloadReactNative();
    await initData('resetData');
    await initData('user/createUser', [USER_ASKER_DATA, USER_TASKER_DATA]);
    await initData('user/updateUser', [USER_ASKER_DATA_UPDATE]);
    await initData('task/createTask', [
      {
        serviceName: 'CLEANING',
        askerPhone: '0834567890',
        description: 'My Task',
        status: 'DONE',
        rated: true,
        isoCode: 'VN',
      },
    ]);
  });

  it('LINE 66 - Asker đổi quà ngay tại item bReward và đăng việc CLEANING', async () => {
    const INCENTIVE_DATA_01 = {
      typeOfPromotion: 'PERCENTAGE',
      valueOfPromotion: 0.1,
      serviceName: 'CLEANING',
      isoCode: 'VN',
      title: 'Giảm giá 10% dịch vụ dọn dẹp nhà',
      point: 100,
      content:
        'bTaskee là công ty tiên phong ứng dụng nền tảng công nghệ vào ngành giúp việc gia đình ở Việt Nam, cho phép bạn cùng Tasker chủ động đăng và nhận việc trực tiếp trên ứng dụng.',
      note: 'Chỉ áp dụng trên app bTaskee',
    };

    await initData('incentives/createIncentives', INCENTIVE_DATA_01);

    await waitForElement('Tab_Account', 8000);
    await expectElementVisible('Tab_Account');
    await tapId('Tab_Account');

    await swipe('AccountScroll', 'up');

    await waitForElement('bRewards', 8000, 'text');
    await expectElementVisible('bRewards', 'text');
    await tapText('bRewards');

    // Go to reward detail
    await waitForElement('CardBRewardBtn', 8000);
    await expectElementVisibleAtIndex('CardBRewardBtn', 0);
    await tapIdAtIndex('CardBRewardBtn', 0);

    await waitForElement(INCENTIVE_DATA_01.title, 8000, 'text');
    await expectElementVisible(INCENTIVE_DATA_01.title, 'text');
    await expectElementVisible(INCENTIVE_DATA_01.point.toString(), 'text');

    // Redeem gift
    await tapText(TEXT.REDEEM_VOUCHER);

    // Reddem success
    await tapText(TEXT.CONFIRM);

    await tapText(TEXT.USED_GIFT);
    await waitForElement('Danh sách địa điểm', 8000, 'text');
    await expectElementVisible('Danh sách địa điểm', 'text');
  });

  it('LINE 118 - Asker không đủ điểm đổi quà ngay (Vo hieu hoa "Đổi quà trên card bReward")', async () => {
    const INCENTIVE_DATA_01 = {
      typeOfPromotion: 'PERCENTAGE',
      valueOfPromotion: 0.1,
      serviceName: 'CLEANING',
      isoCode: 'VN',
      title: 'Giảm giá 10% dịch vụ dọn dẹp nhà',
      point: 100000,
      content:
        'bTaskee là công ty tiên phong ứng dụng nền tảng công nghệ vào ngành giúp việc gia đình ở Việt Nam, cho phép bạn cùng Tasker chủ động đăng và nhận việc trực tiếp trên ứng dụng.',
      note: 'Chỉ áp dụng trên app bTaskee',
    };

    await initData('incentives/createIncentives', INCENTIVE_DATA_01);

    await tapId('Tab_Account');

    await swipe('AccountScroll', 'up');

    await waitForElement('bRewards', 8000, 'text');
    await expectElementVisible('bRewards', 'text');
    await tapText('bRewards');
    // Go to reward detail

    await waitForElement('CardBRewardBtn', 8000);
    await expectElementVisibleAtIndex('CardBRewardBtn', 0);
    await tapIdAtIndex('CardBRewardBtn', 0);

    await waitForElement(INCENTIVE_DATA_01.title, 8000, 'text');
    await expectElementVisible(INCENTIVE_DATA_01.title, 'text');
    await expectElementVisible(INCENTIVE_DATA_01.point.toString(), 'text');

    await expectElementVisible(TEXT.NOT_ENOUGH_POINT, 'text');
  });
});
