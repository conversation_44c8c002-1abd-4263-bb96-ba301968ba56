import { apiRequest, ApiType, Endpoint<PERSON>eys, getIPAddress } from '../api';
import { IPaymentParams } from '../api/types/Base';
import {
  icBankBCA,
  icBankBNI,
  icBankBRI,
  icBankCIMB,
  icBankMandiri,
  icBankPermata,
  IconAssets,
} from '../assets';
import { Alert } from '../helpers';
import { i18n } from '../i18n';
import {
  HostParamsNavigationList,
  PaymentRouteName,
  PaymentStackParamList,
  RouteName,
  TabActivityRouteName,
} from '../navigation';
import { useAppLoadingStore, useAppStore } from '../stores';
import { useSettingsStore } from '../stores/Settings';
import { IBankInfo, IPaymentMethodInfo, Maybe, OmitKeys } from '../types';
import {
  ISO_CODE,
  PAYMENT_METHOD,
  SERVICES,
  TYPE_OF_PAYMENT,
} from './constant';
import { NavigationService } from './Navigation';

type ChoosePaymentMethodScreenParams =
  PaymentStackParamList[PaymentRouteName.ChoosePaymentMethod];

export class PaymentService {
  private static t = (key: string) => i18n.t(key, { ns: 'common' });

  private static getAllListPaymentMethod = (): Pick<
    IPaymentMethodInfo,
    'name' | 'label' | 'icon' | 'isPrepayment' | 'isBPayGroup'
  >[] => {
    return [
      {
        name: PAYMENT_METHOD.card,
        label: this.t('PAYMENT_METHOD_CARD'),
        icon: IconAssets.logoVisa,
      },
      {
        name: PAYMENT_METHOD.cash,
        label: this.t('PAYMENT_METHOD_DIRECT_CASH'),
        icon: IconAssets.logoCash,
      },
      {
        name: PAYMENT_METHOD.credit,
        label: this.t('PAYMENT_METHOD_INDIVIDUAL_ACCOUNT'),
        icon: IconAssets.logoBPay,
        isBPayGroup: true,
      },
      {
        name: PAYMENT_METHOD.bpayBenefit,
        label: this.t('PAYMENT_METHOD_BENEFIT_ACCOUNT'),
        icon: IconAssets.logoBPay,
        isBPayGroup: true,
      },
      {
        name: PAYMENT_METHOD.bPayBusiness,
        label: this.t('PAYMENT_METHOD_FLEXIBLE_ACCOUNT'),
        icon: IconAssets.logoBPay,
        isBPayGroup: true,
      },
      {
        name: PAYMENT_METHOD.bankTransfer,
        label: this.t('PAYMENT_METHOD_BANK_TRANSFER'),
        icon: IconAssets.logoATM,
        isPrepayment: true,
      },
      {
        name: PAYMENT_METHOD.momo,
        label: this.t('PAYMENT_METHOD_MOMO'),
        icon: IconAssets.logoMomo,
        isPrepayment: true,
      },
      {
        name: PAYMENT_METHOD.directTransfer,
        label: this.t('PAYMENT_METHOD_DIRECT_TRANSFER'),
        icon: IconAssets.logoDirectTransfer,
        isPrepayment: true,
      },
      {
        name: PAYMENT_METHOD.zaloPay,
        label: this.t('PAYMENT_METHOD_ZALO_PAY'),
        icon: IconAssets.logoZaloPay,
        isPrepayment: true,
      },
      {
        name: PAYMENT_METHOD.promptPay,
        label: this.t('PAYMENT_METHOD_PROMT_PAY'),
        icon: IconAssets.logoPromptPay,
        isPrepayment: true,
      },
      {
        name: PAYMENT_METHOD.trueMoney,
        label: this.t('PAYMENT_METHOD_TRUE_MONEY'),
        icon: IconAssets.logoTrueMoney,
        isPrepayment: true,
      },
      {
        name: PAYMENT_METHOD.shopeePay,
        label: this.t('PAYMENT_METHOD_SHOPEE_PAY'),
        icon: IconAssets.logoShopeePay,
        isPrepayment: true,
      },
      {
        name: PAYMENT_METHOD.tiki,
        label: this.t('PAYMENT_METHOD_TIKI'),
        icon: IconAssets.logoBPay,
        isPrepayment: true,
      },
      {
        name: PAYMENT_METHOD.vnPay,
        label: this.t('PAYMENT_METHOD_VN_PAY'),
        icon: IconAssets.logoVNPay,
        isPrepayment: true,
      },
      {
        name: PAYMENT_METHOD.idGoPay,
        label: this.t('PAYMENT_METHOD_GO_PAY'),
        icon: IconAssets.logoGoPay,
        isPrepayment: true,
      },
      {
        name: PAYMENT_METHOD.idQRIS,
        label: this.t('PAYMENT_METHOD_QRIS'),
        icon: IconAssets.logoQris,
        isPrepayment: true,
      },
      {
        name: PAYMENT_METHOD.idDANA,
        label: this.t('PAYMENT_METHOD_DANA'),
        icon: IconAssets.logoDana,
        isPrepayment: true,
      },
      {
        name: PAYMENT_METHOD.vietQR,
        label: this.t('PAYMENT_METHOD_VIET_QR'),
        icon: IconAssets.logoVietQR,
        isPrepayment: true,
      },
      {
        name: PAYMENT_METHOD.virtualAccount,
        label: this.t('PAYMENT_METHOD_VIRTUAL_ACCOUNT'),
        icon: IconAssets.icIdVA,
        isPrepayment: true,
      },
      {
        name: PAYMENT_METHOD.kredivo,
        label: this.t('PAYMENT_METHOD_KREDIVO'),
        icon: IconAssets.logoKredivo,
        isPrepayment: true,
      },
      {
        name: PAYMENT_METHOD.duitNow,
        label: this.t('PAYMENT_METHOD_DUIT_NOW'),
        icon: IconAssets.logoDuitNow,
        isPrepayment: true,
      },
      {
        name: PAYMENT_METHOD.touchNGo,
        label: this.t('PAYMENT_METHOD_TOUCH_N_GO'),
        icon: IconAssets.logoTouchNGo,
        isPrepayment: true,
      },
    ];
  };

  static getListBPayGroup = () => {
    return this.getAllListPaymentMethod().filter((e) => e.isBPayGroup);
  };

  static choosePaymentMethod = (
    params: OmitKeys<ChoosePaymentMethodScreenParams, 'onSelected'>,
  ): Promise<
    Parameters<NonNullable<ChoosePaymentMethodScreenParams['onSelected']>>[0]
  > => {
    return new Promise((resolve) => {
      NavigationService.navigate(RouteName.Payment, {
        screen: PaymentRouteName.ChoosePaymentMethod,
        params: {
          ...params,
          onSelected: (paymentMethodInfo) => {
            resolve(paymentMethodInfo);
            NavigationService.goBack();
          },
        },
      });
    });
  };

  static onPostTaskSuccess = async (
    params?: HostParamsNavigationList[RouteName.PostTaskSuccess],
  ) => {
    const bookingId = params?.bookingId;
    if (!bookingId) return;

    if (params?.isPrepayment && !params?.isPaid) {
      await this.handlePrepayment(bookingId);
      return;
    }

    NavigationService.navigateToTabActivity(TabActivityRouteName.TabUpcoming);
    NavigationService.navigate(RouteName.PostTaskSuccess, params);
  };

  static onPostTaskSubscriptionSuccess = async ({
    paymentMethod,
    data,
  }: {
    paymentMethod?: IPaymentMethodInfo;
    data?: ApiType[EndpointKeys.prepaymentTask]['response'];
  }) => {
    const paymentMethodInfo = this.getPaymentInfoByName(paymentMethod?.name);

    NavigationService.navigateToTabActivity(TabActivityRouteName.TabMonthly);

    if (paymentMethodInfo?.isPrepayment) {
      this.directByPrepaymentMethod({
        paymentMethod: paymentMethodInfo?.name,
        bookingId: data?.orderId,
        data,
      });
      return;
    }

    NavigationService.navigate(RouteName.PostTaskSubscriptionSuccess);
  };

  static onTopUpSuccess = async ({
    paymentMethod,
    data,
  }: {
    paymentMethod?: IPaymentMethodInfo;
    data?: ApiType[EndpointKeys.prepaymentTask]['response'];
  }) => {
    const paymentMethodInfo = this.getPaymentInfoByName(paymentMethod?.name);

    if (!paymentMethodInfo?.isPrepayment) return;

    this.directByPrepaymentMethod({
      paymentMethod: paymentMethodInfo?.name,
      data,
    });
  };

  static handlePrepayment = async (bookingId: string) => {
    const { showLoading, hideLoading } = useAppLoadingStore.getState();
    try {
      showLoading();
      const shopperIP = await getIPAddress();
      const response = await apiRequest({
        key: EndpointKeys.prepaymentTask,
        params: {
          taskId: bookingId,
          shopperIP,
        },
      });
      NavigationService.navigateToTabActivity(TabActivityRouteName.TabUpcoming);
      this.directByPrepaymentMethod({
        paymentMethod: response?.payment?.method,
        bookingId,
        data: response,
      });
    } catch (error: any) {
      Alert.alert.open({
        title: this.t('DIALOG_TITLE_INFORMATION'),
        message:
          error.data.code === 'TASK_ALREADY_PAID'
            ? this.t('TASK_PAID')
            : this.t('ERROR_TRY_AGAIN'),
        actions: [{ text: this.t('CLOSE') }],
      });
    } finally {
      hideLoading();
    }
  };

  static directByPrepaymentMethod = ({
    paymentMethod,
    bookingId,
    data,
  }: {
    paymentMethod?: PAYMENT_METHOD;
    bookingId?: string;
    data?: ApiType[EndpointKeys.prepaymentTask]['response'];
  }) => {
    if (!paymentMethod) return;

    try {
      const amount = data?.amount || 0;
      if (!paymentMethod) return;

      switch (paymentMethod) {
        case PAYMENT_METHOD.vietQR:
          if (!data?.qrString || !bookingId) return;
          NavigationService.navigate(RouteName.Payment, {
            screen: PaymentRouteName.PayWithVietQR,
            params: {
              amount,
              paymentMethod,
              qrString: data?.qrString,
              expiryTimer: data?.expiryTimer,
              taskId: bookingId,
            },
          });
          break;

        case PAYMENT_METHOD.directTransfer:
          NavigationService.navigate(RouteName.Payment, {
            screen: PaymentRouteName.PayWithDirectTransfer,
            params: {
              taskPlace: data?.taskPlace,
              orderId: data?.orderId,
              phone: data?.phone,
              cost: data?.cost,
            },
          });
          break;

        case PAYMENT_METHOD.virtualAccount:
          NavigationService.navigate(RouteName.Payment, {
            screen: PaymentRouteName.PayWithVA,
            params: {
              amount: data?.amount,
              userManual: data?.userManual,
              virtualAccountNumber: data?.virtualAccountNumber,
              expiryTimer: data?.expiryTimer,
              payment: data?.payment,
            },
          });
          break;

        case PAYMENT_METHOD.trueMoney:
          if (!data?.data || typeof data.data !== 'string') return;
          NavigationService.navigate(RouteName.WebView, {
            source: { uri: data.data },
          });
          break;

        case PAYMENT_METHOD.idQRIS:
        case PAYMENT_METHOD.promptPay:
        case PAYMENT_METHOD.duitNow:
          NavigationService.navigate(RouteName.Payment, {
            screen: PaymentRouteName.PayWithQRCode,
            params: {
              amount,
              paymentMethod,
              expiryTimer: data?.expiryTimer,
              taskId: bookingId,
              qrString: data?.qrString,
              qrUrl: data?.qrUrl,
            },
          });
          break;

        case PAYMENT_METHOD.momo:
          NavigationService.navigate(RouteName.Payment, {
            screen: PaymentRouteName.PaymentConfirmedAppToApp,
            params: {
              amount,
              paymentMethod,
              url: data?.payUrl,
            },
          });
          break;

        case PAYMENT_METHOD.zaloPay:
          NavigationService.navigate(RouteName.Payment, {
            screen: PaymentRouteName.PaymentConfirmedAppToApp,
            params: {
              amount,
              paymentMethod,
              url: data?.url,
            },
          });
          break;

        case PAYMENT_METHOD.shopeePay:
          NavigationService.navigate(RouteName.Payment, {
            screen: PaymentRouteName.PaymentConfirmedAppToApp,
            params: {
              amount,
              paymentMethod,
              url: data?.redirect_url_http,
              urlApp: data?.redirect_url_app,
            },
          });
          break;

        case PAYMENT_METHOD.idGoPay:
        case PAYMENT_METHOD.vnPay:
        case PAYMENT_METHOD.idDANA:
        case PAYMENT_METHOD.kredivo:
        case PAYMENT_METHOD.touchNGo:
        default:
          const url = data?.paymentUrl;
          NavigationService.navigate(RouteName.Payment, {
            screen: PaymentRouteName.PaymentConfirmedAppToApp,
            params: {
              amount,
              paymentMethod,
              url,
            },
          });
          break;
      }
    } catch (error: any) {
      Alert.alert.open({
        title: this.t('DIALOG_TITLE_INFORMATION'),
        message:
          error.data.code === 'TASK_ALREADY_PAID'
            ? this.t('TASK_PAID')
            : this.t('ERROR_TRY_AGAIN'),
        actions: [{ text: this.t('CLOSE') }],
      });
    }
  };

  static getAllBanksVA = (): IBankInfo[] => {
    const isoCode = useAppStore.getState().isoCode;
    switch (isoCode) {
      case ISO_CODE.ID:
        return [
          {
            name: 'permata',
            text: this.t('BANKS.PERMATA'),
            icon: icBankPermata,
          },
          {
            name: 'bca',
            text: this.t('BANKS.BCA'),
            icon: icBankBCA,
          },
          {
            name: 'cimb',
            text: this.t('BANKS.CIMB'),
            icon: icBankCIMB,
          },
          {
            name: 'bri',
            text: this.t('BANKS.BRI'),
            icon: icBankBRI,
          },
          {
            name: 'bni',
            text: this.t('BANKS.BNI'),
            icon: icBankBNI,
          },
          {
            name: 'mandiri',
            text: this.t('BANKS.MANDIRI'),
            icon: icBankMandiri,
          },
        ];

      default:
        return [];
    }
  };

  static getBankInfoByName = (name?: string) => {
    if (!name) return;
    return this.getAllBanksVA().find((e) => e.name === name);
  };

  static getPaymentMethods() {
    const settingStore = useSettingsStore.getState();
    return settingStore?.settings?.settingSystem?.paymentMethods;
  }

  static getPaymentMethodsByType(type: TYPE_OF_PAYMENT) {
    const paymentMethodsStore = this.getPaymentMethods();
    return paymentMethodsStore?.[type] || [];
  }

  static getPaymentInfoByName(paymentMethodName?: PAYMENT_METHOD) {
    const listAllPaymentMethod = this.getAllListPaymentMethod();
    const matchItem = listAllPaymentMethod.find(
      (item) => item.name === paymentMethodName,
    );

    return matchItem;
  }

  // Get default payment method
  static getDefaultPaymentMethod = (
    options?: Maybe<{
      type?: TYPE_OF_PAYMENT;
      serviceName?: SERVICES;
    }>,
  ) => {
    const type = options?.type || TYPE_OF_PAYMENT.bookTask;
    const serviceName = options?.serviceName;
    if (
      serviceName &&
      type === TYPE_OF_PAYMENT.bookTask &&
      [
        SERVICES.HOME_MOVING,
        SERVICES.INDUSTRIAL_CLEANING,
        SERVICES.OFFICE_CLEANING,
        SERVICES.OFFICE_CLEANING_SUBSCRIPTION,
        SERVICES.CLEANING_SUBSCRIPTION,
      ].includes(serviceName)
    ) {
      return this.getPaymentInfoByName(PAYMENT_METHOD.credit);
    }
    return this.getPaymentInfoByName(PAYMENT_METHOD.cash);
  };

  static showTrueMoneyAccount = (account = '') => {
    let result = '';
    const length = account.length;
    if (length > 0) {
      const charcterNeedHide = account
        .substring(0, length - 4)
        .split('')
        .map((e) => '*')
        .join('');
      const charcterNeedShow = account.substring(length - 4, length);
      result = charcterNeedHide + charcterNeedShow;
    }
    return result;
  };

  static formatPaymentMethodInfoToParams = (
    paymentMethodInfo?: IPaymentMethodInfo,
  ): IPaymentParams => {
    if (!paymentMethodInfo) return {};
    const payment: IPaymentParams = {
      method: paymentMethodInfo?.name,
    };

    if (paymentMethodInfo.cardInfo?._id) {
      payment.cardId = paymentMethodInfo.cardInfo?._id;
    }

    if (paymentMethodInfo.bank?.name) {
      payment.bank = paymentMethodInfo.bank.name;
    }

    if (paymentMethodInfo.walletInfo) {
      payment.phoneNumber = paymentMethodInfo.walletInfo.phoneNumber;
      payment.countryCode = paymentMethodInfo.walletInfo.countryCode;
      payment.isSave = paymentMethodInfo.walletInfo.isSave;
    }
    return payment;
  };

  static getBlackListDefault = ({
    serviceName,
  }: {
    serviceName?: SERVICES;
  }) => {
    switch (serviceName) {
      case SERVICES.INDUSTRIAL_CLEANING:
      case SERVICES.OFFICE_CLEANING:
        return [PAYMENT_METHOD.cash];
      case SERVICES.HOME_MOVING:
        return [PAYMENT_METHOD.cash, PAYMENT_METHOD.card];
      default:
        return [];
    }
  };
}
