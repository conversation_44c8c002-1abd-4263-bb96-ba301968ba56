/**
 * Route Checker Utilities for Super-App-Workspace
 *
 * Lightweight utilities for checking route existence in navigation stacks.
 * Allows manual navigation decision-making based on current navigation state.
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { navigationRef } from './Navigation';

/**
 * Check if a specific route exists in the current navigation stack
 *
 * @param routeName - The route name to check for existence
 * @returns boolean indicating if the route exists in the stack
 *
 * @example
 * ```typescript
 * const chatExists = checkMicroserviceRouteExists(RouteName.ChatManagement);
 * if (chatExists) {
 *   // Chat microservice is already in the stack
 * }
 * ```
 */
export const checkMicroserviceRouteExists = (routeName: string): boolean => {
  if (!routeName) {
    return false;
  }

  try {
    const globalState = navigationRef?.getState();
    const routes = globalState?.routes || [];

    const exist = routes.find((route: any) => route.name === routeName);
    return Boolean(exist);
  } catch (error) {
    console.warn('Error checking route existence:', error);
    return false;
  }
};

/**
 * Check if a specific nested screen route exists within a microservice's navigation stack
 *
 * @param microserviceRoute - The main microservice route (e.g., RouteName.ChatManagement)
 * @param nestedRoute - The nested route within the microservice (e.g., ChatRouteName.ChatMessage)
 * @returns boolean indicating if the nested route exists in the microservice stack
 *
 * @example
 * ```typescript
 * const chatMessageExists = checkNestedScreenRouteExists(
 *   RouteName.ChatManagement,
 *   ChatRouteName.ChatMessage
 * );
 *
 * if (chatMessageExists) {
 *   // ChatMessage screen already exists, navigate to it
 *   NavigationService.navigate(RouteName.ChatManagement, {
 *     screen: ChatRouteName.ChatMessage,
 *     params: chatParams,
 *   });
 * } else {
 *   // ChatMessage doesn't exist, create new instance
 *   NavigationService.navigate(RouteName.ChatManagement, {
 *     screen: ChatRouteName.ChatMessage,
 *     params: chatParams,
 *   });
 * }
 * ```
 */
export const checkNestedScreenRouteExists = (
  microserviceRoute: string,
  nestedRoute: string,
): boolean => {
  if (!microserviceRoute || !nestedRoute) {
    return false;
  }

  try {
    const globalState = navigationRef?.getState();
    const globalRoutes = globalState?.routes || [];

    // Find the microservice route in the stack
    const microserviceRouteIndex = globalRoutes.findIndex(
      (route: any) => route.name === microserviceRoute,
    );

    if (microserviceRouteIndex === -1) {
      return false;
    }

    const microserviceRouteState = globalRoutes[microserviceRouteIndex];
    const nestedRoutes = microserviceRouteState?.state?.routes || [];

    // Check if the nested route exists within the microservice
    const nestedRouteExists = nestedRoutes.find(
      (route: any) => route.name === nestedRoute,
    );

    return Boolean(nestedRouteExists);
  } catch (error) {
    console.warn('Error checking nested route existence:', error);
    return false;
  }
};

/**
 * Get current navigation stack information for debugging
 * Useful for understanding the current navigation state
 *
 * @returns Object containing current route information or null if error
 *
 * @example
 * ```typescript
 * const stackInfo = getNavigationStackInfo();
 * console.log('Current route:', stackInfo?.currentRoute);
 * console.log('Stack depth:', stackInfo?.stackDepth);
 * console.log('All routes:', stackInfo?.routes);
 * ```
 */
export const getNavigationStackInfo = () => {
  try {
    const globalState = navigationRef?.getState();
    const currentRoute = navigationRef?.getCurrentRoute();

    return {
      currentRoute: currentRoute?.name,
      currentParams: currentRoute?.params,
      stackDepth: globalState?.routes?.length || 0,
      routes:
        globalState?.routes?.map((route: any) => ({
          name: route.name,
          hasNestedRoutes: Boolean(route.state?.routes?.length),
          nestedRoutesCount: route.state?.routes?.length || 0,
          nestedRoutes:
            route.state?.routes?.map((nestedRoute: any) => nestedRoute.name) ||
            [],
        })) || [],
    };
  } catch (error) {
    console.warn('Error getting navigation stack info:', error);
    return null;
  }
};

/**
 * Get all nested routes within a specific microservice
 * Useful for understanding what screens are currently active in a microservice
 *
 * @param microserviceRoute - The main microservice route to inspect
 * @returns Array of nested route names or empty array if not found
 *
 * @example
 * ```typescript
 * const chatRoutes = getMicroserviceRoutes(RouteName.ChatManagement);
 * console.log('Active chat routes:', chatRoutes);
 * // Output: ['Chat/Main', 'Chat/ChatMessage', 'Chat/ChatHistory']
 * ```
 */
export const getMicroserviceRoutes = (microserviceRoute: string): string[] => {
  if (!microserviceRoute) {
    return [];
  }

  try {
    const globalState = navigationRef?.getState();
    const globalRoutes = globalState?.routes || [];

    // Find the microservice route in the stack
    const microserviceRouteState = globalRoutes.find(
      (route: any) => route.name === microserviceRoute,
    );

    if (!microserviceRouteState) {
      return [];
    }

    const nestedRoutes = microserviceRouteState?.state?.routes || [];
    return nestedRoutes.map((route: any) => route.name);
  } catch (error) {
    console.warn('Error getting microservice routes:', error);
    return [];
  }
};

/**
 * Check if the current route matches a specific route name
 * Useful for conditional rendering or behavior based on current screen
 *
 * @param routeName - The route name to check against current route
 * @returns boolean indicating if current route matches
 *
 * @example
 * ```typescript
 * const isOnChatScreen = isCurrentRoute(ChatRouteName.ChatMessage);
 * if (isOnChatScreen) {
 *   // Show chat-specific UI
 * }
 * ```
 */
export const isCurrentRoute = (routeName: string): boolean => {
  if (!routeName) {
    return false;
  }

  try {
    const currentRoute = navigationRef?.getCurrentRoute();
    return currentRoute?.name === routeName;
  } catch (error) {
    console.warn('Error checking current route:', error);
    return false;
  }
};

/**
 * Type definitions for navigation stack information
 */
export interface NavigationStackInfo {
  currentRoute?: string;
  currentParams?: any;
  stackDepth: number;
  routes: Array<{
    name: string;
    hasNestedRoutes: boolean;
    nestedRoutesCount: number;
    nestedRoutes: string[];
  }>;
}
