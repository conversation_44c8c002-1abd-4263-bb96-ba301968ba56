import { useCallback } from 'react';
import { find, isEmpty } from 'lodash-es';

import {
  Alert,
  DateTimeHelpers,
  formatMoney,
  getCurrency,
  getIsoCodeGlobal,
  getTextWithLocale,
  TypeFormatDate,
} from '../helpers';
import { AccountRouteName, RouteName } from '../navigation';
import { useSettingsStore, useUserStore } from '../stores';
import { IPaymentMethodInfo, IPromotion } from '../types';
import {
  ISO_CODE,
  NavigationService,
  PAYMENT_METHOD,
  PaymentService,
} from '../utils';
import { useI18n } from './useI18n';

interface GetPaymentMethodWhenBookingProps {
  promotion?: IPromotion;
  price?: any;
  paymentMethod?: IPaymentMethodInfo;
}

export const usePostTaskAction = () => {
  const { t } = useI18n('common');

  const getPaymentMethodWhenBooking = useCallback(
    ({ promotion, price, paymentMethod }: GetPaymentMethodWhenBookingProps) => {
      const { financialAccount } = useUserStore.getState();
      const { settings } = useSettingsStore.getState();
      const isoCode = getIsoCodeGlobal();
      const bookTaskPaymentMethod =
        settings?.settingSystem?.paymentMethods?.bookTask;

      // Neu promotion co payment method thi khong set lai payment method
      if (!isEmpty(promotion?.paymentMethods)) {
        const firstPaymentMethod = promotion?.paymentMethods?.[0];
        return PaymentService.getPaymentInfoByName(firstPaymentMethod);
      }
      // Neu co PTTT uu tien thi khong set lai
      // if (postTask?.isPreferredPaymentMethod) {
      //   return;
      // }
      const creditMethod = PaymentService.getPaymentInfoByName(
        PAYMENT_METHOD.credit,
      );
      const cardMethod = PaymentService.getPaymentInfoByName(
        PAYMENT_METHOD.card,
      );

      // check payment method current if payment === cask => return
      if (paymentMethod?.name !== PAYMENT_METHOD.cash) {
        return;
      }

      // Ưu tiên credit nếu số tiền bPay >= price
      if (
        financialAccount?.FMainAccount &&
        financialAccount?.FMainAccount >= price?.finalCost
      ) {
        return creditMethod;
      }

      // // Ưu tiên lấy payment card (nếu có)
      // let card = null;
      // if (!isEmpty(paymentCardList?.cards)) {
      //   card = checkCardIsDefault(paymentCardList?.cards);
      // }
      // // save card to default paymentMethod
      // if (card) {
      //   return dispatch(setPaymentMethod({ ...cardMethod, cardInfo: card }));
      // }

      // check bPay enough and save to paymentMethod
      // Tet book task return bPay
      // if (paymentCardList?.bPayBalance >= price?.finalCost || service?.isTet) {
      //   return dispatch(setPaymentMethod(creditMethod));
      // }

      // Lấy Default Payment từ settingCountry
      const defaultPaymentMethod = find(
        bookTaskPaymentMethod,
        (payment) => payment?.isDefault,
      );
      const defaultMethodByCountry = {
        VN: PAYMENT_METHOD.cash,
        TH: PAYMENT_METHOD.credit,
        ID: PAYMENT_METHOD.idGoPay,
        MY: PAYMENT_METHOD.cash,
      };
      let defaultPaymentBookTask = PaymentService.getPaymentInfoByName(
        defaultPaymentMethod?.name || defaultMethodByCountry?.[isoCode!],
      );

      // Set default bank for virtual account Indonesia
      if (
        defaultPaymentBookTask?.name === PAYMENT_METHOD.virtualAccount &&
        isoCode === ISO_CODE.ID
      ) {
        // Lấy ngân hàng đầu tiên là Ngân hàng mặc định. Nếu muốn đổi chỉ cần set lại trên DB
        const bankDefault = defaultPaymentMethod?.banks[0];
        // Lấy thông tin ngân hàng
        const bankInfo = PaymentService.getBankInfoByName(bankDefault.name);
        defaultPaymentBookTask = {
          ...defaultPaymentBookTask,
          bank: bankDefault?.name,
          label: bankInfo?.name,
          icon: bankInfo?.icon,
        };
      }
      return defaultPaymentBookTask;
    },
    [],
  );

  const handlePostTaskError = useCallback(
    (resultError: any) => {
      const error = resultError?.data || {};
      const alertObj: any = {
        title: t('DIALOG_TITLE_INFORMATION'),
        message: t('ERROR_TRY_AGAIN'),
        actions: [{ text: t('CLOSE') }],
      };
      switch (error?.code) {
        case 'NOT_ENOUGH_MONEY':
        case 'NOT_ENOUGH_BPAY_BENEFIT':
          return Alert.alert.open({
            title: t('DIALOG_TITLE_INFORMATION'),
            message: t('BPAY_LACK_OF_MONEY', {
              cost: formatMoney(error?.data?.amount || 0),
              currency: getCurrency(),
            }),
            actions: [
              { text: t('CLOSE'), style: 'cancel' },
              {
                text: t('ADD_MONEY'),
                onPress: () =>
                  NavigationService.navigate(RouteName.TabAccountNavigator, {
                    screen: AccountRouteName.BPay,
                  }),
              },
            ],
          });
        case 'NOT_ENOUGH_BPAY_BUSINESS':
          return Alert.alert.open({
            title: t('DIALOG_TITLE_INFORMATION'),
            message: t('MESSAGE_NOT_ENOUGH_BPAY_BUSINESS', {
              cost: formatMoney(error?.data?.amount || 0),
              currency: getCurrency(),
            }),
            actions: [
              {
                text: t('CLOSE'),
                style: 'cancel',
              },
              {
                text: t('ADD_MONEY'),
                onPress: () =>
                  NavigationService.navigate(RouteName.TabAccountNavigator, {
                    screen: AccountRouteName.BPay,
                  }),
              },
            ],
          });

        case 'DATE_TIME_ERROR':
          alertObj.message = t('INCORRECT_DATE_TIME');
          break;
        case 'OUTSTANDING_PAYMENT_STATUS_NEW':
          return Alert.alert.open(
            {
              title: t('DIALOG_TITLE_INFORMATION'),
              message: t('OUTSTANDING_PAYMENT_STATUS_NEW'),
              actions: [
                {
                  text: t('PAYMENT_TOP_UP'),
                  onPress: () => {
                    NavigationService.navigate(
                      RouteName.OutStandingPaymentDebt,
                    );
                  },
                },
              ],
            },
            true,
          );
        case 'PAYMENT_CARD_EXPIRED':
          alertObj.message = t('PAYMENT_CARD_EXPIRED');
          break;
        case 'USER_STATUS_DISABLED':
          alertObj.message = t('CANCEL_TASK_DISABLED_CONTENT');
          break;
        case 'BOOKING_DATE_INVALID':
          alertObj.message = t('STEP4_ERROR_DATE_TIME');
          break;
        case 'BPAY_DEBT':
          return Alert.alert.open({
            title: t('DIALOG_TITLE_INFORMATION'),
            message: t('STEP4_BPAY_DEBT'),
            actions: [
              {
                text: t('PAYMENT_TOP_UP'),
                onPress: () =>
                  NavigationService.navigate(RouteName.TabAccountNavigator, {
                    screen: AccountRouteName.BPay,
                  }),
              },
            ],
          });
        case 'SERVICE_PAUSE':
          const reason = error?.data?.reason;
          if (reason) {
            alertObj.message =
              getTextWithLocale(reason) || t('SERVICE_TEMPORARILY_PAUSED');
          }
          break;
        case 'BOOKING_IS_DUPLICATE':
          alertObj.message = t('STEP4_ERROR_BOOKING_IS_DUPLICATE');
          break;
        case 'DESCRIPTION_LIMIT':
          alertObj.message = t('STEP4_ERROR_BOOKING_DESCRIPTION_LIMIT');
          break;
        case 'COUNTRY_CODE_INVALID':
          alertObj.message = t('COUNTRY_CODE_INVALID');
          alertObj.actions = [
            {
              text: t('OK'),
              onPress: () => {
                NavigationService.popToTop();
                NavigationService.navigate(RouteName.TabAccountNavigator, {
                  screen: AccountRouteName.UserEditProfile,
                });
              },
            },
          ];
          break;
        case 'SERVICE_NOT_STARTED':
          const beginDate = error?.data?.beginDate;
          if (beginDate) {
            const formattedDate = DateTimeHelpers.formatToString({
              timezone: DateTimeHelpers.getTzDevice(),
              date: beginDate,
              typeFormat: TypeFormatDate.DateShort,
              keepLocalTime: true,
            });
            return Alert.alert.open({
              title: t('DIALOG_TITLE_INFORMATION'),
              message: t('ERROR_SERVICE_NOT_STARTED', {
                date: formattedDate,
              }),
              actions: [
                {
                  text: t('OK'),
                },
              ],
            });
          }
          break;
        case 'PAYMENT_METHOD_NOT_APPLY_PROMOTION_CODE':
          // TODO: Hải update handle error này cho prepayment
          break;
        default:
          break;
      }

      if (error?.errorText) {
        alertObj.message =
          getTextWithLocale(error?.errorText) || alertObj.message;
      }

      // Show error alert using new design system
      Alert.alert.open(alertObj);
    },
    [t],
  );
  return { getPaymentMethodWhenBooking, handlePostTaskError };
};
