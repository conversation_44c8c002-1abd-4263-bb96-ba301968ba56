import { useCallback } from 'react';
import { get } from 'lodash-es';

import {
  AccountRouteName,
  BRewardRouteName,
  ChatRouteName,
  RouteName,
  TaskManagementRouteName,
  VoiceChatRouteName,
} from '../navigation';
import { NavigationService } from '../utils';

export enum NotificationType {
  TASK = 34,
  COMBO_VOUCHER = 35,
  PREPAYMENT = 25,
  FINANCIAL = 29,
  BLOCKED = 7,
  SUBSCRIPTION = 36,
  SURVEY = 31,
  AI_AGENT = 99,
}

export const useNotificationHandler = () => {
  /**
   * Handles subscription suggestion navigation
   * Purpose: Processes subscription notification and navigates to post task screen
   * @param notify - Notification item containing subscription data
   */
  const onViewSubscriptionSuggest = useCallback(async (notify: any) => {
    const subscriptionRequestId = get(
      notify,
      'data.subscriptionRequestId',
      null,
    );
    if (!subscriptionRequestId) {
      return null;
    }

    try {
      // TODO: Implement getSubscriptionRequest API call when available in design system
      // const result = await getSubscriptionRequest(subscriptionRequestId);
      //
      // if (result?.isSuccess && result.data) {
      //   const service = settings?.services?.find(
      //     (ser) => ser?.name === SERVICES.CLEANING_SUBSCRIPTION
      //   );
      //   if (!service) return null;
      //
      //   // Navigate to post task step 2 with subscription data
      //   NavigationService.navigate(RouteName.PostTaskStep2, {
      //     requestSubscriptionData: result.data
      //   });
      // }
      // TODO: Implement subscription suggestion navigation with API integration
    } catch (error) {
      console.error('Error handling subscription suggestion:', error);
    }
  }, []);

  /**
   * Navigates to task detail screen
   * Purpose: Helper function to navigate to task detail with proper error handling
   * @param taskId - ID of the task to view
   */
  const goToTaskDetail = useCallback((taskId?: string) => {
    if (!taskId) {
      return null;
    }
    return NavigationService.navigate(RouteName.TaskManagement, {
      screen: TaskManagementRouteName.TaskDetail,
      params: { taskId },
    });
  }, []);

  const onPressNotification = useCallback(
    async (notify: any): Promise<string | null> => {
      // Update local notification state
      const chatId = get(notify, 'chatId', null);
      const taskId = get(notify, 'taskId', null);
      const surveyId = notify.surveyId;
      const type = Number(notify.type || 0) as NotificationType;

      // Handle navigation based on notification type
      switch (type) {
        case 6: // Add money notification
          // TODO: Navigate to bPay screen - implement correct navigation
          break;

        case 20: // Notify asker about cost of task was changed
          // Navigate back to your posts (handled by main navigation)
          break;

        case 19: // Notify user if user's financial account is too low
        case 29: // Notify user about payment result
          // TODO: Navigate to bPay screen - implement correct navigation
          break;

        case 28: // Chat notification
          if (taskId) {
            NavigationService.navigate(RouteName.ChatManagement, {
              screen: ChatRouteName.ChatMessage,
              params: { task: { _id: taskId } },
            });
          } else if (chatId) {
            NavigationService.navigate(RouteName.ChatManagement, {
              screen: ChatRouteName.ChatMessage,
              params: { chatId },
            });
          }
          break;

        case 32: // Point, gift notification
          // TODO: Navigate to member detail screen - implement correct navigation
          break;

        case 36: // Subscription notification
          await onViewSubscriptionSuggest(notify);
          break;

        case 25: // New feature notification (specify dialog with content)
          if (notify?.navigateTo === 'MyRewards') {
            NavigationService.navigate(RouteName.BReward, {
              screen: BRewardRouteName.MyReward,
            });
          } else {
            // Navigate to notification detail screen
            NavigationService.navigate(RouteName.ChatManagement, {
              screen: ChatRouteName.NotificationDetail,
              params: {
                data: notify,
              },
            });
          }
          break;

        // case 30: // New feature notification (specify dialog with content)
        //   NavigationService.navigate(RouteName.ChatManagement, {
        //     screen: ChatRouteName.NotificationDetail,
        //     params: { data: notify },
        //   });
        //   break;

        // case 34: // Navigate to update dishes screen
        //   if (taskId) {
        //     // TODO: Implement UpdateDishesHomeCooking navigation when available
        //     console.log(
        //       'Navigate to UpdateDishesHomeCooking - TODO: Implement',
        //     );
        //   }
        //   break;

        case 1: // Tasker accept notification
          if (taskId) {
            goToTaskDetail(taskId);
          }
          break;

        case 3: // Require rating notification
          if (taskId) {
            // TODO: Navigate to rating screen - implement correct navigation
          }
          break;

        case 5: // Change tasker notification
        case 10: // Notify user task has been cancelled by system
          if (taskId) {
            goToTaskDetail(taskId);
          }
          break;

        case 13: // Notify asker that his task was change tasker or become 'POSTED'
          if (taskId) {
            goToTaskDetail(taskId);
          }
          break;

        case 14: // Notify asker that task date change
          if (taskId) {
            goToTaskDetail(taskId);
          }
          break;

        case 16: // Notify asker about new task is generated by schedule
          // TODO: Navigate to activity tab - implement correct navigation
          break;

        case 18: // Notify tasker WAITING task will expired
          if (taskId) {
            goToTaskDetail(taskId);
          }
          break;

        case 17: // Notify tasker POSTED task will expired
          if (taskId) {
            goToTaskDetail(taskId);
          }
          break;

        case 24: // Email was verified
          // No specific navigation needed
          break;

        case 26: // Post task error
          // No specific navigation needed
          break;

        case 31:
          NavigationService.navigate(RouteName.TabAccountNavigator, {
            screen: AccountRouteName.Survey,
            params: { surveyId },
          });
          break;

        case NotificationType.AI_AGENT:
          NavigationService.navigate(RouteName.VoiceChat, {
            screen: VoiceChatRouteName.Home,
          });
          break;

        default:
          NavigationService.popToTop();
          break;
      }

      return 'NO_BREAK';
    },
    [goToTaskDetail, onViewSubscriptionSuggest],
  );
  return {
    onPressNotification,
  };
};
