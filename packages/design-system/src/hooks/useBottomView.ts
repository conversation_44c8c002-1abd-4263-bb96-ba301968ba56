import { useReanimatedKeyboardAnimation } from 'react-native-keyboard-controller';
import { useAnimatedStyle } from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { Spacing } from '../tokens';

export const useBottomView = () => {
  const { progress } = useReanimatedKeyboardAnimation();
  const insets = useSafeAreaInsets();

  const animatedStyle = useAnimatedStyle(() => {
    return {
      paddingBottom:
        progress.value <= 0
          ? insets.bottom || Spacing.SPACE_16
          : Spacing.SPACE_16,
    };
  });
  return { animatedStyle };
};
