import React, { ReactNode } from 'react';

import { IconAssets } from '../../../assets';
import { Colors } from '../../../tokens';
import { BlockView } from '../../block-view';
import { IconImage } from '../../icon-image';
import { CText } from '../../text';
import { TouchableOpacity } from '../../touchable-opacity';
import { styles } from './styles';

interface ButtonInfoProps {
  label?: string;
  subLabel?: string;
  icon?: IconAssets;
  iconRight?: ReactNode;
  onPress?: () => void;
}

export const ButtonInfo = ({
  label,
  subLabel,
  icon,
  iconRight,
  onPress,
}: ButtonInfoProps) => {
  return (
    <TouchableOpacity
      style={styles.container}
      onPress={onPress}
    >
      <IconImage
        size={30}
        source={icon}
      />
      <BlockView
        flex={1}
        style={styles.contentContainer}
      >
        <CText numberOfLines={1}>{label}</CText>
        {!!subLabel && (
          <CText
            numberOfLines={1}
            flex={1}
            style={styles.subLabel}
          >
            {subLabel}
          </CText>
        )}
      </BlockView>
      {iconRight ? (
        iconRight
      ) : (
        <IconImage
          size={20}
          source={IconAssets.icNext}
          color={Colors.neutral300}
        />
      )}
    </TouchableOpacity>
  );
};
