import React, { useMemo } from 'react';

import { IconAssets } from '../../../assets';
import { useI18n } from '../../../hooks';
import { IPaymentMethodInfo } from '../../../types/payment';
import { PaymentService } from '../../../utils';
import { ButtonInfo } from '../ButtonInfo';

interface PaymentBlockProps {
  data?: IPaymentMethodInfo;
  onPress?: () => void;
}

export const PaymentBlock = ({ data, onPress }: PaymentBlockProps) => {
  const { t } = useI18n('common');

  const info = useMemo(() => {
    if (data?.bank) {
      return {
        label: data.bank.text,
        icon: data.bank.icon,
      };
    }

    if (data?.cardInfo) {
      return {
        label: data?.label,
        subLabel: `${data.cardInfo?.type} - ${data.cardInfo?.number}`,
        icon: data?.icon,
      };
    }

    if (data?.walletInfo) {
      return {
        label: data?.label,
        subLabel: PaymentService.showTrueMoneyAccount(
          data?.walletInfo?.phoneNumber,
        ),
        icon: data?.icon,
      };
    }

    if (data) {
      return {
        label: data?.label,
        icon: data?.icon,
      };
    }
    return {
      label: t('CHOOSE_PAYMENT_METHOD_LABEL'),
      icon: IconAssets.paymentEmpty,
    };
  }, [data, t]);

  return (
    <ButtonInfo
      label={info.label}
      subLabel={info.subLabel}
      icon={info.icon}
      onPress={onPress}
    />
  );
};
