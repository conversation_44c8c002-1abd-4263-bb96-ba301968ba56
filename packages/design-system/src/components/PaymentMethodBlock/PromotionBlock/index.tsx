import React from 'react';

import { IconAssets } from '../../../assets';
import { useI18n } from '../../../hooks';
import { Colors } from '../../../tokens';
import { IconImage } from '../../icon-image';
import { TouchableOpacity } from '../../touchable-opacity';
import { ButtonInfo } from '../ButtonInfo';

interface PromotionBlockProps {
  promotionCode?: string;
  onPress?: () => void;
  onDelete?: () => void;
}

export const PromotionBlock = ({
  promotionCode,
  onPress,
  onDelete,
}: PromotionBlockProps) => {
  const { t } = useI18n('common');

  return (
    <ButtonInfo
      label={promotionCode ? promotionCode : t('TAB_PROMOTION')}
      icon={IconAssets.icPromotion}
      iconRight={
        promotionCode && (
          <TouchableOpacity onPress={onDelete}>
            <IconImage
              source={IconAssets.icCloseFill}
              color={Colors.neutral200}
              size={20}
            />
          </TouchableOpacity>
        )
      }
      onPress={onPress}
    />
  );
};
