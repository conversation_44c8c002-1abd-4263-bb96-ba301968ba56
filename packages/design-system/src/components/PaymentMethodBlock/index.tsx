/**
 * Component for displaying and managing payment methods in the booking flow.
 * Allows users to select payment methods and manage promotions.
 */
import React, { useCallback, useMemo } from 'react';
import { uniq } from 'lodash-es';

import { useI18n } from '../../hooks';
import { useSettingsStore } from '../../stores';
import { Colors, FontSizes } from '../../tokens';
import { OmitKeys } from '../../types';
import {
  PaymentService,
  PromotionsService,
  SERVICES,
  TYPE_OF_PAYMENT,
} from '../../utils';
import { BlockView } from '../block-view';
import { SizedBox } from '../sized-box';
import { CText } from '../text';
import { PaymentBlock } from './PaymentBlock';
import { PromotionBlock } from './PromotionBlock';
import styles from './styles';

type PaymentMethodProps = {
  testID?: string;
  type: TYPE_OF_PAYMENT;
  serviceName?: SERVICES;
  entryPoint?: string;
  isHidePromotion?: boolean;
  horizontal?: boolean;
  paymentMethodOptions?: OmitKeys<
    Parameters<typeof PaymentService.choosePaymentMethod>[0],
    'type' | 'serviceName' | 'entryPoint'
  >;
  promotionOptions?: OmitKeys<
    Parameters<typeof PromotionsService.choosePromotion>[0],
    'paymentMethod' | 'serviceId'
  >;
  onChangePaymentMethod?: (
    data: Awaited<ReturnType<typeof PaymentService.choosePaymentMethod>>,
  ) => void;
  onChangePromotion?: (
    data?: Awaited<ReturnType<typeof PromotionsService.choosePromotion>>,
  ) => void;
};

/**
 * Displays the selected payment method and allows changing it
 */
export const PaymentMethodBlock = React.memo(
  ({
    testID,
    type,
    serviceName,
    entryPoint,
    paymentMethodOptions,
    promotionOptions,
    isHidePromotion,
    horizontal,
    onChangePromotion,
    onChangePaymentMethod,
  }: PaymentMethodProps) => {
    const { t } = useI18n('common');
    const { settings } = useSettingsStore();

    const serviceId = useMemo(() => {
      return settings?.services?.find((service) => service.name === serviceName)
        ?._id;
    }, [settings, serviceName]);

    const currentPaymentMethod = useMemo(() => {
      return paymentMethodOptions?.currentPaymentMethod;
    }, [paymentMethodOptions]);

    const currentPromotionCode = useMemo(() => {
      return promotionOptions?.currentPromotion?.code;
    }, [promotionOptions]);

    /**
     * Handles payment method selection
     */
    const choosePaymentMethod = useCallback(async () => {
      const blackListDefault = PaymentService.getBlackListDefault({
        serviceName,
      });
      const paymentMethodInfo = await PaymentService.choosePaymentMethod({
        type,
        serviceName,
        entryPoint,
        promotionData: {
          serviceId,
          promotion: {
            code: currentPromotionCode!,
          },
          costDetail: {
            cost: promotionOptions?.taskCost,
          },
          date: promotionOptions?.taskDate,
          taskPlace: promotionOptions?.taskPlace,
        },
        blackList: uniq(
          paymentMethodOptions?.blackList?.concat(blackListDefault),
        ),
        whiteList: paymentMethodOptions?.whiteList,
        currentPaymentMethod: paymentMethodOptions?.currentPaymentMethod,
      });
      onChangePaymentMethod?.(paymentMethodInfo);
    }, [
      currentPromotionCode,
      entryPoint,
      onChangePaymentMethod,
      paymentMethodOptions?.blackList,
      paymentMethodOptions?.currentPaymentMethod,
      paymentMethodOptions?.whiteList,
      promotionOptions?.taskCost,
      promotionOptions?.taskDate,
      promotionOptions?.taskPlace,
      serviceId,
      serviceName,
      type,
    ]);

    const choosePromotion = useCallback(async () => {
      const promotionInfo = await PromotionsService.choosePromotion({
        serviceId,
        paymentMethod: currentPaymentMethod?.name,
        ...promotionOptions,
      });
      onChangePromotion?.(promotionInfo);
    }, [
      serviceId,
      currentPaymentMethod?.name,
      promotionOptions,
      onChangePromotion,
    ]);

    /**
     * Handles removing a promotion
     */
    const removePromotion = React.useCallback(() => {
      if (onChangePromotion) {
        onChangePromotion(undefined);
      }
    }, [onChangePromotion]);

    return (
      <BlockView testID={testID}>
        <BlockView
          row
          style={styles.panel}
        >
          <CText
            bold
            size={FontSizes.SIZE_16}
          >
            {t('PAYMENT_METHOD')}
          </CText>
        </BlockView>
        <BlockView
          row={!horizontal}
          style={styles.wrapperPayment}
        >
          <PaymentBlock
            data={currentPaymentMethod}
            onPress={choosePaymentMethod}
          />
          {!isHidePromotion && (
            <>
              <SizedBox
                height={horizontal ? 1 : '80%'}
                width={horizontal ? '100%' : 1}
                color={Colors.neutral100}
              />
              <PromotionBlock
                promotionCode={currentPromotionCode}
                onPress={choosePromotion}
                onDelete={removePromotion}
              />
            </>
          )}
        </BlockView>
      </BlockView>
    );
  },
);
