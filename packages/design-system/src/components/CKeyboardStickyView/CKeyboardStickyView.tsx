import React, { memo } from 'react';
import { StyleSheet } from 'react-native';
import { KeyboardStickyView } from 'react-native-keyboard-controller';
import Animated from 'react-native-reanimated';

import { useBottomView } from '../../hooks';
import { Colors, Spacing } from '../../tokens';
import { BlockView } from '../block-view';
import { BlockViewProps } from '../block-view/types';

const AnimatedBlockView = Animated.createAnimatedComponent(BlockView);

export const CKeyboardStickyView: React.FC<BlockViewProps> = memo(
  ({ style, children, ...props }) => {
    const { animatedStyle } = useBottomView();

    return (
      <KeyboardStickyView>
        <AnimatedBlockView
          style={[animatedStyle, StyleSheet.flatten(style)]}
          padding={{
            horizontal: Spacing.SPACE_16,
            top: Spacing.SPACE_16,
          }}
          backgroundColor={Colors.neutralWhite}
          {...props}
        >
          {children}
        </AnimatedBlockView>
      </KeyboardStickyView>
    );
  },
);
