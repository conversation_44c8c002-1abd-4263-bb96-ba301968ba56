import { useCallback, useEffect, useRef, useState } from 'react';

import { ToastHelpers } from '../../helpers';
import { useI18n } from '../../hooks';
import { useAppStore } from '../../stores';
import { LOCALES } from '../../utils';
import { BottomModalHandle } from '../bottom-modal';

const useSelectLanguage = () => {
  const { t, i18n } = useI18n('common');
  const bottomSheetRef = useRef<BottomModalHandle>(null);

  const { onChangeLocale, locale } = useAppStore();
  const [currentLocale, setCurrentLocale] = useState(locale);

  useEffect(() => {
    setCurrentLocale(locale);
  }, [locale]);

  const onChangeCurrentLanguage = useCallback((lc: LOCALES) => {
    setCurrentLocale(lc);
  }, []);

  const onOpenBottomSheet = useCallback(() => {
    bottomSheetRef.current?.present?.();
  }, []);

  const onApply = useCallback(() => {
    if (!currentLocale || currentLocale === locale) {
      return bottomSheetRef.current?.dismiss?.();
    }
    onChangeLocale(currentLocale);
    bottomSheetRef.current?.dismiss?.();
    ToastHelpers.showSuccess({
      message: t('CHANGE_SUCCESS'),
      position: 'top',
    });
  }, [currentLocale, locale, onChangeLocale, t]);

  const onClose = useCallback(() => {
    bottomSheetRef.current?.dismiss?.();
  }, []);

  return {
    t,
    i18n,
    bottomSheetRef,
    currentLocale,
    onChangeCurrentLanguage,
    onOpenBottomSheet,
    onApply,
    onClose,
    locale,
  };
};

export default useSelectLanguage;
