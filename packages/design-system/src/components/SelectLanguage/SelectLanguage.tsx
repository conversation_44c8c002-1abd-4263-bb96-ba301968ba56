import React, { memo } from 'react';

import { BorderRadius, Colors, FontSizes, Spacing } from '../../tokens';
import { LOCALES_OPTIONS } from '../../utils';
import { BlockView } from '../block-view';
import { BottomModal } from '../bottom-modal';
import { Radio } from '../radio';
import { CText } from '../text';
import { TouchableOpacity } from '../touchable-opacity';
import useSelectLanguage from './hook';

interface SelectLanguageProps {
  children: (language: (typeof LOCALES_OPTIONS)[0]) => React.ReactNode;
}

export const SelectLanguage: React.FC<SelectLanguageProps> = memo(
  ({ children }) => {
    const {
      t,
      bottomSheetRef,
      currentLocale,
      onApply,
      onChangeCurrentLanguage,
      onClose,
      onOpenBottomSheet,
      locale,
    } = useSelectLanguage();

    const currentLanguage =
      LOCALES_OPTIONS.find((item) => item.locale === locale) ??
      LOCALES_OPTIONS[0];

    return (
      <BlockView>
        <TouchableOpacity
          testID="LANGUAGE"
          onPress={onOpenBottomSheet}
        >
          {children(currentLanguage)}
        </TouchableOpacity>
        <BottomModal
          modalRef={bottomSheetRef}
          backgroundContainer={Colors.neutralBackground}
          title={t('LANGUAGE')}
          subTitle={t('SELECT_DISPLAY_LANGUAGE')}
          subTitleStyle={{
            color: Colors.neutral500,
            fontSize: FontSizes.SIZE_14,
          }}
          actions={[
            {
              text: t('CANCEL'),
              style: 'cancel',
              onPress: onClose,
            },
            {
              text: t('CONFIRM'),
              onPress: onApply,
            },
          ]}
        >
          <BlockView
            center
            gap={Spacing.SPACE_16}
            padding={Spacing.SPACE_16}
            backgroundColor={Colors.neutralWhite}
            radius={BorderRadius.RADIUS_08}
            margin={Spacing.SPACE_16}
          >
            {LOCALES_OPTIONS.map((item, index) => (
              <TouchableOpacity
                onPress={() => onChangeCurrentLanguage(item.locale)}
                key={item.locale}
                row
                horizontal
                jBetween
                border={{
                  top: {
                    width: index > 0 ? 1 : 0,
                    color: Colors.neutral50,
                  },
                }}
                padding={{ top: index > 0 ? Spacing.SPACE_16 : 0 }}
              >
                <BlockView
                  row
                  horizontal
                  gap={Spacing.SPACE_12}
                  flex
                >
                  <BlockView
                    width={32}
                    height={32}
                    center
                    backgroundColor={Colors.neutral50}
                    radius={BorderRadius.RADIUS_FULL}
                  >
                    <CText
                      size={FontSizes.SIZE_10}
                      bold
                    >
                      {item.iconStr}
                    </CText>
                  </BlockView>
                  <CText
                    flex
                    color={Colors.neutral500}
                  >
                    {t(item.title)}
                  </CText>
                </BlockView>
                <Radio checked={item.locale === currentLocale} />
              </TouchableOpacity>
            ))}
          </BlockView>
        </BottomModal>
      </BlockView>
    );
  },
);
