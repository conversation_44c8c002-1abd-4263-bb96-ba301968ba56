import React, { memo } from 'react';

import { BorderRadius, Colors, FontSizes, Spacing } from '../../tokens';
import { COUNTRIES } from '../../utils';
import { BlockView } from '../block-view';
import { BottomModal } from '../bottom-modal';
import { FastImage } from '../fast-image';
import { Radio } from '../radio';
import { CText } from '../text';
import { TouchableOpacity } from '../touchable-opacity';
import useSelectCountry, { UseSelectCountryProps } from './hook';
import styles from './styles';

interface SelectCountryProps extends UseSelectCountryProps {
  children: (country: (typeof COUNTRIES)[0]) => React.ReactNode;
}

export const SelectCountry = memo(
  ({ children, onChangeCountrySuccess }: SelectCountryProps) => {
    const {
      t,
      isoCodeSelected,
      bottomSheetRef,
      currentIsoCode,
      onApply,
      onChangeCurrentCountry,
      onClose,
      onOpenBottomSheet,
    } = useSelectCountry({ onChangeCountrySuccess });

    return (
      <BlockView>
        <TouchableOpacity onPress={onOpenBottomSheet}>
          {children(isoCodeSelected)}
        </TouchableOpacity>

        <BottomModal
          modalRef={bottomSheetRef}
          backgroundContainer={Colors.neutralBackground}
          title={t('COUNTRY_TERRITORY')}
          subTitle={t('SELECT_COUNTRY_REGION')}
          subTitleStyle={{
            color: Colors.neutral500,
            fontSize: FontSizes.SIZE_14,
          }}
          actions={[
            {
              text: t('CANCEL'),
              style: 'cancel',
              onPress: onClose,
            },
            {
              text: t('CONFIRM'),
              onPress: onApply,
            },
          ]}
        >
          <BlockView
            center
            gap={Spacing.SPACE_16}
            padding={Spacing.SPACE_16}
            backgroundColor={Colors.neutralWhite}
            radius={BorderRadius.RADIUS_08}
            margin={Spacing.SPACE_16}
          >
            {COUNTRIES.map((item, index) => (
              <TouchableOpacity
                onPress={() => onChangeCurrentCountry(item.isoCode)}
                key={item.isoCode}
                row
                horizontal
                jBetween
                border={{
                  top: {
                    width: index > 0 ? 1 : 0,
                    color: Colors.neutral50,
                  },
                }}
                padding={{ top: index > 0 ? Spacing.SPACE_16 : 0 }}
              >
                <BlockView
                  row
                  horizontal
                  gap={Spacing.SPACE_12}
                  flex
                >
                  <FastImage
                    source={item.flag}
                    style={styles.flagIconModal}
                  />
                  <CText
                    flex
                    color={Colors.neutral500}
                  >
                    {t(item.name)}
                  </CText>
                </BlockView>
                <Radio checked={item.isoCode === currentIsoCode} />
              </TouchableOpacity>
            ))}
          </BlockView>
        </BottomModal>
      </BlockView>
    );
  },
);
