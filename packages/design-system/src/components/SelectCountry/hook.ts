import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Keyboard } from 'react-native';
import RNRestart from 'react-native-restart';

import { EndpointKeys, useApiMutation } from '../../api';
import { ToastHelpers } from '../../helpers';
import { useAppLoading, useI18n } from '../../hooks';
import { useAppStore, useAuth } from '../../stores';
import { AppConfig, COUNTRIES, ISO_CODE } from '../../utils';
import { BottomModalHandle } from '../bottom-modal';

export interface UseSelectCountryProps {
  onChangeCountrySuccess?: () => void;
}

const useSelectCountry = ({
  onChangeCountrySuccess,
}: UseSelectCountryProps) => {
  const { t } = useI18n('common');
  const bottomSheetRef = useRef<BottomModalHandle>(null);

  const { showAppLoading, hideAppLoading } = useAppLoading();
  const { isAuthenticated } = useAuth();
  const { isoCode, onChangeIsoCode } = useAppStore();
  const [currentIsoCode, setCurrentIsoCode] = useState(isoCode || ISO_CODE.VN);
  const { mutate: updateUserCountryMutate } = useApiMutation({
    key: EndpointKeys.updateUserCountry,
    options: {
      onMutate: () => {
        showAppLoading();
      },
      onSuccess: () => {
        onAfterUpdateIsoCode();
      },
      onError: () => {
        ToastHelpers.showError({
          message: t('ERROR_TRY_AGAIN'),
          position: 'top',
        });
      },
      onSettled: () => {
        hideAppLoading();
        bottomSheetRef.current?.dismiss?.();
      },
    },
  });

  const isoCodeSelected = useMemo(() => {
    return COUNTRIES.find((item) => item.isoCode === isoCode) ?? COUNTRIES[0];
  }, [isoCode]);

  useEffect(() => {
    if (!isoCode) return;
    setCurrentIsoCode(isoCode);
  }, [isoCode]);

  const onChangeCurrentCountry = useCallback((code: ISO_CODE) => {
    setCurrentIsoCode(code);
  }, []);

  const onOpenBottomSheet = useCallback(() => {
    bottomSheetRef.current?.present?.();
    Keyboard.dismiss();
  }, []);

  const onAfterUpdateIsoCode = useCallback(async () => {
    ToastHelpers.showSuccess({
      message: t('CHANGE_SUCCESS'),
      position: 'top',
    });
    onChangeIsoCode(currentIsoCode);
    onChangeCountrySuccess?.();
    await AppConfig.resetConfig();
    RNRestart.restart();
  }, [currentIsoCode, onChangeCountrySuccess, onChangeIsoCode, t]);

  const onApply = useCallback(() => {
    if (!currentIsoCode || currentIsoCode === isoCode) {
      return bottomSheetRef.current?.dismiss?.();
    }
    if (isAuthenticated) {
      updateUserCountryMutate({});
      return;
    }
    onAfterUpdateIsoCode();
  }, [
    currentIsoCode,
    isoCode,
    isAuthenticated,
    onAfterUpdateIsoCode,
    updateUserCountryMutate,
  ]);

  const onClose = useCallback(() => {
    bottomSheetRef.current?.dismiss?.();
  }, []);

  return {
    t,
    bottomSheetRef,
    COUNTRIES,
    currentIsoCode,
    onChangeCurrentCountry,
    onOpenBottomSheet,
    onApply,
    onClose,
    isoCode,
    isoCodeSelected,
  };
};

export default useSelectCountry;
