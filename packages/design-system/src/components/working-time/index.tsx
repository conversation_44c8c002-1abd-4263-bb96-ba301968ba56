import React from 'react';
import { StyleProp, StyleSheet, TextStyle } from 'react-native';

import {
  DateTimeHelpers,
  IDate,
  ITimezone,
  TypeFormatDate,
} from '../../helpers';
import { useI18n } from '../../hooks';
import { Maybe } from '../../types';
import { BlockView } from '../block-view';
import { ConditionView } from '../condition-view';
import { DateWithGMT } from '../date-with-gmt';
import { DurationWithGMT } from '../duration-with-gmt';
import { CText } from '../text';
import styles from './styles';

export const WorkingTime = ({
  date,
  duration,
  timezone,
  isEnableSchedule,
  schedule,
  titleStyle,
}: {
  date?: Maybe<IDate>;
  duration?: number;
  timezone?: ITimezone;
  isEnableSchedule?: boolean;
  schedule?: number[];
  titleStyle?: StyleProp<TextStyle>;
}) => {
  const { t } = useI18n('common');

  return (
    <BlockView>
      <CText
        testID="timeToWork"
        bold
        style={StyleSheet.flatten([styles.subPanel, titleStyle])}
      >
        {t('TIME_TO_WORK')}
      </CText>

      <BlockView>
        <BlockView style={styles.group}>
          <CText style={styles.txtVLabel}>{t('WOKING_DAY')}</CText>
          <DateWithGMT
            testID={'workingDay'}
            timezone={timezone}
            date={date}
            typeFormat={TypeFormatDate.DateTimeFullWithDay}
            style={styles.txtValue}
          />
        </BlockView>
      </BlockView>

      <BlockView style={styles.group}>
        <CText style={styles.txtVLabel}>{t('WORK_IN')}</CText>
        <DurationWithGMT
          testID={'duration'}
          style={styles.txtValue}
          timezone={timezone}
          date={date}
          duration={duration}
        />
      </BlockView>
      <ConditionView
        condition={isEnableSchedule}
        viewTrue={
          <BlockView style={styles.group}>
            <CText style={styles.txtVLabel}>
              {t('POST_TASK_CHECKBOX_REPEAT')}
            </CText>
            <CText style={styles.txtValue}>
              {schedule
                ?.map((day) =>
                  DateTimeHelpers.formatToString({
                    timezone: timezone,
                    date: DateTimeHelpers.toDayTz({
                      timezone: timezone,
                    }).day(day),
                    typeFormat: TypeFormatDate.DayAbbreviated,
                  }),
                )
                .join(', ')}
            </CText>
          </BlockView>
        }
      />
    </BlockView>
  );
};
