import React from 'react';

import { IconAssets } from '../../assets';
import { Colors, Spacing } from '../../tokens';
import { IAddress, Maybe } from '../../types';
import { BlockView } from '../block-view';
import { Icon } from '../icon';
import { IconImage } from '../icon-image';
import { CText } from '../text';
import { TouchableOpacity } from '../touchable-opacity';

export const TitleChooseService = React.memo(
  ({
    address,
    onPressInfoButton,
  }: {
    address: Maybe<IAddress>;
    onPressInfoButton: () => void;
  }) => {
    return (
      <BlockView
        flex
        row
        horizontal
        jBetween
      >
        <BlockView
          flex
          row
        >
          <IconImage
            source={IconAssets.icLocation}
            size={24}
            color={Colors.red500}
          />
          <BlockView
            flex
            margin={{ left: Spacing.SPACE_08 }}
          >
            <CText>{address?.shortAddress}</CText>
            <CText
              bold
              numberOfLines={1}
              margin={{ right: Spacing.SPACE_16 }}
            >
              {address?.address}
            </CText>
          </BlockView>
        </BlockView>
        <TouchableOpacity onPress={onPressInfoButton}>
          <Icon
            name={'icMoreInformation'}
            size={24}
          />
        </TouchableOpacity>
      </BlockView>
    );
  },
);
