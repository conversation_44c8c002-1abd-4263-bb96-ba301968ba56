import { EndpointConfig, EndpointKeys } from './type';

export const EndpointsID: Record<EndpointKeys, EndpointConfig> = {
  [EndpointKeys.getEnv]: {
    path: 'v5/env-indo/get-asker-env',
    isDualAuth: true,
  },
  [EndpointKeys.getAllSettingsWithoutLogin]: {
    path: 'v5/api-asker-indo/get-all-settings-without-login',
  },
  [EndpointKeys.getAllSettings]: {
    path: 'v5/api-asker-indo/get-all-settings',
  },
  [EndpointKeys.getUser]: {
    path: 'v5/api-asker-indo/get-user',
  },
  [EndpointKeys.getUpComingTasks]: {
    path: 'v5/api-asker-indo/get-up-coming-tasks',
  },
  [EndpointKeys.getScheduleTasks]: {
    path: 'v5/api-asker-indo/get-schedule-tasks',
  },
  [EndpointKeys.getMonthlyTasks]: {
    path: 'v5/api-asker-indo/get-subscription-by-userId',
  },
  [EndpointKeys.checkTaskSameTime]: {
    path: 'v5/api-asker-indo/check-task-sametime',
  },
  [EndpointKeys.postTaskAirConditioner]: {
    path: 'v5/booking-indo/air-conditioner',
  },
  [EndpointKeys.postTaskChildCare]: {
    path: 'v5/booking-indo/child-care',
  },
  [EndpointKeys.postTaskElderlyCare]: {
    path: 'v5/booking-indo/elderly-care',
  },
  [EndpointKeys.postTaskPatientCare]: {
    path: 'v5/booking-indo/patient-care',
  },
  [EndpointKeys.postTaskOfficeCleaning]: {
    path: 'v5/booking-indo/office-cleaning',
  },
  [EndpointKeys.checkTaskerConflictTime]: {
    path: 'v5/api-asker-indo/check-tasker-conflict-time',
  },
  [EndpointKeys.postTaskSubscriptionCleaning]: {
    path: 'v5/booking-indo/subscription',
  },
  [EndpointKeys.postTaskOfficeCleaningSubscription]: {
    path: 'v5/booking-indo/subscription-office-cleaning',
  },
  // Card
  [EndpointKeys.removeCard]: {
    path: 'v5/payment-indo/disable-card',
  },
  [EndpointKeys.setCardDefault]: {
    path: 'v5/api-asker-indo/set-payment-card-default',
  },
  [EndpointKeys.getFinancialAccount]: {
    path: 'v5/api-asker-indo/get-financial-account',
  },
  [EndpointKeys.bookTaskForceTasker]: {
    path: 'v5/booking-indo/book-task-force-tasker',
  },
  [EndpointKeys.postTaskCleaning]: {
    path: 'v5/booking-indo/home-cleaning',
  },
  [EndpointKeys.getOutstandingPayment]: {
    path: 'v5/api-asker-indo/get-outstanding-payment',
  },
  [EndpointKeys.getTaskDetail]: {
    path: 'v5/api-asker-indo/get-task-detail',
  },
  [EndpointKeys.cancelTask]: {
    path: 'v5/cancel-task-indo/cancel',
  },
  [EndpointKeys.cancelPrepayTask]: {
    path: 'v5/cancel-task-indo/cancel-prepay-task',
  },
  [EndpointKeys.checkCancelFee]: {
    path: 'v5/cancel-task-indo/get-fee-asker-cancel-task',
  },
  [EndpointKeys.addFavoriteService]: {
    path: 'v5/api-asker-indo/add-favourite-services',
  },
  [EndpointKeys.getFavoriteTasker]: {
    path: 'v5/api-asker-indo/get-favorite-tasker-v2',
  },
  [EndpointKeys.getListTaskerWorked]: {
    path: 'v5/api-asker-indo/get-list-tasker-worked',
  },
  [EndpointKeys.addFavoriteTasker]: {
    path: 'v5/api-asker-indo/add-favorite-tasker',
  },
  [EndpointKeys.removeFavoriteTasker]: {
    path: 'v5/api-asker-indo/remove-favorite-tasker',
  },
  // Blacklist Tasker APIs
  [EndpointKeys.getBlackListTasker]: {
    path: 'v5/api-asker-indo/get-blacklist-taskers',
  },
  [EndpointKeys.getSuggestBlackList]: {
    path: 'v5/api-asker-indo/get-suggest-blacklist-taskers',
  },
  [EndpointKeys.addBlackListTasker]: {
    path: 'v5/api-asker-indo/add-blackList-tasker',
  },
  [EndpointKeys.removeBlackListTasker]: {
    path: 'v5/api-asker-indo/remove-tasker-from-blackList',
  },
  [EndpointKeys.postTaskWaterHeater]: {
    path: 'v5/booking-indo/water-heater',
  },
  [EndpointKeys.postTaskMassage]: {
    path: 'v5/booking-indo/massage',
  },
  // Housekeeping
  [EndpointKeys.createHousekeepingLocation]: {
    path: 'v5/api-asker-indo/add-housekeeping-location',
  },
  [EndpointKeys.updateRoomHousekeepingLocation]: {
    path: 'v5/api-asker-indo/update-housekeeping-location',
  },
  [EndpointKeys.deleteRoomHousekeepingLocation]: {
    path: 'v5/api-asker-indo/delete-housekeeping-location',
  },
  [EndpointKeys.postTaskHousekeeping]: {
    path: 'v5/booking-indo/housekeeping-v2',
  },
  [EndpointKeys.postTaskDeepCleaning]: {
    path: 'v5/booking-indo/deep-cleaning',
  },

  // Rating APIs
  [EndpointKeys.customerRating]: {
    path: 'v5/rating-indo/customer-rating',
  },
  [EndpointKeys.closeRatingTask]: {
    path: 'v5/api-asker-indo/close-rating-task',
  },
  [EndpointKeys.updateRatingTask]: {
    path: 'v5/rating-indo/update',
  },
  [EndpointKeys.setReviewStore]: {
    path: 'v5/api-asker-indo/set-review-store',
  },
  [EndpointKeys.getTaskDone]: {
    path: 'v5/api-asker-indo/get-tasks-done',
  },

  // Update task
  [EndpointKeys.changePremiumOption]: {
    path: 'v5/update-task-indo/update-task-premium',
  },
  [EndpointKeys.updateTaskNote]: {
    path: 'v5/update-task-indo/update-task-note',
  },
  [EndpointKeys.getHistoryTasks]: {
    path: 'v5/api-asker-indo/get-list-history-tasks',
  },
  [EndpointKeys.getExtraMoneyUpdateDateTime]: {
    path: 'v5/update-task-indo/get-extra-money-update-date-time',
  },
  [EndpointKeys.createUpdateDateTimeRequest]: {
    path: 'v5/update-task-indo/create-update-date-time-request',
  },
  [EndpointKeys.askerCreateRequestUpdateDetailV4]: {
    path: 'v5/update-task-indo/asker-create-request',
  },
  [EndpointKeys.updateTaskHomeCleaning]: {
    path: 'v5/update-task-indo/home-cleaning',
  },
  [EndpointKeys.updateTaskAirConditioner]: {
    path: 'v5/update-task-indo/air-conditioner',
  },
  [EndpointKeys.updateTaskDeepCleaning]: {
    path: 'v5/update-task-indo/deep-cleaning',
  },
  [EndpointKeys.updateTaskChildCare]: {
    path: 'v5/update-task-indo/child-care',
  },
  [EndpointKeys.updateTaskHomeCooking]: {
    path: 'v5/update-task-indo/home-cooking',
  },
  [EndpointKeys.updateTaskDisinfection]: {
    path: 'v5/update-task-indo/disinfection',
  },
  [EndpointKeys.updateTaskPatientCare]: {
    path: 'v5/update-task-indo/patient-care',
  },
  [EndpointKeys.updateTaskElderlyCare]: {
    path: 'v5/update-task-indo/elderly-care',
  },
  [EndpointKeys.updateTaskSofa]: {
    path: 'v5/update-task-indo/sofa',
  },
  [EndpointKeys.updateTaskOfficeCleaning]: {
    path: 'v5/update-task-indo/office-cleaning',
  },
  [EndpointKeys.updateTaskWashingMachine]: {
    path: 'v5/update-task-indo/washing-machine',
  },
  [EndpointKeys.updateTaskWaterHeater]: {
    path: 'v5/update-task-indo/water-heater',
  },
  [EndpointKeys.updateTaskOfficeCarpetCleaning]: {
    path: 'v5/update-task-indo/office-carpet-cleaning',
  },
  [EndpointKeys.updateTaskMassage]: {
    path: 'v5/update-task-indo/massage',
  },
  [EndpointKeys.updateTaskIndustrialCleaning]: {
    path: 'v5/update-task-indo/industrial-cleaning',
  },
  [EndpointKeys.updateTaskHomeMoving]: {
    path: 'v5/update-task-indo/home-moving',
  },
  [EndpointKeys.updateHouseKeeping]: {
    path: 'v5/update-task-indo/housekeeping',
  },
  [EndpointKeys.updateTaskBeautyCare]: {
    path: 'v5/update-task-indo/beauty-care',
  },
  [EndpointKeys.updateTaskIroning]: {
    path: 'v5/update-task-indo/ironing',
  },
  [EndpointKeys.updateTaskHairStyling]: {
    path: 'v5/update-task-indo/hair-styling',
  },
  [EndpointKeys.updateTaskMakeup]: {
    path: 'v5/update-task-indo/makeup',
  },
  [EndpointKeys.updateTaskNail]: {
    path: 'v5/update-task-indo/nail',
  },
  [EndpointKeys.updateGroceryAssistant]: {
    path: 'v5/update-task-indo/grocery-assistant',
  },
  [EndpointKeys.checkTaskerConflictUpdateTime]: {
    path: 'v5/update-task-indo/check-tasker-conflict-time',
  },
  [EndpointKeys.postTaskWashingMachine]: {
    path: 'v5/booking-indo/washing-machine',
  },
  [EndpointKeys.postTaskDisinfection]: {
    path: 'v5/booking-indo/disinfection',
  },
  [EndpointKeys.postTaskHomeCooking]: {
    path: 'v5/booking-indo/home-cooking',
  },
  [EndpointKeys.postTaskOfficeCarpetCleaning]: {
    path: 'v5/booking-indo/carpet-cleaning',
  },
  [EndpointKeys.postTaskIndustrialCleaning]: {
    path: 'v5/booking-indo/industrial-cleaning',
  },
  [EndpointKeys.postTaskSofaCleaning]: {
    path: 'v5/booking-indo/sofa',
  },
  [EndpointKeys.postTaskIroning]: {
    path: 'v5/booking-indo/ironing',
  },
  [EndpointKeys.postTaskLaundry]: {
    path: 'v5/booking-indo/laundry',
  },
  [EndpointKeys.postTaskHomeMoving]: {
    path: 'v5/booking-indo/home-moving',
  },
  [EndpointKeys.getDetailSubscriptionSchedule]: {
    path: 'v5/api-asker-indo/get-detail-subscription-schedule',
  },
  [EndpointKeys.updateSubscription]: {
    path: 'v5/update-task-indo/new-subscription',
  },
  [EndpointKeys.updateExpireSubscription]: {
    path: 'v5/update-task-indo/update-expired-subscription',
  },
  [EndpointKeys.getScheduleDetail]: {
    path: 'v5/api-asker-indo/get-task-schedule-detail',
  },
  [EndpointKeys.activeTaskScheduleByTaskId]: {
    path: 'v5/api-asker-indo/active-task-schedule-by-task-id',
  },
  [EndpointKeys.updateTaskScheduleTime]: {
    path: 'v5/api-asker-indo/update-schedule-time',
  },
  [EndpointKeys.cancelTaskSchedule]: {
    path: 'v5/api-asker-indo/remove-task-schedule',
  },
  [EndpointKeys.updateTaskSchedule]: {
    path: 'v5/api-asker-indo/active-task-schedule',
  },
  [EndpointKeys.cancelTaskPaymentProcess]: {
    path: 'v5/api-asker-indo/cancel-task-payment-process',
  },
  [EndpointKeys.updatePaymentMethod]: {
    path: 'v5/update-task-indo/update-payment-method',
  },

  /* -------------------------------- COMMUNITY -------------------------------- */
  [EndpointKeys.searchPost]: {
    path: 'v5/community-indo/search-post',
  },
  [EndpointKeys.searchUser]: {
    path: 'v5/community-indo/search-user',
  },
  [EndpointKeys.insertUserCommunity]: {
    path: 'v5/community-indo/insert-user',
  },
  [EndpointKeys.getUserCommunity]: {
    path: 'v5/community-indo/get-user',
  },
  [EndpointKeys.createNewsPost]: {
    path: 'v5/community-indo/create-post',
  },
  [EndpointKeys.getUserProfileCommunity]: {
    path: 'v5/community-indo/get-profile',
  },
  [EndpointKeys.getUserPosts]: {
    path: 'v5/community-indo/get-user-posts',
  },
  [EndpointKeys.getUserSharedPosts]: {
    path: 'v5/community-indo/get-user-shared-posts',
  },
  [EndpointKeys.editPost]: {
    path: 'v5/community-indo/edit-post',
  },
  [EndpointKeys.deletePost]: {
    path: 'v5/community-indo/delete-post',
  },
  [EndpointKeys.getNotificationsCommunity]: {
    path: 'v5/community-indo/get-notifications',
  },
  [EndpointKeys.setIsReadNotificationCommunity]: {
    path: 'v5/community-indo/set-notification-as-read',
  },
  [EndpointKeys.reportUser]: {
    path: 'v5/community-indo/insert-user-report',
  },
  [EndpointKeys.commentPost]: {
    path: 'v5/community-indo/comment-post',
  },
  [EndpointKeys.replyComment]: {
    path: 'v5/community-indo/reply-comment',
  },
  [EndpointKeys.likeAndUnLikeComment]: {
    path: 'v5/community-indo/like-and-unlike-comment',
  },
  [EndpointKeys.likeAndUnLikePost]: {
    path: 'v5/community-indo/like-and-unlike-post',
  },
  [EndpointKeys.getComments]: {
    path: 'v5/community-indo/get-comments',
  },
  [EndpointKeys.updateUserProfileCommunity]: {
    path: 'v5/community-indo/update-user-profile',
  },
  [EndpointKeys.updateAvatar]: {
    path: 'v5/api-asker-indo/update-user-avatar',
  },
  [EndpointKeys.sendVerifyEmail]: {
    path: 'v5/user-asker-indo/send-verify-email',
  },
  [EndpointKeys.updateUserProfile]: {
    path: 'v5/api-asker-indo/update-user-info',
  },
  [EndpointKeys.getOwnedMedals]: {
    path: 'v5/community-indo/get-owned-medals',
  },
  [EndpointKeys.sharePost]: {
    path: 'v5/community-indo/share-post',
  },
  [EndpointKeys.followUser]: {
    path: 'v5/community-indo/follow-user',
  },
  [EndpointKeys.unFollowUser]: {
    path: 'v5/community-indo/unfollow-user',
  },
  [EndpointKeys.getListFollowing]: {
    path: 'v5/community-indo/get-list-following',
  },
  [EndpointKeys.getListFollowers]: {
    path: 'v5/community-indo/get-list-followers',
  },
  [EndpointKeys.hideAndUnHidePost]: {
    path: 'v5/community-indo/hide-and-unhide-post',
  },
  [EndpointKeys.blockAndUnBlockUser]: {
    path: 'v5/community-indo/block-and-unblock-user',
  },
  [EndpointKeys.getNewsFeedDetail]: {
    path: 'v5/community-indo/get-post-detail',
  },
  /* ------------------------------ END COMMUNITY ----------------------------- */

  /* -------------------------------------------------------------------------- */
  /* -------------------------------- CHAT --------------------------- */
  /* -------------------------------------------------------------------------- */
  [EndpointKeys.getConversations]: {
    path: 'v5/api-asker-indo/get-list-chat-v2',
  },
  [EndpointKeys.getNotifications]: {
    path: 'v5/api-asker-indo/get-notification-not-from-btaskee',
  },
  [EndpointKeys.getListChatMessage]: {
    path: 'v5/api-asker-indo/get-chat-list-chat-message-v2',
  },
  [EndpointKeys.setIsReadChatMessage]: {
    path: 'v5/api-asker-indo/set-is-read-chat-message-v2',
  },
  [EndpointKeys.setIsReadNotification]: {
    path: 'v5/api-asker-indo/update-status-notification',
  },
  [EndpointKeys.archiveConversation]: {
    path: 'v5/api-asker-indo/archive-chat-message',
  },
  [EndpointKeys.removeConversation]: {
    path: 'v5/api-asker-indo/delete-chat-message',
  },
  [EndpointKeys.sendChatMessage]: {
    path: 'v5/api-asker-indo/send-chat-message-v2',
  },
  [EndpointKeys.getTaskerServices]: {
    path: 'v5/api-asker-indo/get-tasker-services',
  },
  [EndpointKeys.getTaskerFreeTime]: {
    path: 'v5/api-asker-indo/get-tasker-free-time',
  },
  [EndpointKeys.getPricingTaskDateOptions]: {
    path: 'v5/pricing-indo/task-date-options',
  },
  [EndpointKeys.getMoreMessage]: {
    path: 'v5/api-asker-indo/load-more-messages',
  },
  [EndpointKeys.rejectRequestUpdateDetailV4]: {
    path: 'v5/update-task-indo/asker-reject-request',
  },
  [EndpointKeys.approveRequestUpdateDetailV4]: {
    path: 'v5/update-task-indo/asker-approve-request',
  },
  [EndpointKeys.getIncreaseDurationExtraMoney]: {
    path: 'v5/update-task-indo/get-increase-duration-extra-money',
  },
  [EndpointKeys.getUpdateDetailExtraMoneyV4]: {
    path: 'v5/update-task-indo/get-extra-money',
  },
  [EndpointKeys.removeAllNotification]: {
    path: 'v5/api-asker-indo/remove-notification-user',
  },
  [EndpointKeys.removeNotificationNotFromBtaskeeById]: {
    path: 'v5/api-asker-indo/remove-notification-not-from-btaskee-by-id',
  },
  [EndpointKeys.translateMessage]: {
    path: 'v5/api-asker-indo/translate-message',
  },
  [EndpointKeys.getListChatHistoryMassage]: {
    path: 'v5/api-asker-indo/get-chat-history-by-task',
  },
  // Marketing
  [EndpointKeys.getMarketingCampaign]: {
    path: 'v5/api-asker-indo/get-marketing-campaign',
  },
  [EndpointKeys.addActivatedServices]: {
    path: 'v5/api-asker-indo/add-activated-services',
  },
  [EndpointKeys.deleteAccount]: {
    path: 'v5/user-asker-indo/delete',
  },

  // Combo Voucher APIs
  [EndpointKeys.getListUserComboVoucher]: {
    path: 'v5/api-asker-indo/get-list-user-combo-voucher',
  },
  [EndpointKeys.getListComboVoucherTransactionHistories]: {
    path: 'v5/api-asker-indo/get-list-combo-voucher-transaction-histories',
  },
  [EndpointKeys.getUserComboVoucherDetail]: {
    path: 'v5/api-asker-indo/get-user-combo-voucher-detail',
  },
  [EndpointKeys.cancelComboVoucherSubscription]: {
    path: 'v5/api-asker-indo/cancel-combo-voucher-subscription',
  },
  [EndpointKeys.getFreeComboVoucher]: {
    path: 'v5/api-asker-indo/get-free-combo-voucher',
  },
  [EndpointKeys.payComboVoucher]: {
    path: 'v5/api-asker-indo/pay-combo-voucher',
  },
  [EndpointKeys.getMyRewards]: {
    path: 'v5/api-asker-indo/get-my-rewards-v2',
  },
  [EndpointKeys.getGiftDetail]: {
    path: 'v5/api-asker-indo/get-gift-detail',
  },
  [EndpointKeys.redeemGift]: {
    path: 'v5/api-asker-indo/redeem-gift',
  },
  [EndpointKeys.getReferralFriends]: {
    path: 'v5/api-asker-indo/get-referral-friends',
  },
  [EndpointKeys.createLocationAsker]: {
    path: 'v5/api-asker-indo/add-location-asker',
  },
  [EndpointKeys.createHomeMovingLocation]: {
    path: 'v5/api-asker-indo/add-home-moving-location',
  },
  [EndpointKeys.updateLocationAsker]: {
    path: 'v5/api-asker-indo/update-location-asker',
  },
  [EndpointKeys.updateHomeMovingLocation]: {
    path: 'v5/api-asker-indo/update-home-moving-location',
  },
  [EndpointKeys.deleteLocationAsker]: {
    path: 'v5/api-asker-indo/delete-location-asker',
  },
  [EndpointKeys.deleteHomeMovingLocation]: {
    path: 'v5/api-asker-indo/delete-home-moving-location',
  },
  [EndpointKeys.listBusinessMemberTransactions]: {
    path: 'v5/api-asker-indo/list-business-member-transactions',
  },
  [EndpointKeys.getListPayment]: {
    path: 'v5/api-asker-indo/get-list-payment',
  },
  [EndpointKeys.getFinancialTransaction]: {
    path: 'v5/api-asker-indo/get-financial-transaction',
  },
  [EndpointKeys.getDetailFinancialTransaction]: {
    path: 'v5/api-asker-indo/get-detail-financial-transaction',
  },
  [EndpointKeys.prepaymentTask]: {
    path: 'v5/payment-indo/prepay-task',
  },

  /* -------------------------------- COMPLAINT -------------------------------- */
  [EndpointKeys.createUserComplaint]: {
    path: 'v5/api-asker-indo/create-user-complaint',
  },
  /* ------------------------------ END COMPLAINT ----------------------------- */

  [EndpointKeys.createReportTransaction]: {
    path: 'v5/api-asker-indo/create-report-transaction',
  },
  [EndpointKeys.setPaymentCardDefault]: {
    path: 'v5/api-asker-indo/set-payment-card-default',
  },
  [EndpointKeys.disableCard]: {
    path: 'v5/payment-indo/disable-card',
  },
  [EndpointKeys.topUpCredit]: {
    path: 'v5/payment-indo/top-up-credit',
  },
  [EndpointKeys.getSurveyDetail]: {
    path: 'v5/api-asker-indo/get-survey-by-id',
  },
  [EndpointKeys.askerSendSurvey]: {
    path: 'v5/api-asker-indo/asker-send-survey',
  },
  [EndpointKeys.addCard]: {
    path: 'v5/payment-indo/integrate-card',
  },
  [EndpointKeys.getChallengeShopper]: {
    // TODO: Only use in VietNam integrate card, add for not show error
    path: 'v5/payment/adyen-get-challenge-shopper',
  },
  [EndpointKeys.getLastPostedTask]: {
    path: 'v5/api-asker-vn/get-last-post-task',
  },
  [EndpointKeys.getPromotionByService]: {
    path: 'v5/api-asker-indo/get-gift-v2',
  },
  [EndpointKeys.getRewardsByTask]: {
    path: 'v5/api-asker-indo/get-rewards-for-book-task',
  },
  [EndpointKeys.liXiTasker]: {
    path: 'v5/api-asker-indo/lixi-tasker',
  },
  [EndpointKeys.updateUserCountry]: {
    path: 'v5/user-asker-indo/update-user-country',
  },
  [EndpointKeys.getMemberInfo]: {
    path: 'v5/api-asker-indo/get-member-info',
  },
  [EndpointKeys.getRewardsForYou]: {
    path: 'v5/api-asker-indo/get-rewards-for-you',
  },
  [EndpointKeys.getListPointTransactionReceived]: {
    path: 'v5/api-asker-indo/get-list-point-transaction-received',
  },
  [EndpointKeys.getListPointTransactionUsed]: {
    path: 'v5/api-asker-indo/get-list-point-transaction-used',
  },
  [EndpointKeys.createFeedback]: {
    path: 'v5/api-asker-indo/create-feedback',
  },
  [EndpointKeys.postTaskMakeup]: {
    path: 'v5/booking-indo/makeup',
  },
  [EndpointKeys.postTaskHairStyling]: {
    path: 'v5/booking-indo/hair-styling',
  },
  [EndpointKeys.rechargePayment]: {
    path: 'v5/payment-indo/recharge-payment',
  },
  [EndpointKeys.validateFriendCode]: {
    path: 'v5/user-asker-indo/validate-friendCode',
  },
  [EndpointKeys.validatePhone]: {
    path: 'v5/user-asker-indo/validate-phone',
  },
  [EndpointKeys.validateEmail]: {
    path: 'v5/user-asker-indo/validate-email',
  },
  /* -------------------------------------------------------------------------- */
  /* -------------------------------- DUAL AUTH ------------------------------- */
  /* -------------- Public APIs accessible with or without login -------------- */
  /* -------------------------------------------------------------------------- */
  [EndpointKeys.pricingHomeCleaning]: {
    path: 'v5/pricing-indo/home-cleaning',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceAirConditioner]: {
    path: 'v5/pricing-indo/air-conditioner-v2',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceChildCare]: {
    path: 'v5/pricing-indo/child-care',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceElderlyCare]: {
    path: 'v5/pricing-indo/elderly-care',
    isDualAuth: true,
  },
  [EndpointKeys.getPricePatientCare]: {
    path: 'v5/pricing-indo/patient-care',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceOfficeCleaning]: {
    path: 'v5/pricing-indo/office-cleaning',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceCleaningSubscription]: {
    path: 'v5/pricing-indo/subscription',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceCleaning]: {
    path: 'v5/pricing-indo/home-cleaning',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceChildCareSubscription]: {
    path: 'v5/pricing-indo/subscription-child-care',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceOfficeCleaningSubscription]: {
    path: 'v5/pricing-indo/subscription-office-cleaning',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceElderlyCareSubscription]: {
    path: 'v5/pricing-indo/subscription-elderly-care',
    isDualAuth: true,
  },
  [EndpointKeys.getPricePatientCareSubscription]: {
    path: 'v5/pricing-indo/subscription-patient-care',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceWaterHeater]: {
    path: 'v5/pricing-indo/water-heater',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceWashingMachine]: {
    path: 'v5/pricing-indo/washing-machine',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceDisinfection]: {
    path: 'v5/pricing-indo/disinfection',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceHomeCooking]: {
    path: 'v5/pricing-indo/home-cooking',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceOfficeCarpetCleaning]: {
    path: 'v5/pricing-indo/carpet-cleaning',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceIndustrialCleaning]: {
    path: 'v5/pricing-indo/industrial-cleaning',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceSofaCleaning]: {
    path: 'v5/pricing-indo/sofa',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceMassage]: {
    path: 'v5/pricing-indo/massage',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceIroning]: {
    path: 'v5/pricing-indo/ironing',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceLaundry]: {
    path: 'v5/pricing-indo/laundry',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceHomeMoving]: {
    path: 'v5/pricing-indo/home-moving',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceHousekeeping]: {
    path: 'v5/pricing-indo/housekeeping-v2',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceDeepCleaning]: {
    path: 'v5/pricing-indo/deep-cleaning',
    isDualAuth: true,
  },
  // Community
  [EndpointKeys.getTermConditions]: {
    path: 'v5/community-indo/get-term-conditions',
    isDualAuth: true,
  },
  [EndpointKeys.getAllTags]: {
    path: 'v5/community-indo/get-all-tags',
    isDualAuth: true,
  },
  [EndpointKeys.getListNewsFeeds]: {
    path: 'v5/community-indo/get-posts',
    isDualAuth: true,
  },
  [EndpointKeys.getRewardHomePage]: {
    path: 'v5/api-asker-indo/get-reward-home-page',
    isDualAuth: true,
  },
  [EndpointKeys.getMarketingCampaignWithoutLogin]: {
    path: 'v5/api-asker-indo/get-marketing-campaign-without-login',
    isDualAuth: true,
  },
  [EndpointKeys.getServices]: {
    path: 'v5/api-asker-indo/get-services-v2',
    isDualAuth: true,
  },
  [EndpointKeys.getMarketingCampaignDetail]: {
    path: 'v5/api-asker-indo/get-marketing-campaign-detail',
    isDualAuth: true,
  },
  [EndpointKeys.getRewardDetail]: {
    path: 'v5/api-asker-indo/get-reward-detail',
    isDualAuth: true,
  },
  [EndpointKeys.getListReward]: {
    path: 'v5/api-asker-indo/get-list-reward',
    isDualAuth: true,
  },
  [EndpointKeys.getBeautyAccessories]: {
    path: 'v5/api-asker-indo/get-beauty-accessories',
    isDualAuth: true,
  },
  [EndpointKeys.getReferralSetting]: {
    path: 'v5/api-asker-indo/get-referral-setting',
    isDualAuth: true,
  },
  [EndpointKeys.checkPromotion]: {
    path: 'v5/promotion-indo/check-post-task',
    isDualAuth: true,
  },
  // Savings Package APIs
  [EndpointKeys.getListComboVoucher]: {
    path: 'v5/api-asker-indo/get-list-combo-voucher-v2',
    isDualAuth: true,
  },
  [EndpointKeys.getComboVoucherDetail]: {
    path: 'v5/api-asker-indo/get-combo-voucher-detail',
    isDualAuth: true,
  },
  [EndpointKeys.getSpecialIncentive]: {
    path: 'v5/api-asker-indo/get-special-incentive',
    isDualAuth: true,
  },
  [EndpointKeys.trackTryToProcessPayment]: {
    path: 'v5/api-asker-indo/track-asker-try-to-process-payment',
  },
  [EndpointKeys.getSupportCity]: {
    path: 'v5/api-asker-indo/get-support-city',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceMakeup]: {
    path: 'v5/pricing-indo/makeup',
    isDualAuth: true,
  },
  [EndpointKeys.getPriceHairStyling]: {
    path: 'v5/pricing-indo/hair-styling',
    isDualAuth: true,
  },
  [EndpointKeys.checkCompareVersionApp]: {
    path: 'v5/api-asker-indo/check-compare-version-app',
    isDualAuth: true,
  },
  [EndpointKeys.initRaixPushToken]: {
    path: 'v5/api-asker-indo/init-raix-push-token',
    isDualAuth: true,
  },

  /* ----------------------------- BUSINESS ACCOUNT ---------------------------- */
  [EndpointKeys.verifyMember]: {
    path: 'v5/api-asker-indo/verify-members',
  },
  [EndpointKeys.addMemberToBusiness]: {
    path: 'v5/api-asker-indo/add-members-to-business',
  },
  [EndpointKeys.getListMember]: {
    path: 'v5/api-asker-indo/list-members-by-level',
  },
  [EndpointKeys.updateGroupName]: {
    path: 'v5/api-asker-indo/update-member-level',
  },
  [EndpointKeys.removeMemberFromBusiness]: {
    path: 'v5/api-asker-indo/remove-member-from-business',
  },
  [EndpointKeys.removeMemberFromGroup]: {
    path: 'v5/api-asker-indo/remove-members-level',
  },
  [EndpointKeys.addMemberToLevel]: {
    path: 'v5/api-asker-indo/add-members-level',
  },
  [EndpointKeys.createBusinessLevel]: {
    path: 'v5/api-asker-indo/create-business-level',
  },
  [EndpointKeys.updateBusinessLevel]: {
    path: 'v5/api-asker-indo/update-business-level',
  },
  [EndpointKeys.removeBusinessLevel]: {
    path: 'v5/api-asker-indo/remove-business-level',
  },
  [EndpointKeys.createBusiness]: {
    path: 'v5/api-asker-indo/create-business',
  },
  [EndpointKeys.listBusinessTransactions]: {
    path: 'v5/api-asker-indo/list-business-transactions',
  },
  [EndpointKeys.getTotalTopUp]: {
    path: 'v5/api-asker-indo/get-total-topup-bpay',
  },
  [EndpointKeys.topUpMemberLevel]: {
    path: 'v5/api-asker-indo/topup-members-level',
  },
  [EndpointKeys.getTotalRevoke]: {
    path: 'v5/api-asker-indo/get-total-revoke-bpay',
  },
  [EndpointKeys.revokeMemberLevel]: {
    path: 'v5/api-asker-indo/revoke-members-level',
  },
  [EndpointKeys.sendBusinessReport]: {
    path: 'v5/api-asker-indo/send-business-report',
  },
  [EndpointKeys.getBusinessTopUpSettingInfo]: {
    path: 'v5/api-asker-indo/get-business-topup-setting-info',
  },
  /* --------------------------- END BUSINESS ACCOUNT -------------------------- */
  [EndpointKeys.updatePrivacyPolicy]: {
    path: 'v5/user-asker-indo/update-privacy-policy',
  },
};
