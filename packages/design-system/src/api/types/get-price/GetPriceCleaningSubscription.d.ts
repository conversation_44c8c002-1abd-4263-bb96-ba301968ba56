import { IAddons, IPriceSub, ITaskPlace } from '../../../types';
import { IPaymentParams, IPromotionParams } from '../Base';

export type IParamsGetPriceCleaningSubscription = {
  schedule: string[];
  timezone: string;
  month: number;
  task: {
    taskPlace: ITaskPlace;
    homeType?: string;
    duration: number;
    payment?: IPaymentParams;
    addons?: IAddons[];
    isPremium?: boolean;
    isEco?: boolean;
    promotion?: IPromotionParams;
  };
  service: {
    _id: string;
  };
  isoCode: string;
};

export class IGetPriceCleaningSubscriptionAPI {
  params?: IParamsGetPriceCleaningSubscription;
  response?: IPriceSub;
  error?: any;
}
