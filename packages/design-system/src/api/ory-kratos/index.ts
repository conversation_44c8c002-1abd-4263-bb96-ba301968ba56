import { enc, SHA256 } from 'crypto-js';

import { useAppStore, useAuth } from '../../stores';
import { HttpClient } from './axios';
import {
  IGetFlowResponse,
  IgetWhoAmIResponse,
  ILoginSocialBody,
  ILoginSocialResponse,
  IRegisterParams,
  IRegisterResponse,
  IRole,
  ISendRecoveryCodeParams,
  ISubmitLoginFlowBody,
  ISubmitLoginFlowResponse,
  IUpdateProfileParams,
  IVerificationParams,
  IVerificationResponse,
  IVerifyRecoveryCodeResponse,
} from './types';

export class OryKratosApi {
  private static instance = HttpClient.getInstance();

  // Login with password
  static getLoginFlow = () => {
    return this.instance.get<IGetFlowResponse>('/self-service/login/api');
  };

  static submitLoginFlow = ({ flowId, ...body }: ISubmitLoginFlowBody) => {
    return this.instance.post<ISubmitLoginFlowResponse>(
      `/self-service/login?flow=${flowId}`,
      {
        ...body,
        password: SHA256(body.password).toString(enc.Hex),
        method: 'password',
        role: IRole.ASKER,
      },
    );
  };

  // Social login
  static loginSocial = ({ flowId, ...body }: ILoginSocialBody) => {
    return this.instance.post<ILoginSocialResponse>(
      `/self-service/login?flow=${flowId}`,
      {
        ...body,
        method: 'oidc',
      },
    );
  };

  static getUpdateProfileFlow = (session_token: string) => {
    return this.instance.get<IGetFlowResponse>('/self-service/settings/api', {
      headers: {
        'X-Session-Token': session_token,
      },
    });
  };

  static updateProfile = ({
    flowId,
    referralCode,
    phoneNumber,
    name,
  }: IUpdateProfileParams) => {
    const { isoCode } = useAppStore.getState();
    return this.instance.post<any>(`/self-service/settings?flow=${flowId}`, {
      method: 'profile',
      referralCode,
      traits: {
        role: IRole.ASKER,
        isoCode,
        username: phoneNumber,
        name,
      },
    });
  };

  // Register new account
  static getFlowRegister = () => {
    return this.instance.get<IGetFlowResponse>(
      '/self-service/registration/api',
    );
  };

  static register = ({ flowId, ...body }: IRegisterParams) => {
    const { isoCode } = useAppStore.getState();

    return this.instance.post<IRegisterResponse>(
      `/self-service/registration?flow=${flowId}`,
      {
        traits: {
          username: body.phoneNumber,
          email: body.email,
          name: body.name,
          role: IRole.ASKER,
          isoCode: isoCode,
        },
        referralCode: body.referralCode,
        password: SHA256(body.password).toString(enc.Hex),
        method: 'password',
      },
    );
  };

  static getFlowResendVerificationCode = () => {
    return this.instance.get<IGetFlowResponse>(
      '/self-service/verification/api',
    );
  };
  static resendVerificationCode = async ({
    flowId,
    userName,
  }: {
    flowId: string;
    userName: string;
  }) => {
    return this.instance.post<any>(
      `/self-service/verification?flow=${flowId}`,
      {
        method: 'code',
        userName: userName,
      },
    );
  };
  static verification = ({ code, flowId }: IVerificationParams) => {
    const { isoCode } = useAppStore.getState();
    return this.instance.post<IVerificationResponse>(
      `/self-service/verification?flow=${flowId}`,
      {
        code,
        method: 'code',
        role: IRole.ASKER,
        transient_payload: {
          isoCode,
        },
      },
    );
  };

  static getWhoAmI = ({ token }: { token: string }) => {
    return this.instance.get<IgetWhoAmIResponse>(
      `sessions/whoami?tokenize_as=btaskee-jwt`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      },
    );
  };

  // Sync old token (btaskee-jwt) to ory kratos session (Old app to new app)
  static syncOldToken = async () => {
    const { token } = useAuth.getState();

    const flow = await this.getLoginFlow();
    return this.instance.post(`self-service/login?flow=${flow.id}`, {
      body: {
        method: 'jwt-legacy',
        token,
      },
    });
  };

  // Forgot password
  static getFlowForgotPassword = () => {
    return this.instance.get<IGetFlowResponse>('/self-service/recovery/api');
  };

  static sendRecoveryCode = ({ flowId, ...body }: ISendRecoveryCodeParams) => {
    return this.instance.post<IVerifyRecoveryCodeResponse>(
      `/self-service/recovery?flow=${flowId}`,
      {
        ...body,
        method: 'code',
        role: IRole.ASKER,
      },
    );
  };

  static verifyRecoveryCode = ({ code, flowId }: IVerificationParams) => {
    return this.instance.post<IVerifyRecoveryCodeResponse>(
      `/self-service/recovery?flow=${flowId}`,
      {
        code,
        method: 'code',
      },
    );
  };
  static updatePasswordRecovery = ({
    flowId,
    password,
    session,
  }: {
    flowId: string;
    password: string;
    session: string;
  }) => {
    return this.instance.post<any>(
      `/self-service/settings?flow=${flowId}`,
      {
        password: SHA256(password).toString(enc.Hex),
        method: 'password',
      },
      {
        headers: {
          'X-Session-Token': session,
        },
      },
    );
  };
  static logout = ({ token }: { token: string }) => {
    return this.instance.delete(`/self-service/logout/api`, {
      data: {
        session_token: token,
      },
    });
  };
}

export * from './axios';
export * from './types';
export * from './utils';
