import { find } from 'lodash-es';

import { Alert, getTextWithLocale } from '../../helpers';
import { i18n } from '../../i18n';
import { useAppLoadingStore } from '../../stores';
import { IVerifyRecoveryCodeResponse } from './types';

export class OryUtils {
  static getMessageError = (error: any) => {
    const nodeHaveMessage = error?.ui?.nodes.find((node: any) => {
      return node.messages.length > 0;
    });
    return (
      getTextWithLocale(
        nodeHaveMessage?.messages?.[0] ||
          error?.ui?.messages?.[0] ||
          error?.error,
      ) ||
      error?.message ||
      i18n.t('ERROR_TRY_AGAIN')
    );
  };

  static getSessionToken = (
    data: IVerifyRecoveryCodeResponse['continue_with'],
  ) => {
    const orySession = find(data, {
      action: 'set_ory_session_token',
    });
    return orySession?.ory_session_token;
  };

  static getVerification = (
    data: IVerifyRecoveryCodeResponse['continue_with'],
  ) => {
    const verification = find(data, {
      action: 'show_verification_ui',
    });
    return verification;
  };

  static onError = (error: any) => {
    const { hideLoading } = useAppLoadingStore.getState();
    hideLoading();
    Alert.alert.open({
      title: i18n.t('DIALOG_TITLE_INFORMATION'),
      message: OryUtils.getMessageError(error),
      actions: [
        {
          text: i18n.t('CLOSE'),
          onPress: () => {},
        },
      ],
    });
  };
}
