import axios, {
  AxiosError,
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
} from 'axios';

import { Log } from '../../utils';

// định nghĩa type axios instance: get/post trả về Promise<T>
interface TypedAxiosInstance extends AxiosInstance {
  get<T = any, R = T>(url: string, config?: AxiosRequestConfig): Promise<R>;
  post<T = any, R = T>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig,
  ): Promise<R>;
  patch<T = any, R = T>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig,
  ): Promise<R>;
  delete<T = any, R = T>(url: string, config?: AxiosRequestConfig): Promise<R>;
  put<T = any, R = T>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig,
  ): Promise<R>;
}

export class HttpClient {
  private static instance: TypedAxiosInstance;

  private constructor() {}

  public static getInstance(): TypedAxiosInstance {
    if (!HttpClient.instance) {
      HttpClient.instance = axios.create({
        timeout: 30000,
        withCredentials: false,
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
      }) as TypedAxiosInstance;

      HttpClient.setupInterceptors(HttpClient.instance);
    }
    return HttpClient.instance;
  }

  static setBaseURL = (baseURL: string) => {
    this.instance.defaults.baseURL = baseURL;
  };

  static updateToken = (token: string) => {
    this.instance.defaults.headers.common.Authorization = `Bearer ${token}`;
  };

  static removeToken = () => {
    delete this.instance.defaults.headers.common.Authorization;
  };

  private static setupInterceptors(instance: TypedAxiosInstance) {
    instance.interceptors.response.use(
      (response: AxiosResponse) => {
        let data = response.config.data;

        try {
          data = JSON.parse(data);
        } catch (e) {}
        Log.consoleLog('✅ Request success:', {
          header: response.config.headers,
          baseURL: instance.defaults.baseURL,
          endpoint: response.config.url,
          response: response.data,
          data,
        });

        return response.data;
      },
      (error: AxiosError) => {
        let data = error?.config?.data;
        try {
          data = JSON.parse(data);
        } catch (e) {}
        Log.consoleLog('❌ Request failed:', {
          header: error.config?.headers,
          baseURL: instance.defaults.baseURL,
          endpoint: error.config?.url,
          data,
          error: error.response || error,
        });
        return Promise.reject(error.response?.data);
      },
    );
  }
}
