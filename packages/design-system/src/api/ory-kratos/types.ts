export enum IRole {
  ASKER = 'ASKER',
  TASKER = 'TASKER',
}
export type ISubmitLoginFlowBody = {
  identifier: string;
  password: string;
  flowId: string;
};

export type ILoginSocialBody = {
  provider: 'google' | 'facebook' | 'apple';
  id_token: string;
  flowId: string;
  id_token_nonce?: string;
};
export type ILoginSocialResponse = {
  session_token: string;
  continue_with?: {
    action:
      | 'show_verification_ui'
      | 'set_ory_session_token'
      | 'show_settings_ui';
    flow?: {
      id: string;
      verifiable_address: string;
    };
    ory_session_token?: string;
  }[];
  isNewUser?: boolean;
};

export type IUpdateProfileParams = {
  phoneNumber: string;
  referralCode?: string;
  name?: string;
  flowId: string;
};

export type IGetFlowResponse = {
  id?: string;
  organization_id?: string | null;
  type?: string;
  expires_at?: string;
  issued_at?: string;
  request_url?: string;
  created_at?: string;
  updated_at?: string;
  refresh?: boolean;
  requested_aal?: string;
  state?: string;
};

export type ISubmitLoginFlowResponse = {
  session_token: string;
  session: any;
  continue_with?: {
    action:
      | 'show_verification_ui'
      | 'set_ory_session_token'
      | 'show_settings_ui';
    flow?: {
      id: string;
      verifiable_address: string;
    };
    ory_session_token?: string;
  }[];
};

export type IgetWhoAmIResponse = {
  active: boolean;
  id: string;
  identity: any;
  issued_at: string;
  devices: any[];
  tokenized: string;
};

export type IRegisterParams = {
  name: string;
  email?: string;
  password: string;
  phoneNumber: string;
  referralCode?: string;
  flowId: string;
};

export type IRegisterResponse = {
  session_token: string;
  session?: any;
  identity?: any;
  continue_with?: {
    action:
      | 'show_verification_ui'
      | 'set_ory_session_token'
      | 'show_settings_ui';
    flow?: {
      id: string;
      verifiable_address: string;
    };
    ory_session_token?: string;
  }[];
};

export type IVerificationParams = {
  code: string;
  flowId: string;
};

export type IVerificationResponse = {
  id: string;
};

export type ISendRecoveryCodeParams = {
  userName: string;
  flowId: string;
};

export type IVerifyRecoveryCodeResponse = {
  id: string;
  session_token: string;
  continue_with?: {
    action:
      | 'show_verification_ui'
      | 'set_ory_session_token'
      | 'show_settings_ui';
    flow?: {
      id: string;
      verifiable_address: string;
    };
    ory_session_token?: string;
  }[];
};
