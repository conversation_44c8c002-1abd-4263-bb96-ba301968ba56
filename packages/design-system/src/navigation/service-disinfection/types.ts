import {
  NativeStackNavigationProp,
  NativeStackScreenProps,
} from '@react-navigation/native-stack';

import { DisinfectionRouteName } from './route-name';

export type DisinfectionStackParamList = {
  [DisinfectionRouteName.IntroService]:
    | {
        isHideButton?: boolean;
      }
    | undefined;
  [DisinfectionRouteName.ChooseAddress]: undefined;
  [DisinfectionRouteName.ChooseService]: undefined;
  [DisinfectionRouteName.ChooseDateTime]: undefined;
  [DisinfectionRouteName.ConfirmAndPayment]: undefined;
  [DisinfectionRouteName.ChooseDuration]: undefined;
  [DisinfectionRouteName.NotesForTasker]: undefined;
  [DisinfectionRouteName.PostTaskSuccess]: undefined;
};

export type DisinfectionNavigationProps =
  NativeStackNavigationProp<DisinfectionStackParamList>;

export type DisinfectionStackScreenProps<
  T extends keyof DisinfectionStackParamList,
> = NativeStackScreenProps<DisinfectionStackParamList, T>;
