import {
  NativeStackNavigationProp,
  NativeStackScreenProps,
} from '@react-navigation/native-stack';

import { IAddress } from '../../types';
import { IndustrialCleaningRouteName } from './route-name';

export type IndustrialCleaningStackParamList = {
  [IndustrialCleaningRouteName.IntroService]:
    | {
        isHideButton?: boolean;
      }
    | undefined;
  [IndustrialCleaningRouteName.ChooseAddress]:
    | {
        entryPoint?: boolean;
      }
    | undefined;
  [IndustrialCleaningRouteName.ChooseService]: undefined;
  [IndustrialCleaningRouteName.ChooseDateTime]: undefined;
  [IndustrialCleaningRouteName.ConfirmAndPayment]: undefined;
  [IndustrialCleaningRouteName.PostTaskSuccess]: undefined;
  [IndustrialCleaningRouteName.EditLocation]: { location: IAddress };
  [IndustrialCleaningRouteName.ChooseAddOn]: undefined;
};

export type IndustrialCleaningNavigationProps =
  NativeStackNavigationProp<IndustrialCleaningStackParamList>;

export type IndustrialCleaningScreenProps<
  T extends keyof IndustrialCleaningStackParamList,
> = NativeStackScreenProps<IndustrialCleaningStackParamList, T>;
