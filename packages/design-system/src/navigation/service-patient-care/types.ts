import {
  NativeStackNavigationProp,
  NativeStackScreenProps,
} from '@react-navigation/native-stack';

import { PatientCareRouteName } from './route-name';

export type PatientCareStackParamList = {
  [PatientCareRouteName.ChooseAddress]:
    | {
        entryPoint?: string;
      }
    | undefined;
  [PatientCareRouteName.ChooseService]: undefined;
  [PatientCareRouteName.ChooseDateTime]: undefined;
  [PatientCareRouteName.ConfirmAndPayment]: undefined;
  [PatientCareRouteName.PostTaskSuccess]: undefined;
  [PatientCareRouteName.EditLocation]: undefined;
  [PatientCareRouteName.WorkingProcess]: undefined;
  [PatientCareRouteName.IntroService]:
    | {
        isHideButton?: boolean;
        entryPoint?: string;
      }
    | undefined;
  [PatientCareRouteName.ChooseWorkTime]: undefined;
};

export type PatientCareNavigationProps =
  NativeStackNavigationProp<PatientCareStackParamList>;

export type PatientCareScreenProps<T extends keyof PatientCareStackParamList> =
  NativeStackScreenProps<PatientCareStackParamList, T>;
