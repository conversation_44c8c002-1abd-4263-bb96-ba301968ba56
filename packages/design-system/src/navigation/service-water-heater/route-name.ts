/**
 * Centralized route names for service-water-heater
 * These routes are used throughout the water heater service navigation
 */
export enum WaterHeaterRouteName {
  ChooseAddress = 'WaterHeater/ChooseAddress',
  ChooseService = 'WaterHeater/ChooseService',
  ChooseDateTime = 'WaterHeater/ChooseDateTime',
  ConfirmAndPayment = 'WaterHeater/ConfirmAndPayment',
  PostTaskSuccess = 'WaterHeater/PostTaskSuccess',
  EditLocation = 'WaterHeater/EditLocation',
  WorkingProcess = 'WaterHeater/WorkingProcess',
  IntroService = 'WaterHeater/IntroService',
}
