import type { NativeStackNavigationProp } from '@react-navigation/native-stack';

import { WaterHeaterRouteName } from './route-name';

/**
 * Type definitions for service-water-heater navigation
 */
export type WaterHeaterStackParamList = {
  [WaterHeaterRouteName.ChooseAddress]: { entryPoint?: string } | undefined;
  [WaterHeaterRouteName.ChooseService]: { entryPoint?: string } | undefined;
  [WaterHeaterRouteName.ChooseDateTime]: { entryPoint?: string } | undefined;
  [WaterHeaterRouteName.ConfirmAndPayment]: { entryPoint?: string } | undefined;
  [WaterHeaterRouteName.PostTaskSuccess]: { entryPoint?: string } | undefined;
  [WaterHeaterRouteName.EditLocation]: { entryPoint?: string } | undefined;
  [WaterHeaterRouteName.WorkingProcess]: { entryPoint?: string } | undefined;
  [WaterHeaterRouteName.IntroService]:
    | { isHideButton?: boolean; entryPoint?: string }
    | undefined;
};

export type WaterHeaterNavigationProps =
  NativeStackNavigationProp<WaterHeaterStackParamList>;
