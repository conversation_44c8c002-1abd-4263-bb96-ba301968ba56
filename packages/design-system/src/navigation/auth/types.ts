import { NativeStackScreenProps } from '@react-navigation/native-stack';

import { AuthRouteName } from './route-name';

export type AuthStackParamList = {
  [AuthRouteName.Login]?: {
    phoneNumber?: string;
    password?: string;
  };
  [AuthRouteName.OTP]: {
    phoneNumber: string;
    flowId: string;
    session?: string;
    userName: string;
    verifyFor: 'register' | 'forgot_password';
  };
  [AuthRouteName.Register]?: {
    phoneNumber?: string;
    name?: string;
    email?: string;
    password?: string;
    referralCode?: string;
  };
  [AuthRouteName.ForgotPassword]: undefined;
  [AuthRouteName.InputPassword]: { flowId: string; session: string };
  [AuthRouteName.SocialUpdateProfile]: {
    session_token: string;
    name?: string;
    email?: string;
    picture?: string;
  };
};

export type AuthStackScreenProps<T extends keyof AuthStackParamList> =
  NativeStackScreenProps<AuthStackParamList, T>;
