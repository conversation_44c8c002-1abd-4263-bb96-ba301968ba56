import {
  NativeStackNavigationProp,
  NativeStackScreenProps,
} from '@react-navigation/native-stack';

import { OfficeCleaningSubscriptionRouteName } from './route-name';

export type OfficeCleaningSubscriptionStackParamList = {
  [OfficeCleaningSubscriptionRouteName.ChooseService]?: {
    renewOldSubscription?: undefined;
  };
  [OfficeCleaningSubscriptionRouteName.ConfirmAndPayment]: undefined;
  [OfficeCleaningSubscriptionRouteName.NotesForTasker]: undefined;
  [OfficeCleaningSubscriptionRouteName.ChangeVATInfo]: undefined;
};

export type OfficeCleaningSubscriptionNavigationProps =
  NativeStackNavigationProp<OfficeCleaningSubscriptionStackParamList>;

export type OfficeCleaningSubscriptionStackScreenProps<
  T extends keyof OfficeCleaningSubscriptionStackParamList,
> = NativeStackScreenProps<OfficeCleaningSubscriptionStackParamList, T>;
