import {
  *************************,
  NativeStackScreenProps,
} from '@react-navigation/native-stack';

import { IroningRouteName } from './route-name';

export type IroningStackParamList = {
  [IroningRouteName.Home]: undefined;
  [IroningRouteName.IntroService]:
    | {
        isHideButton?: boolean;
        entryPoint?: string;
      }
    | undefined;
  [IroningRouteName.ChooseAddress]:
    | {
        entryPoint?: string;
      }
    | undefined;
  [IroningRouteName.ChooseService]: undefined;
  [IroningRouteName.ChooseDateTime]: undefined;
  [IroningRouteName.ConfirmAndPayment]: undefined;
  [IroningRouteName.PostTaskSuccess]: undefined;
  [IroningRouteName.ChooseDuration]: undefined;
  [IroningRouteName.NotesForTasker]: undefined;
};

export type IroningNavigationProps =
  *************************<IroningStackParamList>;

export type IroningScreenProps<T extends keyof IroningStackParamList> =
  NativeStackScreenProps<IroningStackParamList, T>;
