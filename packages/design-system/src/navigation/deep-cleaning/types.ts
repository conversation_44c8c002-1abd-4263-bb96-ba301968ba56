import {
  NativeStackNavigationProp,
  NativeStackScreenProps,
} from '@react-navigation/native-stack';

import { DeepCleaningRouteName } from './route-name';

export type DeepCleaningStackParamList = {
  [DeepCleaningRouteName.ChooseAddress]: undefined;
  [DeepCleaningRouteName.ChooseDateTime]: undefined;
  [DeepCleaningRouteName.ConfirmAndPayment]: undefined;
  [DeepCleaningRouteName.ChooseDuration]: undefined;
  [DeepCleaningRouteName.EditLocation]: undefined;
  [DeepCleaningRouteName.WorkingProcess]: undefined;
  [DeepCleaningRouteName.IntroService]?: {
    isHideButton?: boolean;
  };
};

export type DeepCleaningNavigationProps =
  NativeStackNavigationProp<DeepCleaningStackParamList>;

export type DeepCleaningStackScreenProps<
  T extends keyof DeepCleaningStackParamList,
> = NativeStackScreenProps<DeepCleaningStackParamList, T>;
