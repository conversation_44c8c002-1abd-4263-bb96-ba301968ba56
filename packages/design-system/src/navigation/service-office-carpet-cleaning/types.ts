import {
  NativeStackNavigationProp,
  NativeStackScreenProps,
} from '@react-navigation/native-stack';

import { OfficeCarpetCleaningRouteName } from './route-name';

export type OfficeCarpetCleaningStackParamList = {
  [OfficeCarpetCleaningRouteName.ChooseAddress]:
    | {
        entryPoint?: string;
      }
    | undefined;
  [OfficeCarpetCleaningRouteName.ChooseService]: undefined;
  [OfficeCarpetCleaningRouteName.ChooseDateTime]: undefined;
  [OfficeCarpetCleaningRouteName.ConfirmAndPayment]: undefined;
  [OfficeCarpetCleaningRouteName.PostTaskSuccess]: undefined;
  [OfficeCarpetCleaningRouteName.EditLocation]: undefined;
  [OfficeCarpetCleaningRouteName.IntroService]:
    | {
        isHideButton?: boolean;
        entryPoint?: string;
      }
    | undefined;
};

export type OfficeCarpetCleaningNavigationProps =
  NativeStackNavigationProp<OfficeCarpetCleaningStackParamList>;

export type OfficeCarpetCleaningScreenProps<
  T extends keyof OfficeCarpetCleaningStackParamList,
> = NativeStackScreenProps<OfficeCarpetCleaningStackParamList, T>;
