import type { NativeStackNavigationProp } from '@react-navigation/native-stack';

import { WashingMachineRouteName } from './route-name';

/**
 * Type definitions for service-washing-machine navigation
 */
export type WashingMachineStackParamList = {
  [WashingMachineRouteName.ChooseAddress]: { entryPoint?: string } | undefined;
  [WashingMachineRouteName.ChooseService]: { entryPoint?: string } | undefined;
  [WashingMachineRouteName.ChooseDateTime]: { entryPoint?: string } | undefined;
  [WashingMachineRouteName.ConfirmAndPayment]:
    | { entryPoint?: string }
    | undefined;
  [WashingMachineRouteName.PostTaskSuccess]:
    | { entryPoint?: string }
    | undefined;
  [WashingMachineRouteName.EditLocation]: { entryPoint?: string } | undefined;
  [WashingMachineRouteName.WorkingProcess]: { entryPoint?: string } | undefined;
  [WashingMachineRouteName.IntroService]:
    | { isHideButton?: boolean; entryPoint?: string }
    | undefined;
};

export type WashingMachineNavigationProps =
  NativeStackNavigationProp<WashingMachineStackParamList>;
