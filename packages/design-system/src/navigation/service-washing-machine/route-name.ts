/**
 * Centralized route names for service-washing-machine
 * These routes are used throughout the washing machine service navigation
 */
export enum WashingMachineRouteName {
  ChooseAddress = 'WashingMachine/ChooseAddress',
  ChooseService = 'WashingMachine/ChooseService',
  ChooseDateTime = 'WashingMachine/ChooseDateTime',
  ConfirmAndPayment = 'WashingMachine/ConfirmAndPayment',
  PostTaskSuccess = 'WashingMachine/PostTaskSuccess',
  EditLocation = 'WashingMachine/EditLocation',
  WorkingProcess = 'WashingMachine/WorkingProcess',
  IntroService = 'WashingMachine/IntroService',
}
