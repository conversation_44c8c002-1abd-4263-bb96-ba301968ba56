import {
  NativeStackNavigationProp,
  NativeStackScreenProps,
} from '@react-navigation/native-stack';

import { ElderlyCareRouteName } from './route-name';

export type ElderlyCareStackParamList = {
  [ElderlyCareRouteName.IntroService]:
    | {
        isHideButton?: boolean;
      }
    | undefined;
  [ElderlyCareRouteName.ChooseAddress]: undefined;
  [ElderlyCareRouteName.ChooseService]: undefined;
  [ElderlyCareRouteName.ChooseDateTime]: undefined;
  [ElderlyCareRouteName.ConfirmAndPayment]: undefined;
  [ElderlyCareRouteName.ChooseDuration]: undefined;
  [ElderlyCareRouteName.NotesForTasker]: undefined;
  [ElderlyCareRouteName.PostTaskSuccess]: undefined;
  [ElderlyCareRouteName.WorkingProcess]: undefined;
  [ElderlyCareRouteName.ChooseWorkTime]: undefined;
};

export type ElderlyCareNavigationProps =
  NativeStackNavigationProp<ElderlyCareStackParamList>;

export type ElderlyCareStackScreenProps<
  T extends keyof ElderlyCareStackParamList,
> = NativeStackScreenProps<ElderlyCareStackParamList, T>;
