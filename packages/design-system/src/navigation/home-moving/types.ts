import { NativeStackScreenProps } from '@react-navigation/native-stack';

import { HomeMovingRouteName } from './route-name';

export type HomeMovingStackParamList = {
  [HomeMovingRouteName.Home]: undefined;
  [HomeMovingRouteName.IntroService]?: {
    isHideButton?: boolean;
  };
  [HomeMovingRouteName.ChooseAddress]: undefined;
  [HomeMovingRouteName.ChooseService]?: {
    entryPoint?: string;
  };
  [HomeMovingRouteName.ChooseDateTime]: undefined;
  [HomeMovingRouteName.ConfirmAndPayment]: undefined;
  [HomeMovingRouteName.PostTaskSuccess]: undefined;
  [HomeMovingRouteName.ChooseDuration]: undefined;
  [HomeMovingRouteName.NotesForTasker]: undefined;
  [HomeMovingRouteName.WorkingDescriptionProgress]: undefined;
  [HomeMovingRouteName.StandardPackagingProcess]: undefined;
};

export type HomeMovingStackScreenProps<
  T extends keyof HomeMovingStackParamList,
> = NativeStackScreenProps<HomeMovingStackParamList, T>;
