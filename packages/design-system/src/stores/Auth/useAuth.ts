import * as Keychain from 'react-native-keychain';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

import { ApiClient, OryKratosApi } from '../../api';
import { useUserStore } from '../User';
import { AuthState } from './types';

const INITIAL_STATE: Pick<
  AuthState,
  'isAuthenticated' | 'token' | 'session_token'
> = {
  isAuthenticated: false,
  token: null,
  session_token: null,
};

export const useAuth = create<AuthState>()(
  persist(
    (set, get) => ({
      ...INITIAL_STATE,
      setToken: (token: string) => {
        ApiClient.updateToken(token);
        set({ token });
      },
      setSessionToken: (session_token: string) => {
        set({ session_token });
      },
      login: async ({ token, session_token }) => {
        ApiClient.updateToken(token);
        const { getUser, getFinancialAccount } = useUserStore.getState();
        const { onAuthenticationSuccess } = useAuth.getState();
        set({ token, isAuthenticated: true, session_token });
        const user = await getUser();
        getFinancialAccount();
        onAuthenticationSuccess(user!);
      },
      logout: async () => {
        const session_token = get().session_token!;
        if (session_token) {
          await OryKratosApi.logout({
            token: session_token,
          }).catch(() => {});
        }
        const { clearUser } = useUserStore.getState();
        clearUser();
        set({ ...INITIAL_STATE });
      },
      onAuthenticationSuccess: () => {},
      setOnAuthenticationSuccess: (onAuthenticationSuccess) =>
        set({ onAuthenticationSuccess }),
      tempAuth: {
        routeName: null,
        params: {},
      },
      setTempAuth: (tempAuth) => set({ tempAuth }),
      clearTempAuth: () => set({ tempAuth: { routeName: null, params: {} } }),
      rehydrate: async () => {
        try {
        } catch (e) {}
      },
      reset: () => {
        set({ ...INITIAL_STATE });
      },
    }),
    {
      name: 'auth-store',
      storage: createJSONStorage(() => ({
        getItem: async (key) => {
          const credentials = await Keychain.getGenericPassword({
            service: key,
          });
          if (credentials) {
            const parsedData = credentials.password;
            return JSON.parse(parsedData ?? '{}');
          }
          return null;
        },
        setItem: async (key, value) => {
          await Keychain.setGenericPassword(
            'name-auth-store',
            JSON.stringify(value),
            {
              service: key,
            },
          );
        },
        removeItem: async (key) => {
          await Keychain.resetGenericPassword({ service: key });
        },
        version: 1,
      })),
    },
  ),
);
