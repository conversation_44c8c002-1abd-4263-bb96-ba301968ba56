import { AuthRouteName } from '../../navigation';
import { IUser } from '../../types';

export interface AuthState {
  isAuthenticated: boolean;
  token: string | null;
  session_token: string | null;
  login: ({
    token,
    session_token,
  }: {
    token: string;
    session_token: string;
  }) => Promise<void>;
  logout: () => Promise<void>;
  onAuthenticationSuccess: (user: IUser) => void;
  setOnAuthenticationSuccess: (
    onAuthenticationSuccess: (user: IUser) => void,
  ) => void;
  setToken: (token: string) => void;
  setSessionToken: (session_token: string) => void;
  tempAuth: {
    routeName: AuthRouteName | null;
    params: Record<string, string>;
  };
  setTempAuth: (tempAuth: {
    routeName: AuthRouteName;
    params: Record<string, string>;
  }) => void;
  clearTempAuth: () => void;
  reset: () => void;
}
