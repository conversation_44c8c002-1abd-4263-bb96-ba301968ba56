import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

import { AppStorage } from '../../utils';

interface IState {
  messageUnread: number;
}

interface IAction {
  onInCreaseMessageUnread: () => void;
  resetMessageUnread: () => void;
}

interface IAgentStore extends IState, IAction {}

export const useAgentStore = create<IAgentStore>()(
  persist(
    (set) => ({
      messageUnread: 0,
      onInCreaseMessageUnread: () =>
        set((state) => ({ messageUnread: state.messageUnread + 1 })),
      resetMessageUnread: () => set({ messageUnread: 0 }),
    }),
    {
      name: 'agent-storage',
      storage: createJSONStorage(() => AppStorage.zustandStorage),
      version: 1,
    },
  ),
);
