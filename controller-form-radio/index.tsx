import * as React from 'react';
import { FieldPath, FieldValues, useController } from 'react-hook-form';

import { FormRadio } from '@components/common/form-radio';

import { ControllerFormRadioProps } from './type';

export const ControllerFormRadio: <
  T,
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>(
  props: ControllerFormRadioProps<TFieldValues, TName, T>,
) => React.ReactNode = ({ name, control, data, defaultValue, onPressItem, ...props }) => {
  const {
    field: { onChange },
  } = useController({ name, control, defaultValue });

  return (
    <FormRadio
      onPressItem={(item) => {
        onChange(item);
        onPressItem?.(item);
      }}
      data={data}
      {...props}
    />
  );
};
