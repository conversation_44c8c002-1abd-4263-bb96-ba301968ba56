import React, { useRef, useState } from 'react';
import { StyleSheet } from 'react-native';
import PagerView, { PagerViewProps } from 'react-native-pager-view';
import moment from 'moment';

import { EndPointApi } from '@api/end-point.api';
import { Api } from '@api/index';
import { ApiParamsList } from '@api/type';
import { BaseScreen } from '@components/common/base-screen';
import { BottomButtons } from '@components/common/bottom-buttons';
import { Box } from '@components/common/box';
import { ConditionView } from '@components/common/condition-view';
import { SizedBox } from '@components/common/sized-box';
import { Toolbar } from '@components/common/toolbar';
import { ScrollView } from '@components/customs/scroll-view';
import { Text } from '@components/customs/text';
import { TouchableOpacity } from '@components/customs/touchable-opacity';
import { View } from '@components/customs/view';
import { TypeMedicalRegister } from '@constants/index';
import { AlertHelpers } from '@helpers/alert.helpers';
import { DateTimeHelpers, FormatDate } from '@helpers/date-time.helpers';
import { useAppNavigation } from '@hooks/customs/useAppNavigation';
import { useAppLoading } from '@hooks/useAppLoading';
import { useConstants } from '@hooks/useConstant';
import { useTranslation } from '@hooks/useTranslations';
import { ErrorModel } from '@models/error.model';
import { UserProfileModel } from '@models/user-profile.model';
import { RouteName } from '@routes/route-name';
import { COLORS } from '@theme/colors';
import { SIZES } from '@theme/sizes';

import { HealthInformationPage } from './components/health-information-page';
import { HealthInformationPageHandle } from './components/health-information-page/type';
import { MedicalInformationPage } from './components/medical-information-page';
import { MedicalInformationPageHandle } from './components/medical-information-page/type';
import { UserProfilePage } from './components/user-profile-page';
import { UserProfilePageHandle } from './components/user-profile-page/type';
import { MedicalRegisterScreenProps } from './type';

export const MedicalRegisterScreen = ({ route }: MedicalRegisterScreenProps) => {
  const medicalInformation = route.params?.medicalInformation;

  const navigation = useAppNavigation();
  const { t } = useTranslation();
  const { typeMedicalRegisterList } = useConstants();
  const { showAppLoading, hideAppLoading } = useAppLoading();

  const [pageActive, setPageActive] = useState(0);
  const [type, setType] = useState(route.params?.type || TypeMedicalRegister.Medical);

  const pagerViewRef = useRef<PagerView>(null);
  const userProfilePageRef = useRef<UserProfilePageHandle>(null);
  const medicalInformationPagePageRef = useRef<MedicalInformationPageHandle>(null);
  const healthInformationPagePageRef = useRef<HealthInformationPageHandle>(null);

  const onChangePage: PagerViewProps['onPageSelected'] = (e) => {
    const { position } = e.nativeEvent;
    setPageActive(position);
  };

  const onNext = () => {
    userProfilePageRef.current?.handleSubmit(async (userProfile) => {
      if (pageActive === 0) {
        pagerViewRef.current?.setPage(1);
      } else {
        const patientInfo: UserProfileModel = {
          patientId: userProfile.patientId,
          fileNum: userProfile.fileNum,
          fullName: userProfile.fullName,
          gender: userProfile.gender,
          dob: userProfile.dob,
          mobile: userProfile.mobile,
          city: userProfile.address?.city?.name,
          cityId: userProfile.address?.city?.id,
          district: userProfile.address?.district?.name,
          districtId: userProfile.address?.district?.id,
          ward: userProfile.address?.ward?.name,
          wardId: userProfile.address?.ward?.id,
          street: userProfile.address?.street,
          addressNo: userProfile.address?.addressNo,
          email: userProfile.email,
        };
        const dob = moment(patientInfo.dob, FormatDate.client).format(FormatDate.server);

        let params: ApiParamsList[EndPointApi.CreateAPVisit] = {
          patientInfo: {
            ...patientInfo,
            dob,
          },
          amount: 0,
          visitEndTime: '',
          visitStartTime: '',
          email: userProfile.email,
        };

        if (type === TypeMedicalRegister.Medical) {
          medicalInformationPagePageRef.current?.handleSubmit(async (data) => {
            try {
              showAppLoading();
              const visitDate = moment(data.doctorSchedule?.scheduleDate).format(FormatDate.server);
              const visitStartTime = `${visitDate} ${data.doctorSchedule?.scheduleInfo?.startTime}`;
              const visitEndTime = `${visitDate} ${data.doctorSchedule?.scheduleInfo?.endTime}`;
              params = {
                ...params,
                amount: data.doctorSchedule?.doctor?.price || 0,
                visitEndTime,
                visitStartTime,
                roomIdDoctorId: data.doctorSchedule?.doctor?.contactId,
                roomDoctorName: `${data.doctorSchedule?.doctor?.qualification} ${data.doctorSchedule?.doctor?.fullName}`,
                specialityId: data.specialist?.id,
                specialityText: data.specialist?.caption,
                notes: data.notes,
              };
              const resCreateAPVisit = await Api.createAPVisit(params);
              navigation.navigate(RouteName.HistoryVisit);
              navigation.navigate(RouteName.ConfirmAndPayment, {
                userProfile: patientInfo,
                medicalInfo: data,
                type,
                patientVisitId: resCreateAPVisit.data as number,
              });
            } catch (error) {
              if (ErrorModel.isModel(error)) {
                AlertHelpers.showFailed({ message: error.description });
              }
            } finally {
              hideAppLoading();
            }
          })();
        } else if (type === TypeMedicalRegister.Health) {
          healthInformationPagePageRef.current?.handleSubmit(async (data) => {
            try {
              showAppLoading();
              const visitDate = moment(data.visitDate, FormatDate.client).format(FormatDate.server);
              const startTime = DateTimeHelpers.formatTime(data.visitTime?.startTime);
              const endTime = DateTimeHelpers.formatTime(data.visitTime?.endTime);
              const visitStartTime = `${visitDate} ${startTime}`;
              const visitEndTime = `${visitDate} ${endTime}`;
              params = {
                ...params,
                amount: data.servicePack?.price || 0,
                visitEndTime,
                visitStartTime,
                serviceId: data.servicePack?.hisId as number,
                serviceName: data.servicePack?.sPName as string,
                notes: data.notes,
              };
              const resCreateAPVisit = await Api.createAPVisit(params);
              navigation.navigate(RouteName.HistoryVisit);
              navigation.navigate(RouteName.ConfirmAndPayment, {
                userProfile: patientInfo,
                healthInfo: data,
                type,
                patientVisitId: resCreateAPVisit.data as number,
              });
            } catch (error) {
              if (ErrorModel.isModel(error)) {
                AlertHelpers.showFailed({ message: error.description });
              }
            } finally {
              hideAppLoading();
            }
          })();
        }
      }
    })();
  };

  return (
    <BaseScreen>
      <Toolbar title={t('BOOK_EXAMINATION')} />
      <PagerView
        style={styles.pagerView}
        initialPage={pageActive}
        ref={pagerViewRef}
        onPageSelected={onChangePage}
        scrollEnabled={false}
      >
        <UserProfilePage ref={userProfilePageRef} key={'1'} />
        <View>
          <ScrollView>
            <SizedBox height={SIZES.VIEW.PADDING_TOP} />
            <Box paddingVertical={10}>
              <View flexDirection="row" justifyContent="space-between" style={styles.typeContainer}>
                {typeMedicalRegisterList.map((item) => {
                  const isActive = item.key === type;
                  const title =
                    item.key === TypeMedicalRegister.Medical
                      ? t('CHOOSE_DOCTOR')
                      : t('CHOOSE_PACKAGE');
                  return (
                    <TouchableOpacity
                      key={item.key}
                      style={[
                        styles.typeBtn,
                        {
                          backgroundColor: isActive ? COLORS.PRIMARY : COLORS.WHITE,
                        },
                      ]}
                      onPress={() => setType(item.key)}
                    >
                      <Text type="bold14" color={isActive ? COLORS.TEXT_WHITE : COLORS.TEXT}>
                        {title}
                      </Text>
                    </TouchableOpacity>
                  );
                })}
              </View>
            </Box>
            <ConditionView
              key={'2'}
              condition={type === TypeMedicalRegister.Medical}
              viewTrue={
                <MedicalInformationPage
                  ref={medicalInformationPagePageRef}
                  defaultValue={medicalInformation}
                />
              }
              viewFalse={<HealthInformationPage ref={healthInformationPagePageRef} />}
            />
          </ScrollView>
        </View>
      </PagerView>
      <BottomButtons
        leftProps={
          pageActive === 1
            ? {
                title: t('BACK'),
                onPress: () => {
                  pagerViewRef.current?.setPage(0);
                },
              }
            : null
        }
        rightProps={{
          title: pageActive === 1 ? t('REGISTER') : t('NEXT'),
          onPress: onNext,
        }}
      />
    </BaseScreen>
  );
};

const styles = StyleSheet.create({
  pagerView: {
    flex: 1,
  },
  typeBtn: {
    flex: 1,
    alignItems: 'center',
    padding: 10,
  },
  typeContainer: {
    borderWidth: 1,
    borderRadius: 8,
    overflow: 'hidden',
    borderColor: COLORS.BORDER,
  },
});
