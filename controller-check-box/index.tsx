import * as React from 'react';
import { FieldPath, FieldValues, useController } from 'react-hook-form';

import { Checkbox } from '@components/customs/checkbox';

import { ControllerFormCheckboxProps } from './type';

export const ControllerFormCheckbox: <
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>(
  props: ControllerFormCheckboxProps<TFieldValues, TName>,
) => React.ReactNode = ({ name, control, defaultValue, ...props }) => {
  const {
    field: { onChange, value },
  } = useController({ name, control, defaultValue });

  return (
    <Checkbox
      onPress={() => {
        onChange(!value);
      }}
      checked={!!value}
      {...props}
    />
  );
};
