import * as React from 'react';
import { FieldPath, FieldValues, useController } from 'react-hook-form';

import { TextInput } from '../../customs/text-input';
import { ControllerTextInputProps } from './type';

export const ControllerTextInput: <
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>(
  props: ControllerTextInputProps<TFieldValues, TName>,
) => React.ReactNode = ({ name, control, defaultValue, onChangeText, ...props }) => {
  const {
    field: { onChange, value },
  } = useController({ name, control, defaultValue });

  const onChangeTextInput = (text: string) => {
    onChange(text);
    if (onChangeText) {
      onChangeText(text);
    }
  };

  return <TextInput value={value} onChangeText={onChangeTextInput} {...props} />;
};
